{"learner": {"attributes": {"scikit_learn": "{\"_estimator_type\": \"regressor\"}"}, "feature_names": ["Global Region", "Overall Risk Assessment", "Security Risk Assessment", "Political Risk Assessment", "Economic Risk Assessment", "Legal Risk Assessment", "Tax Risk Assessment", "Operational Risk Assessment", "Development Stage", "Activity Status", "Primary Commodity", "Commodities", "Mine Type", "Geologic Ore Body Type", "Ore Minerals", "Processing Method", "Production Forms", "Grade Reserves & Resources", "Primary Reserves & Resources", "In-Situ Value", "Total Value", "Mill Capacity", "Stripping Ratio", "MillHead Grade", "Recovery Rate", "Production Capacity", "Estimated Mine Life", "Initial Capital Cost", "Mining & Processing Costs", "Mine Sustaining Cost"], "feature_types": ["int", "int", "int", "int", "int", "int", "int", "int", "int", "int", "int", "int", "int", "int", "int", "int", "int", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float", "float"], "gradient_booster": {"model": {"gbtree_model_param": {"num_parallel_tree": "1", "num_trees": "300"}, "iteration_indptr": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 203, 204, 205, 206, 207, 208, 209, 210, 211, 212, 213, 214, 215, 216, 217, 218, 219, 220, 221, 222, 223, 224, 225, 226, 227, 228, 229, 230, 231, 232, 233, 234, 235, 236, 237, 238, 239, 240, 241, 242, 243, 244, 245, 246, 247, 248, 249, 250, 251, 252, 253, 254, 255, 256, 257, 258, 259, 260, 261, 262, 263, 264, 265, 266, 267, 268, 269, 270, 271, 272, 273, 274, 275, 276, 277, 278, 279, 280, 281, 282, 283, 284, 285, 286, 287, 288, 289, 290, 291, 292, 293, 294, 295, 296, 297, 298, 299, 300], "tree_info": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "trees": [{"base_weights": [58.00924, -649.2967, 10425.3955, -940.9995, 1379.334, 45.006454, 142.65045, -1150.4657, -132.51973, 835.4505, 25.175442, -12.449476, -6.9287066, -6.06194, 0.85358834, -0.3723577, 11.0099], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 0, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, 15, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [1033378100.0, 79409860.0, 140536960.0, 19491928.0, 8420296.0, 0.0, 0.0, 3114792.0, 2631883.0, 3053627.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, 16, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.1965036, 0.04001509, 0.49706092, -0.18306975, 1.9500875, 45.006454, 142.65045, -0.28752968, -0.22261187, 0.6948394, 25.175442, -12.449476, -6.9287066, -6.06194, 0.85358834, -0.3723577, 11.0099], "split_indices": [29, 20, 21, 29, 27, 0, 0, 27, 20, 26, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [139.0, 131.0, 8.0, 115.0, 16.0, 4.0, 4.0, 91.0, 24.0, 12.0, 4.0, 74.0, 17.0, 7.0, 17.0, 3.0, 9.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "17", "size_leaf_vector": "1"}}, {"base_weights": [-20.562447, -655.3107, 7136.6426, -1117.1543, 652.1079, 94.26658, 36.57169, -12.192708, -629.59875, 296.05618, 25.86766, -7.67569, -2.257134, -2.4558506, 5.52079], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 1, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, 11, 13, -1, -1, -1, -1, -1], "loss_changes": [613441300.0, 75602590.0, 53172350.0, 3812616.0, 21965548.0, 0.0, 0.0, 0.0, 829686.0, 4157116.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 8, 8, 9, 9], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, 12, 14, -1, -1, -1, -1, -1], "split_conditions": [1.1965036, -0.20835175, -0.9297316, -0.28752968, 1.5831932, 94.26658, 36.57169, -12.192708, -0.20714763, 0.4472843, 25.86766, -7.67569, -2.257134, -2.4558506, 5.52079], "split_indices": [29, 20, 22, 27, 27, 0, 0, 0, 29, 26, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [133.0, 123.0, 10.0, 91.0, 32.0, 5.0, 5.0, 74.0, 17.0, 28.0, 4.0, 12.0, 5.0, 9.0, 19.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [65.22279, -489.70526, 10044.624, -909.8519, 1654.3497, 125.12536, 50.655315, -1153.8025, -28.27486, 857.4922, 26.215384, -12.378933, -7.5450907, -5.1810727, 2.4901679, 2.4773386, 11.009587], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 2, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, 15, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [735916000.0, 115008150.0, 22631296.0, 22763744.0, 13936648.0, 0.0, 0.0, 1852680.0, 3388879.2, 1657045.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, 16, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [2.8577383, 0.092575856, 0.26727843, -0.18306975, 1.9500875, 125.12536, 50.655315, -0.2967835, -0.22261187, -0.56870174, 26.215384, -12.378933, -7.5450907, -5.1810727, 2.4901679, 2.4773386, 11.009587], "split_indices": [20, 20, 17, 29, 27, 0, 0, 27, 20, 17, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [131.0, 125.0, 6.0, 105.0, 20.0, 3.0, 3.0, 82.0, 23.0, 12.0, 8.0, 66.0, 16.0, 8.0, 15.0, 4.0, 8.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "17", "size_leaf_vector": "1"}}, {"base_weights": [191.9677, -597.90094, 7456.338, -1067.1472, 799.514, 14.346354, 8968.189, -12.328964, -789.0331, 254.12888, 2225.6267, 145.2672, 5067.888, -8.61608, -1.231813, -0.42604682, 5.791489, 11.83264, 25.495806, 23.21463, 72.339226], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 3, "left_children": [1, 3, 5, 7, 9, -1, 11, -1, 13, 15, 17, -1, 19, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [819240400.0, 85442620.0, 114587780.0, 3498432.0, 25100914.0, 0.0, 164857730.0, 0.0, 1778414.0, 2435837.0, 21900.0, 0.0, 30796576.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 6, 6, 8, 8, 9, 9, 10, 10, 12, 12], "right_children": [2, 4, 6, 8, 10, -1, 12, -1, 14, 16, 18, -1, 20, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [0.6820698, 0.0526399, 0.17150046, -0.44001678, 1.455611, 14.346354, 0.4095176, -12.328964, 0.8186169, -0.031931423, 0.3882633, 145.2672, 1.0, -8.61608, -1.231813, -0.42604682, 5.791489, 11.83264, 25.495806, 23.21463, 72.339226], "split_indices": [18, 27, 24, 27, 27, 0, 24, 0, 26, 20, 20, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [141.0, 128.0, 13.0, 96.0, 32.0, 3.0, 10.0, 58.0, 38.0, 24.0, 8.0, 3.0, 7.0, 34.0, 4.0, 13.0, 11.0, 3.0, 5.0, 4.0, 3.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "21", "size_leaf_vector": "1"}}, {"base_weights": [29.055645, -765.8735, 4399.8496, -1020.5957, 426.76266, 2455.353, 8215.85, -12.07967, -683.09344, -5.6436577, 1051.9878, 941.84766, 3527.8633, 110.68503, 33.092083, -8.1553955, -0.8446568, 1.8349713, -2.210126, 5.4945316, 12.116628, 2.3769689, 15.865492, 40.474354, 18.665138], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 4, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, 15, 17, 19, 21, 23, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [496745100.0, 37194304.0, 143070500.0, 5498480.0, 5953770.0, 22648840.0, 61349184.0, 0.0, 2943890.0, 610849.6, 56252.0, 3254436.5, 213528.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 8, 8, 9, 9, 10, 10, 11, 11, 12, 12], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, 16, 18, 20, 22, 24, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [0.3882633, 0.0526399, 2.8455322, -0.4199511, -0.031931423, 1.9500875, 1.6779041, -12.07967, 0.4472843, -0.3972186, 0.570119, -0.56870174, 2.0008802, 110.68503, 33.092083, -8.1553955, -0.8446568, 1.8349713, -2.210126, 5.4945316, 12.116628, 2.3769689, 15.865492, 40.474354, 18.665138], "split_indices": [20, 27, 25, 27, 20, 27, 27, 0, 26, 17, 25, 17, 21, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [141.0, 120.0, 21.0, 99.0, 21.0, 15.0, 6.0, 62.0, 37.0, 13.0, 8.0, 7.0, 8.0, 3.0, 3.0, 30.0, 7.0, 7.0, 6.0, 3.0, 5.0, 4.0, 3.0, 5.0, 3.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "25", "size_leaf_vector": "1"}}, {"base_weights": [30.393087, -547.72235, 10360.42, -930.9823, 1855.8702, 34.377373, 138.26463, -1112.6415, -84.881424, 818.53955, 28.113369, -11.9229965, -7.019028, -4.8573923, 2.608276, 9.356257, 3.7970722], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 5, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, 15, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [901467900.0, 133459390.0, 144421000.0, 18964488.0, 17521144.0, 0.0, 0.0, 2500240.0, 3314060.2, 209798.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, 16, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, 0.04001509, 0.49706092, -0.1673151, 1.5831932, 34.377373, 138.26463, -0.28752968, 0.0526399, -0.58405066, 28.113369, -11.9229965, -7.019028, -4.8573923, 2.608276, 9.356257, 3.7970722], "split_indices": [29, 20, 21, 29, 27, 0, 0, 27, 27, 23, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [149.0, 142.0, 7.0, 123.0, 19.0, 3.0, 4.0, 101.0, 22.0, 10.0, 9.0, 83.0, 18.0, 10.0, 12.0, 7.0, 3.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "17", "size_leaf_vector": "1"}}, {"base_weights": [-61.259136, -557.72577, 6349.277, -1075.5735, 621.372, 100.89749, 33.048103, -11.96621, -712.9807, 153.1537, 2217.096, -7.898718, -1.1180946, 3.3664234, -5.5024033, 25.786947, 11.203984], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 6, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, 11, 13, 15, -1, -1, -1, -1, -1, -1], "loss_changes": [442741760.0, 79434880.0, 80531390.0, 3096832.0, 29546092.0, 0.0, 0.0, 0.0, 1067212.0, 4315287.0, 679544.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 8, 8, 9, 9, 10, 10], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, 12, 14, 16, -1, -1, -1, -1, -1, -1], "split_conditions": [1.1965036, -0.18306975, 1.0741702, -0.33963278, 1.455611, 100.89749, 33.048103, -11.96621, -0.10746456, -0.30668214, 5.0, -7.898718, -1.1180946, 3.3664234, -5.5024033, 25.786947, 11.203984], "split_indices": [29, 29, 26, 27, 27, 0, 0, 0, 18, 28, 11, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [137.0, 128.0, 9.0, 89.0, 39.0, 3.0, 6.0, 65.0, 24.0, 31.0, 8.0, 21.0, 3.0, 25.0, 6.0, 5.0, 3.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "17", "size_leaf_vector": "1"}}, {"base_weights": [139.37993, -541.6562, 9961.093, -882.7406, 1777.472, 42.67674, 136.62273, -1056.8424, -11.614855, 9.165058, 24.409304, -11.53055, -7.055178, -8.17656, 1.7871923], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 7, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, -1, -1, -1, -1, -1, -1], "loss_changes": [928470300.0, 104401380.0, 131343360.0, 17277384.0, 7473200.0, 0.0, 0.0, 2397480.0, 3214537.5, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, 0.092575856, 0.49706092, -0.21593031, 1.5372862, 42.67674, 136.62273, -0.33963278, -0.4363957, 9.165058, 24.409304, -11.53055, -7.055178, -8.17656, 1.7871923], "split_indices": [29, 20, 21, 20, 27, 0, 0, 27, 27, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [137.0, 129.0, 8.0, 113.0, 16.0, 4.0, 4.0, 94.0, 19.0, 8.0, 8.0, 72.0, 22.0, 3.0, 16.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [123.46706, -534.5778, 10801.275, -896.4271, 1626.5592, 46.61308, 135.52974, -1120.973, -300.0289, 727.9772, 27.710999, -11.695052, -7.0580363, -5.333541, 0.82004493, 0.3681133, 9.542645], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 8, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, 15, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [968402500.0, 103118800.0, 71986300.0, 14662432.0, 17523160.0, 0.0, 0.0, 806448.0, 2896199.8, 1841598.5, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, 16, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, 0.092575856, 0.49706092, -0.28688815, 1.9500875, 46.61308, 135.52974, -0.2967835, -0.11083611, 0.8186169, 27.710999, -11.695052, -7.0580363, -5.333541, 0.82004493, 0.3681133, 9.542645], "split_indices": [29, 20, 21, 20, 27, 0, 0, 27, 29, 26, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [136.0, 129.0, 7.0, 111.0, 18.0, 3.0, 4.0, 80.0, 31.0, 11.0, 7.0, 70.0, 10.0, 19.0, 12.0, 3.0, 8.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "17", "size_leaf_vector": "1"}}, {"base_weights": [123.18228, -508.83588, 9800.451, -872.4834, 1963.9467, 41.96242, 134.44551, -1056.1799, -86.03164, 8.049655, 26.282925, -11.490888, -6.093911, 1.4377439, -4.8502774], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 9, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, -1, -1, -1, -1, -1, -1], "loss_changes": [897954400.0, 125805940.0, 127382270.0, 17389696.0, 11743320.0, 0.0, 0.0, 3367624.0, 2270369.5, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, 0.20579594, 0.49706092, -0.19222529, 1.5831932, 41.96242, 134.44551, -0.28752968, 0.0222285, 8.049655, 26.282925, -11.490888, -6.093911, 1.4377439, -4.8502774], "split_indices": [29, 20, 21, 29, 27, 0, 0, 27, 23, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [145.0, 137.0, 8.0, 120.0, 17.0, 4.0, 4.0, 97.0, 23.0, 7.0, 10.0, 79.0, 18.0, 15.0, 8.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [101.05229, -518.1614, 9722.048, -851.7206, 1525.775, 41.626724, 133.36993, -1052.2848, -39.458675, 2141.2842, 827.50525, -10.8153, -2.978472, -4.716531, 1.9166535, 8.959832, 25.469727, 3.1163263, 13.842059], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 10, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, 15, 17, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [886741400.0, 96747810.0, 125352320.0, 19670560.0, 6823612.0, 0.0, 0.0, 1817912.0, 2587690.2, 2769656.0, 2727519.5, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9, 10, 10], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, 16, 18, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, 0.04001509, 0.49706092, -0.19222529, 2.0, 41.626724, 133.36993, 0.64192426, -0.22261187, -0.94184685, 0.17150046, -10.8153, -2.978472, -4.716531, 1.9166535, 8.959832, 25.469727, 3.1163263, 13.842059], "split_indices": [29, 20, 21, 29, 8, 0, 0, 21, 20, 22, 24, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [147.0, 139.0, 8.0, 120.0, 19.0, 4.0, 4.0, 96.0, 24.0, 9.0, 10.0, 92.0, 4.0, 8.0, 16.0, 3.0, 6.0, 6.0, 4.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "19", "size_leaf_vector": "1"}}, {"base_weights": [11.449859, -519.4522, 114.23953, -854.8779, 1467.4742, -1031.1592, -1.2348773, 768.3438, 25.562057, -11.037511, -6.7140102, -4.8004375, 1.4846383, 0.2360238, 9.89396], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 11, "left_children": [1, 3, -1, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [817830800.0, 87228590.0, 0.0, 16702048.0, 12497768.0, 1661400.0, 1504843.8, 2116694.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7], "right_children": [2, 4, -1, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2220678, 0.092575856, 114.23953, 0.02337902, 1.9500875, -0.28752968, -0.20922844, 0.8186169, 25.562057, -11.037511, -6.7140102, -4.8004375, 1.4846383, 0.2360238, 9.89396], "split_indices": [29, 20, 0, 27, 27, 27, 29, 26, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [133.0, 128.0, 5.0, 110.0, 18.0, 91.0, 19.0, 12.0, 6.0, 74.0, 17.0, 4.0, 15.0, 3.0, 9.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-146.38103, -545.61975, 6708.8677, -871.9577, 1407.0499, 19.0991, 92.0624, -1025.4377, -18.270008, 924.0422, 23.800442, -11.051812, -6.5119977, -5.8996863, 2.2804153, 1.1599545, 12.073232], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 12, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, 15, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [397770720.0, 88527380.0, 78294080.0, 15442000.0, 7199688.0, 0.0, 0.0, 2264432.0, 2810077.0, 3293388.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, 16, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2432504, 0.04001509, -0.37959817, -0.20835175, 1.9500875, 19.0991, 92.0624, -0.28752968, -0.20922844, 2.0, 23.800442, -11.051812, -6.5119977, -5.8996863, 2.2804153, 1.1599545, 12.073232], "split_indices": [29, 20, 23, 20, 27, 0, 0, 27, 29, 11, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [143.0, 136.0, 7.0, 117.0, 19.0, 3.0, 4.0, 99.0, 18.0, 14.0, 5.0, 80.0, 19.0, 5.0, 13.0, 4.0, 10.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "17", "size_leaf_vector": "1"}}, {"base_weights": [138.99356, -466.34738, 116.20628, -940.04565, 886.8634, -1042.2063, -184.0359, 447.6882, 23.131062, -11.032376, -6.757, -5.3905454, 7.4949474, 2.5893912, 10.965949], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 13, "left_children": [1, 3, -1, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [971512640.0, 86088520.0, 0.0, 7454280.0, 20887140.0, 1200416.0, 4712468.0, 3145369.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7], "right_children": [2, 4, -1, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, 0.0526399, 116.20628, -0.18306975, 1.9500875, -0.2967835, 0.6479089, 0.858101, 23.131062, -11.032376, -6.757, -5.3905454, 7.4949474, 2.5893912, 10.965949], "split_indices": [29, 27, 0, 29, 27, 27, 22, 25, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [138.0, 132.0, 6.0, 98.0, 34.0, 86.0, 12.0, 27.0, 7.0, 72.0, 14.0, 9.0, 3.0, 22.0, 5.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [50.306152, -445.0147, 91.004295, -696.5212, 2957.8396, -971.91315, 201.15074, 14.364436, 34.791134, -10.579304, -5.30188, -3.0352066, 5.2642884], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 14, "left_children": [1, 3, -1, 5, 7, 9, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [604734460.0, 111607470.0, 0.0, 29860976.0, 2139536.0, 2934568.0, 4920402.5, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, -1, 6, 8, 10, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [1.103419, 1.9500875, 91.004295, -0.20835175, 0.8139121, -0.2685201, 0.4472843, 14.364436, 34.791134, -10.579304, -5.30188, -3.0352066, 5.2642884], "split_indices": [25, 27, 0, 20, 20, 27, 26, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [133.0, 127.0, 6.0, 119.0, 8.0, 91.0, 28.0, 3.0, 5.0, 75.0, 16.0, 11.0, 17.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [-7.0374975, -540.53046, 6298.51, -933.8353, 693.145, 94.047905, 3958.0432, -10.651234, -453.7, 258.78204, 22.074581, 54.571514, 19.67128, -5.9296503, 3.560251, 0.20277414, 7.070266], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 15, "left_children": [1, 3, 5, 7, 9, -1, 11, -1, 13, 15, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [474359070.0, 63724690.0, 42745696.0, 5757168.0, 20476872.0, 0.0, 13141120.0, 0.0, 2804761.0, 2765208.8, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 6, 6, 8, 8, 9, 9], "right_children": [2, 4, 6, 8, 10, -1, 12, -1, 14, 16, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.1965036, -0.11083611, 1.0741702, -0.2967835, 1.9500875, 94.047905, 2.0, -10.651234, -0.19460368, 0.9795277, 22.074581, 54.571514, 19.67128, -5.9296503, 3.560251, 0.20277414, 7.070266], "split_indices": [29, 29, 26, 27, 27, 0, 0, 0, 29, 26, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [139.0, 129.0, 10.0, 98.0, 31.0, 3.0, 7.0, 76.0, 22.0, 25.0, 6.0, 3.0, 4.0, 19.0, 3.0, 17.0, 8.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "17", "size_leaf_vector": "1"}}, {"base_weights": [171.88382, -489.2294, 9261.468, -802.84485, 1677.8464, 119.27118, 29.475996, -1001.227, 76.14352, 707.4048, 25.598501, -10.845878, -6.593979, -1.6518095, 5.82404, 1.7518249, 10.98126], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 16, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, 15, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [797752060.0, 85620900.0, 116317250.0, 19083264.0, 11883752.0, 0.0, 0.0, 1820720.0, 2661877.8, 1679055.5, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, 16, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, 0.092575856, -0.9297316, -0.18306975, 1.9500875, 119.27118, 29.475996, -0.33963278, 1.0, -0.56870174, 25.598501, -10.845878, -6.593979, -1.6518095, 5.82404, 1.7518249, 10.98126], "split_indices": [29, 20, 22, 29, 27, 0, 0, 27, 1, 17, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [131.0, 123.0, 8.0, 108.0, 15.0, 5.0, 3.0, 88.0, 20.0, 8.0, 7.0, 69.0, 19.0, 14.0, 6.0, 4.0, 4.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "17", "size_leaf_vector": "1"}}, {"base_weights": [46.427094, -425.45853, 9005.636, -839.21423, 1395.5308, 44.561996, 113.03638, -1038.8943, -252.54527, 707.6509, 23.565754, -10.5448885, -4.7505174, -5.9930735, 2.2033174, 0.89835835, 10.693463], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 17, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, 15, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [591485760.0, 101379230.0, 22809408.0, 12506432.0, 14859140.0, 0.0, 0.0, 211128.0, 4887383.5, 3479191.5, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, 16, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [2.8577383, -0.009964261, 2.6527653, -0.2967835, 1.9500875, 44.561996, 113.03638, -0.15545082, -0.20835175, 0.6948394, 23.565754, -10.5448885, -4.7505174, -5.9930735, 2.2033174, 0.89835835, 10.693463], "split_indices": [20, 20, 21, 27, 27, 0, 0, 18, 20, 26, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [138.0, 132.0, 6.0, 108.0, 24.0, 3.0, 3.0, 80.0, 28.0, 15.0, 9.0, 77.0, 3.0, 16.0, 12.0, 6.0, 9.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "17", "size_leaf_vector": "1"}}, {"base_weights": [-90.764465, -451.80685, 80.478004, -740.67444, 1596.8372, -910.39355, 94.69548, 927.1925, 24.210018, -10.151111, -5.2023087, -2.4015923, 2.7928421, 0.7344897, 12.381527], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 18, "left_children": [1, 3, -1, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [415006660.0, 81073870.0, 0.0, 16957908.0, 7137188.0, 3516584.0, 1365095.0, 2829211.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7], "right_children": [2, 4, -1, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2432504, 0.092575856, 80.478004, 0.0526399, 1.9500875, -0.2967835, -0.12326148, 2.0, 24.210018, -10.151111, -5.2023087, -2.4015923, 2.7928421, 0.7344897, 12.381527], "split_indices": [29, 20, 0, 27, 27, 27, 29, 11, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [139.0, 134.0, 5.0, 118.0, 16.0, 98.0, 20.0, 10.0, 6.0, 76.0, 22.0, 7.0, 13.0, 3.0, 7.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-9.020782, -537.3611, 6091.719, -899.412, 663.24725, 91.672676, 3792.4675, -996.82043, -243.75288, 332.698, 15.306578, 20.109964, 50.711643, -10.434879, -6.8560777, -4.921851, 1.6950431, -0.10984702, 5.5442863], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 19, "left_children": [1, 3, 5, 7, 9, -1, 11, 13, 15, 17, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [444860480.0, 55801084.0, 43018176.0, 6015840.0, 8092225.0, 0.0, 8024896.0, 518904.0, 1520789.8, 1758859.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 6, 6, 7, 7, 8, 8, 9, 9], "right_children": [2, 4, 6, 8, 10, -1, 12, 14, 16, 18, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.1965036, -0.1536302, 1.0741702, -0.18306975, 2.4009788, 91.672676, 2.9190056, -0.2967835, -0.22261187, -0.031931423, 15.306578, 20.109964, 50.711643, -10.434879, -6.8560777, -4.921851, 1.6950431, -0.10984702, 5.5442863], "split_indices": [29, 20, 26, 29, 21, 0, 18, 27, 20, 20, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [136.0, 126.0, 10.0, 97.0, 29.0, 3.0, 7.0, 84.0, 13.0, 22.0, 7.0, 4.0, 3.0, 71.0, 13.0, 8.0, 5.0, 9.0, 13.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "19", "size_leaf_vector": "1"}}, {"base_weights": [15.295519, -450.66013, 9065.977, -766.8704, 1536.8859, 28.124556, 130.52979, -940.43787, -83.57789, 6.0589304, 1826.0981, -9.689023, -0.57736456, -5.354098, 0.5614558, 12.302102, 26.526344], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 20, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, -1, 15, -1, -1, -1, -1, -1, -1], "loss_changes": [602931200.0, 86652136.0, 137817150.0, 13915784.0, 4009176.0, 0.0, 0.0, 2305768.0, 1608396.8, 0.0, 3631636.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 10, 10], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, -1, 16, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, 0.092575856, 0.49706092, -0.18306975, 0.09053025, 28.124556, 130.52979, 0.8281771, -0.29197046, 6.0589304, -0.23109435, -9.689023, -0.57736456, -5.354098, 0.5614558, 12.302102, 26.526344], "split_indices": [29, 20, 21, 29, 18, 0, 0, 21, 20, 0, 17, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [141.0, 135.0, 6.0, 117.0, 18.0, 3.0, 3.0, 93.0, 24.0, 5.0, 13.0, 90.0, 3.0, 5.0, 19.0, 9.0, 4.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "17", "size_leaf_vector": "1"}}, {"base_weights": [-176.01588, -528.00525, 6184.9233, -934.9504, 556.76215, 76.98461, 31.251291, -10.282684, -627.7003, 207.80087, 22.08758, -6.815933, -1.8781185, -0.36881414, 8.852998], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 21, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, 11, 13, -1, -1, -1, -1, -1], "loss_changes": [298810850.0, 56142120.0, 8358016.0, 1992216.0, 19717668.0, 0.0, 0.0, 0.0, 440868.0, 5005896.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 8, 8, 9, 9], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, 12, 14, -1, -1, -1, -1, -1], "split_conditions": [1.2432504, -0.19222529, -0.59605557, -0.33963278, 1.9500875, 76.98461, 31.251291, -10.282684, 2.0, 0.9795277, 22.08758, -6.815933, -1.8781185, -0.36881414, 8.852998], "split_indices": [29, 29, 22, 27, 27, 0, 0, 0, 4, 26, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [131.0, 125.0, 6.0, 91.0, 34.0, 3.0, 3.0, 68.0, 23.0, 29.0, 5.0, 20.0, 3.0, 22.0, 7.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [174.22911, -407.55518, 8881.6455, -863.54346, 870.3973, 35.684826, 124.184586, -959.94916, -144.5172, 503.99127, 23.163517, -10.232893, -6.56771, -4.718733, 7.0997734, 1.8635948, 11.206089], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 22, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, 15, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [728035460.0, 79420360.0, 124808320.0, 6792976.0, 17905332.0, 0.0, 0.0, 1017656.0, 3971407.5, 5666753.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, 16, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, 0.009196514, 0.49706092, -0.18306975, 1.9500875, 35.684826, 124.184586, -0.3466928, 0.6479089, 0.9795277, 23.163517, -10.232893, -6.56771, -4.718733, 7.0997734, 1.8635948, 11.206089], "split_indices": [29, 27, 21, 29, 27, 0, 0, 27, 22, 26, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [142.0, 134.0, 8.0, 99.0, 35.0, 4.0, 4.0, 87.0, 12.0, 29.0, 6.0, 70.0, 17.0, 9.0, 3.0, 20.0, 9.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "17", "size_leaf_vector": "1"}}, {"base_weights": [117.967445, -390.39172, 8935.975, -733.30457, 1466.6085, 24.730349, 123.19111, -904.90533, 14.820685, 809.7709, 23.253883, -10.147655, -6.936144, 3.4467256, -4.4844403, -0.66374135, 10.5476265], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 23, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, 15, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [657965700.0, 89589096.0, 144453060.0, 15162220.0, 10526392.0, 0.0, 0.0, 1524368.0, 3669157.5, 3075183.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, 16, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, 0.092575856, 0.49706092, -0.20835175, 1.9500875, 24.730349, 123.19111, -0.4199775, 5.0, -0.56870174, 23.253883, -10.147655, -6.936144, 3.4467256, -4.4844403, -0.66374135, 10.5476265], "split_indices": [29, 20, 21, 20, 27, 0, 0, 27, 8, 17, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [145.0, 138.0, 7.0, 117.0, 21.0, 3.0, 4.0, 95.0, 22.0, 13.0, 8.0, 60.0, 35.0, 13.0, 9.0, 3.0, 10.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "17", "size_leaf_vector": "1"}}, {"base_weights": [-3.921098, -421.56448, 96.02513, -798.9949, 1159.6824, -962.43774, -395.7277, 129.76434, 1447.7606, -9.785928, -4.1485715, -5.4980054, -0.23584436, 5.5147934, -3.2436676, 4.7906766, 18.032932], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 24, "left_children": [1, 3, -1, 5, 7, 9, 11, 13, 15, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [577772300.0, 83286400.0, 0.0, 7001048.0, 7822796.0, 292736.0, 1936440.0, 1519501.8, 6368312.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8], "right_children": [2, 4, -1, 6, 8, 10, 12, 14, 16, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [2.8577383, -0.031931423, 96.02513, -0.28688815, 0.35915467, -0.075494125, -0.1536302, 2.0, 0.23448703, -9.785928, -4.1485715, -5.4980054, -0.23584436, 5.5147934, -3.2436676, 4.7906766, 18.032932], "split_indices": [20, 20, 0, 20, 26, 21, 20, 8, 20, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [142.0, 137.0, 5.0, 111.0, 26.0, 78.0, 33.0, 6.0, 20.0, 75.0, 3.0, 23.0, 10.0, 3.0, 3.0, 6.0, 14.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "17", "size_leaf_vector": "1"}}, {"base_weights": [68.129654, -437.4256, 9880.627, -772.57227, 1434.6859, 44.56701, 128.3437, -931.5901, 37.287598, 789.5782, 23.842764, -9.953266, -6.175437, -4.2028837, 3.1601224, 0.6273398, 10.740324], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 25, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, 15, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [708750800.0, 86439090.0, 54945600.0, 14973552.0, 10981460.0, 0.0, 0.0, 1326760.0, 2683559.8, 2827063.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, 16, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, 0.092575856, 4.0, -0.18306975, 1.9500875, 44.56701, 128.3437, -0.33963278, -0.22261187, 2.0, 23.842764, -9.953266, -6.175437, -4.2028837, 3.1601224, 0.6273398, 10.740324], "split_indices": [29, 20, 11, 29, 27, 0, 0, 27, 20, 11, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [141.0, 135.0, 6.0, 115.0, 20.0, 3.0, 3.0, 96.0, 19.0, 13.0, 7.0, 78.0, 18.0, 7.0, 12.0, 4.0, 9.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "17", "size_leaf_vector": "1"}}, {"base_weights": [107.9416, -440.3114, 8729.446, -777.1657, 1101.9955, 23.67095, 120.73418, -897.80066, -106.98085, 1473.8309, 220.56274, -9.554444, -6.3817096, 1.257995, -5.547281, 9.534909, 24.278605, -0.9410181, 5.5877776], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 26, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, 15, 17, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [632504400.0, 66317524.0, 141623800.0, 8312012.0, 7212960.0, 0.0, 0.0, 707120.0, 1841682.1, 5627432.0, 904022.75, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9, 10, 10], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, 16, 18, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, -0.009964261, 0.49706092, -0.18306975, 4.0, 23.67095, 120.73418, -0.28713, 0.30283502, -0.55640566, 1.0, -9.554444, -6.3817096, 1.257995, -5.547281, 9.534909, 24.278605, -0.9410181, 5.5877776], "split_indices": [29, 20, 21, 29, 8, 0, 0, 20, 23, 22, 10, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [132.0, 125.0, 7.0, 103.0, 22.0, 3.0, 4.0, 87.0, 16.0, 15.0, 7.0, 69.0, 18.0, 11.0, 5.0, 11.0, 4.0, 4.0, 3.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "19", "size_leaf_vector": "1"}}, {"base_weights": [129.28418, -449.84396, 8544.462, -659.3939, 1896.0917, 34.031815, 119.768295, -878.4624, 34.392433, 8.935451, 23.413116, -9.060153, -0.45769203, -4.6193705, 1.7772634], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 27, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, -1, -1, -1, -1, -1, -1], "loss_changes": [681256300.0, 65648544.0, 118060030.0, 18420692.0, 2817500.0, 0.0, 0.0, 2069200.0, 2216295.5, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, 1.3761487, 0.49706092, -0.18306975, 0.3882633, 34.031815, 119.768295, 0.8281771, -0.36327416, 8.935451, 23.413116, -9.060153, -0.45769203, -4.6193705, 1.7772634], "split_indices": [29, 27, 21, 29, 20, 0, 0, 21, 27, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [138.0, 130.0, 8.0, 120.0, 10.0, 4.0, 4.0, 91.0, 29.0, 4.0, 6.0, 88.0, 3.0, 6.0, 23.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-96.38205, -451.92056, 96.64629, -798.67395, 990.3553, -9.497859, -338.6373, 602.57385, 20.122059, 2.417621, -5.343035, 2.357969, 13.024281], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 28, "left_children": [1, 3, -1, 5, 7, -1, 9, 11, -1, -1, -1, -1, -1], "loss_changes": [493695300.0, 69306580.0, 0.0, 7394632.0, 9486114.0, 0.0, 3422576.2, 5083236.5, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 6, 6, 7, 7], "right_children": [2, 4, -1, 6, 8, -1, 10, 12, -1, -1, -1, -1, -1], "split_conditions": [2.1903315, -0.0155352615, 96.64629, -0.2967835, 1.9500875, -9.497859, -0.75929564, 0.858101, 20.122059, 2.417621, -5.343035, 2.357969, 13.024281], "split_indices": [29, 29, 0, 27, 27, 0, 23, 25, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [140.0, 136.0, 4.0, 110.0, 26.0, 82.0, 28.0, 20.0, 6.0, 7.0, 21.0, 14.0, 6.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [77.77397, -455.62878, 8425.787, -732.966, 1258.9048, 33.459732, 118.20422, -867.388, 91.87699, 761.57367, 20.737091, -9.730562, -6.282471, 2.9694486, -3.6773288, -0.8381725, 9.997637], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 29, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, 15, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [667314940.0, 67968750.0, 115644740.0, 13606772.0, 6524986.0, 0.0, 0.0, 2041824.0, 1805711.1, 2902979.5, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, 16, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2432504, 0.092575856, 0.49706092, 0.0526399, 1.9500875, 33.459732, 118.20422, -0.41024357, 5.0, -0.56870174, 20.737091, -9.730562, -6.282471, 2.9694486, -3.6773288, -0.8381725, 9.997637], "split_indices": [29, 20, 21, 27, 27, 0, 0, 27, 8, 17, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [148.0, 140.0, 8.0, 121.0, 19.0, 4.0, 4.0, 104.0, 17.0, 13.0, 6.0, 70.0, 34.0, 12.0, 5.0, 3.0, 10.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "17", "size_leaf_vector": "1"}}, {"base_weights": [3.6025932, -413.5285, 91.79902, -681.36945, 1320.6458, -812.89624, 124.655334, 686.19196, 20.279263, -9.171724, -4.675476, -0.51361233, 4.5592923, 0.76910454, 9.050463], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 30, "left_children": [1, 3, -1, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [528194660.0, 62211520.0, 0.0, 12292996.0, 6685444.0, 3136200.0, 1014722.4, 1397076.5, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7], "right_children": [2, 4, -1, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [2.3330073, 0.092575856, 91.79902, 0.14237125, 1.9500875, -0.2967835, 0.2040027, 1.0741702, 20.279263, -9.171724, -4.675476, -0.51361233, 4.5592923, 0.76910454, 9.050463], "split_indices": [20, 20, 0, 27, 27, 27, 25, 26, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [136.0, 131.0, 5.0, 114.0, 17.0, 98.0, 16.0, 10.0, 7.0, 74.0, 24.0, 11.0, 5.0, 3.0, 7.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-8.502671, -411.8429, 8117.122, -704.62933, 1394.7417, 102.11364, 39.935745, -825.6358, 91.32442, 720.1396, 21.392286, -9.476454, -5.7248955, -0.91911215, 5.4949217, 1.0215646, 10.163491], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 31, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, 15, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [485118720.0, 75563740.0, 19668672.0, 11825972.0, 8503824.0, 0.0, 0.0, 2731272.0, 1477745.5, 2092691.5, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, 16, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [2.8577383, 0.04001509, 0.26727843, 0.0526399, 1.9500875, 102.11364, 39.935745, -0.41024357, 0.2040027, -0.56870174, 21.392286, -9.476454, -5.7248955, -0.91911215, 5.4949217, 1.0215646, 10.163491], "split_indices": [20, 20, 17, 27, 27, 0, 0, 27, 25, 17, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [146.0, 140.0, 6.0, 121.0, 19.0, 3.0, 3.0, 105.0, 16.0, 11.0, 8.0, 69.0, 36.0, 12.0, 4.0, 4.0, 7.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "17", "size_leaf_vector": "1"}}, {"base_weights": [-109.424934, -383.20377, 93.63275, -711.402, 1057.9309, -903.90564, -256.565, 646.9677, 19.017145, -9.220561, -3.330463, -5.1080494, 0.42671734, 0.5029876, 9.184766], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 32, "left_children": [1, 3, -1, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [369260060.0, 66023520.0, 0.0, 9662000.0, 7785264.0, 511336.0, 2684571.8, 3031721.5, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7], "right_children": [2, 4, -1, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [2.1903315, -0.009964261, 93.63275, -0.31818703, 1.9500875, 0.21210697, -0.19460368, 0.6948394, 19.017145, -9.220561, -3.330463, -5.1080494, 0.42671734, 0.5029876, 9.184766], "split_indices": [29, 20, 0, 27, 27, 26, 29, 26, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [140.0, 137.0, 3.0, 112.0, 25.0, 78.0, 34.0, 18.0, 7.0, 75.0, 3.0, 18.0, 16.0, 6.0, 12.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [157.58472, -392.60748, 8209.572, -645.10516, 1143.0369, 32.26164, 115.510445, -827.02783, 2.4380226, 1404.7037, 3.4195814, -8.934304, -4.074759, 1.6393495, -3.814094, 6.815031, 18.619038], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 33, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, 15, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [623443600.0, 51919890.0, 112600260.0, 13431616.0, 3502172.0, 0.0, 0.0, 2115300.0, 1674249.5, 3359918.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, 16, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2432504, 0.092575856, 0.49706092, -0.19222529, 1.0, 32.26164, 115.510445, -0.2337211, 0.0222285, 1.5831932, 3.4195814, -8.934304, -4.074759, 1.6393495, -3.814094, 6.815031, 18.619038], "split_indices": [29, 20, 21, 29, 4, 0, 0, 27, 23, 27, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [139.0, 131.0, 8.0, 113.0, 18.0, 4.0, 4.0, 88.0, 25.0, 13.0, 5.0, 75.0, 13.0, 18.0, 7.0, 6.0, 7.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "17", "size_leaf_vector": "1"}}, {"base_weights": [145.39778, -392.89905, 8143.8955, -733.89056, 1051.7498, 32.003544, 114.586365, -844.90625, -125.69962, 626.1375, 21.175207, -9.047137, -5.487636, 3.052596, -3.1494234, 2.216034, 10.959343], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 34, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, 15, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [614590850.0, 66781970.0, 110805890.0, 7253244.0, 10467602.0, 0.0, 0.0, 1132844.0, 1564146.4, 3557983.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, 16, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, -0.009964261, 0.49706092, -0.18306975, 1.9500875, 32.003544, 114.586365, -0.28752968, -0.5392281, 0.32695737, 21.175207, -9.047137, -5.487636, 3.052596, -3.1494234, 2.216034, 10.959343], "split_indices": [29, 20, 21, 29, 27, 0, 0, 27, 17, 24, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [141.0, 133.0, 8.0, 108.0, 25.0, 4.0, 4.0, 91.0, 17.0, 19.0, 6.0, 74.0, 17.0, 5.0, 12.0, 11.0, 8.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "17", "size_leaf_vector": "1"}}, {"base_weights": [151.74348, -379.78073, 8969.378, -674.7851, 1320.7281, 37.30022, 113.66968, -853.6114, -95.043076, 766.4266, 22.406412, -9.01928, -5.6155643, -3.3875723, 1.7286088, 0.28809395, 10.585825], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 35, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, 15, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [659557300.0, 67611590.0, 58094016.0, 11737516.0, 8480600.0, 0.0, 0.0, 721032.0, 1886750.0, 2986383.5, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, 16, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, 0.092575856, 1.0, -0.18306975, 1.9500875, 37.30022, 113.66968, -0.2967835, 0.0526399, 2.0, 22.406412, -9.01928, -5.6155643, -3.3875723, 1.7286088, 0.28809395, 10.585825], "split_indices": [29, 20, 12, 29, 27, 0, 0, 27, 27, 11, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [139.0, 132.0, 7.0, 113.0, 19.0, 3.0, 4.0, 86.0, 27.0, 13.0, 6.0, 72.0, 14.0, 14.0, 13.0, 4.0, 9.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "17", "size_leaf_vector": "1"}}, {"base_weights": [6.654437, -429.4043, 8228.54, -668.9259, 1230.2316, 23.868715, 120.13048, -858.5831, -96.61897, 569.2257, 20.931932, -8.790598, -2.1390278, -4.3428025, 1.5260969, 2.2883222, 9.319115], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 36, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, 15, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [498295170.0, 53295336.0, 126079870.0, 12507836.0, 8505422.0, 0.0, 0.0, 960336.0, 2590950.0, 1144647.5, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, 16, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2220678, 0.04001509, 0.49706092, -0.2967835, 1.9500875, 23.868715, 120.13048, -0.11083611, -0.20922844, 0.17150046, 20.931932, -8.790598, -2.1390278, -4.3428025, 1.5260969, 2.2883222, 9.319115], "split_indices": [29, 20, 21, 27, 27, 0, 0, 29, 29, 24, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [137.0, 131.0, 6.0, 115.0, 16.0, 3.0, 3.0, 86.0, 29.0, 10.0, 6.0, 83.0, 3.0, 12.0, 17.0, 6.0, 4.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "17", "size_leaf_vector": "1"}}, {"base_weights": [199.90416, -332.25024, 8755.918, -647.69336, 1320.8663, 119.2295, 44.71089, -806.3262, 55.445602, 1691.123, 507.79626, -8.868587, -5.646848, 3.4562845, -2.9830837, 8.176633, 19.587305, 0.31607783, 9.760578], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 37, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, 15, 17, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [622134340.0, 68124340.0, 55251330.0, 12202744.0, 5462972.0, 0.0, 0.0, 1230996.0, 2258828.8, 1670560.0, 1752894.1, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9, 10, 10], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, 16, 18, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, 0.04001509, 0.4095176, -0.20835175, 4.0, 119.2295, 44.71089, -0.3204445, 4.0, 0.3882633, 0.17150046, -8.868587, -5.646848, 3.4562845, -2.9830837, 8.176633, 19.587305, 0.31607783, 9.760578], "split_indices": [29, 20, 24, 20, 8, 0, 0, 20, 8, 20, 24, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [135.0, 128.0, 7.0, 108.0, 20.0, 3.0, 4.0, 88.0, 20.0, 13.0, 7.0, 64.0, 24.0, 11.0, 9.0, 4.0, 9.0, 4.0, 3.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "19", "size_leaf_vector": "1"}}, {"base_weights": [-52.22579, -405.96915, 6827.9487, -747.407, 774.62054, 35.033688, 84.45516, -832.03766, -178.50505, 355.52454, 21.607178, -8.791388, -5.3163586, 6.5771136, -4.4237676, 0.6436108, 8.659861], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 38, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, 15, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [348373150.0, 55400290.0, 8055168.0, 4954908.0, 17239704.0, 0.0, 0.0, 834212.0, 3600742.8, 3659865.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, 16, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, 0.0526399, 0.49706092, -0.19222529, 1.9500875, 35.033688, 84.45516, -0.28752968, -0.44094184, 0.9795277, 21.607178, -8.791388, -5.3163586, 6.5771136, -4.4237676, 0.6436108, 8.659861], "split_indices": [29, 27, 21, 29, 27, 0, 0, 27, 25, 26, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [141.0, 135.0, 6.0, 105.0, 30.0, 3.0, 3.0, 91.0, 14.0, 24.0, 6.0, 77.0, 14.0, 3.0, 11.0, 16.0, 8.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "17", "size_leaf_vector": "1"}}, {"base_weights": [18.96437, -400.50226, 89.644424, -648.5464, 1056.6166, -765.5562, -7.959841, 680.06274, 17.591227, -8.726619, -5.631308, 1.5341759, -3.5630095, 0.16160958, 9.439974], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 39, "left_children": [1, 3, -1, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [502650900.0, 46948230.0, 0.0, 8238868.0, 3829538.0, 1524760.0, 1066542.2, 2437821.5, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7], "right_children": [2, 4, -1, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2432504, 0.092575856, 89.644424, 0.0526399, 1.9500875, -0.4199775, 5.0, 2.0, 17.591227, -8.726619, -5.631308, 1.5341759, -3.5630095, 0.16160958, 9.439974], "split_indices": [29, 20, 0, 27, 27, 27, 8, 11, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [132.0, 127.0, 5.0, 109.0, 18.0, 92.0, 17.0, 13.0, 5.0, 58.0, 34.0, 12.0, 5.0, 4.0, 9.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [126.47785, -374.03238, 8619.3545, -742.72736, 670.7488, 35.38474, 109.60168, 2.7336724, -787.4474, 274.77612, 1439.7878, -8.383137, -5.34501, -0.511122, 4.311478, 17.947643, 5.838477], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 40, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, 11, 13, 15, -1, -1, -1, -1, -1, -1], "loss_changes": [611085060.0, 52886144.0, 56363456.0, 4804736.0, 10566872.0, 0.0, 0.0, 0.0, 799828.0, 1296067.6, 2597958.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 8, 8, 9, 9, 10, 10], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, 12, 14, 16, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2432504, 0.0526399, 1.0, -0.8276071, 1.455611, 35.38474, 109.60168, 2.7336724, -0.2967835, -0.10745917, 2.0, -8.383137, -5.34501, -0.511122, 4.311478, 17.947643, 5.838477], "split_indices": [29, 27, 12, 23, 27, 0, 0, 0, 27, 29, 8, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [142.0, 135.0, 7.0, 100.0, 35.0, 3.0, 4.0, 4.0, 96.0, 24.0, 11.0, 78.0, 18.0, 8.0, 16.0, 7.0, 4.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "17", "size_leaf_vector": "1"}}, {"base_weights": [48.814472, -451.03592, 8477.7, -661.1896, 1046.5656, 116.20752, 42.67699, -783.56366, -144.13966, 661.29834, 21.333342, -8.301438, -4.5143347, 1.0655535, -5.057782, 1.9414613, 11.007011], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 41, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, 15, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [602096400.0, 43180010.0, 56262530.0, 7416512.0, 5706788.0, 0.0, 0.0, 1110304.0, 2229797.2, 2659926.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, 16, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2432504, 0.092575856, -0.011381618, -0.18306975, 2.2059762, 116.20752, 42.67699, -0.2337211, 0.43790478, 0.17150046, 21.333342, -8.301438, -4.5143347, 1.0655535, -5.057782, 1.9414613, 11.007011], "split_indices": [29, 20, 17, 29, 27, 0, 0, 27, 24, 24, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [141.0, 134.0, 7.0, 118.0, 16.0, 3.0, 4.0, 95.0, 23.0, 13.0, 3.0, 82.0, 13.0, 14.0, 9.0, 7.0, 6.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "17", "size_leaf_vector": "1"}}, {"base_weights": [76.22835, -374.16458, 8687.16, -636.4455, 1067.4589, 36.6891, 115.33595, -811.1744, -235.33093, 503.55502, 21.233156, -8.329737, -1.9416889, -4.591433, 0.4181488, -1.3654336, 6.7496653], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 42, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, 15, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [546213700.0, 51346124.0, 57671650.0, 7798840.0, 11433964.0, 0.0, 0.0, 900736.0, 2252272.5, 1738019.8, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, 16, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, -0.009964261, 4.0, -0.2967835, 1.9500875, 36.6891, 115.33595, -0.11083611, -0.20835175, -0.56870174, 21.233156, -8.329737, -1.9416889, -4.591433, 0.4181488, -1.3654336, 6.7496653], "split_indices": [29, 20, 11, 27, 27, 0, 0, 29, 20, 17, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [139.0, 133.0, 6.0, 113.0, 20.0, 3.0, 3.0, 78.0, 35.0, 14.0, 6.0, 75.0, 3.0, 19.0, 16.0, 3.0, 11.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "17", "size_leaf_vector": "1"}}, {"base_weights": [-129.28368, -387.77475, 54.49805, -717.60724, 678.0669, -781.18353, -191.57213, 295.0008, 17.424942, -8.23518, -4.9858575, 0.5027006, -3.1878948, 0.35251066, 8.100814], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 43, "left_children": [1, 3, -1, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [195360180.0, 45836440.0, 0.0, 3161144.0, 12125888.0, 642072.0, 385250.66, 3182368.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7], "right_children": [2, 4, -1, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [2.8455322, 0.21398789, 54.49805, -0.11083611, 1.9500875, -0.28752968, 2.0, 0.9795277, 17.424942, -8.23518, -4.9858575, 0.5027006, -3.1878948, 0.35251066, 8.100814], "split_indices": [25, 27, 0, 29, 27, 27, 11, 26, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [133.0, 128.0, 5.0, 98.0, 30.0, 87.0, 11.0, 23.0, 7.0, 74.0, 13.0, 4.0, 7.0, 16.0, 7.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [26.679583, -375.49725, 7376.9736, -708.9718, 530.8457, 92.65845, 36.438335, -746.99396, -129.68352, 202.48328, 1815.3551, -8.121756, -5.3933134, -3.457991, 1.1882796, -4.4005384, 3.2289534, 9.10907, 22.659393], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 44, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, 15, 17, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [398890560.0, 39063390.0, 15595456.0, 1973460.0, 14394680.0, 0.0, 0.0, 752980.0, 417063.75, 2385787.0, 788334.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9, 10, 10], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, 16, 18, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [2.8577383, -0.2066361, 0.26727843, -0.11083611, 1.9500875, 92.65845, 36.438335, -0.3564305, -0.29787838, -0.20922844, -0.46722066, -8.121756, -5.3933134, -3.457991, 1.1882796, -4.4005384, 3.2289534, 9.10907, 22.659393], "split_indices": [20, 20, 17, 29, 27, 0, 0, 27, 20, 29, 23, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [133.0, 127.0, 6.0, 93.0, 34.0, 3.0, 3.0, 87.0, 6.0, 28.0, 6.0, 64.0, 23.0, 3.0, 3.0, 4.0, 24.0, 3.0, 3.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "19", "size_leaf_vector": "1"}}, {"base_weights": [-5.2159805, -376.3108, 77.267784, -754.0243, 415.92963, -803.15314, -447.49567, 127.00866, 18.011017, -8.311671, -5.14692, -2.1048114, -4.9299226, -0.25205588, 5.100748], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 45, "left_children": [1, 3, -1, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [375916320.0, 37718640.0, 0.0, 920512.0, 16179468.0, 153904.0, 47131.0, 2053689.5, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7], "right_children": [2, 4, -1, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2220678, -0.18306975, 77.267784, -0.2337211, 1.9500875, -0.2380833, -0.7300881, 0.08470288, 18.011017, -8.311671, -5.14692, -2.1048114, -4.9299226, -0.25205588, 5.100748], "split_indices": [29, 29, 0, 27, 27, 18, 23, 18, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [129.0, 124.0, 5.0, 84.0, 40.0, 71.0, 13.0, 34.0, 6.0, 63.0, 8.0, 3.0, 10.0, 25.0, 9.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [67.515335, -376.7824, 7445.5386, -596.71814, 1268.7863, 16.867405, 105.63449, -709.8658, 168.95851, 599.9159, 18.626547, -7.872075, -3.7037401, 5.6157813, -0.6054839, 7.546569, 2.1780047], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 46, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, 15, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [461731500.0, 48892310.0, 125824320.0, 10342864.0, 5237854.0, 0.0, 0.0, 2412784.0, 1475795.2, 367698.5, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, 16, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2432504, 0.04001509, 0.49706092, 0.0526399, 1.5372862, 16.867405, 105.63449, -0.2967835, 2.0, -0.6716731, 18.626547, -7.872075, -3.7037401, 5.6157813, -0.6054839, 7.546569, 2.1780047], "split_indices": [29, 20, 21, 27, 27, 0, 0, 27, 8, 23, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [139.0, 132.0, 7.0, 117.0, 15.0, 3.0, 4.0, 102.0, 15.0, 8.0, 7.0, 82.0, 20.0, 5.0, 10.0, 5.0, 3.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "17", "size_leaf_vector": "1"}}, {"base_weights": [-133.04845, -351.72275, 49.186184, -641.4184, 920.86316, -751.6955, -177.21255, 497.44324, 1665.3737, -7.745658, -2.985886, 3.2203436, -2.992716, -0.35328436, 7.0508065, 10.26649, 22.070923], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 47, "left_children": [1, 3, -1, 5, 7, 9, 11, 13, 15, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [159697300.0, 51466068.0, 0.0, 5651312.0, 7367610.0, 711624.0, 1497938.5, 2016196.5, 847850.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8], "right_children": [2, 4, -1, 6, 8, 10, 12, 14, 16, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [2.8577383, -0.009964261, 49.186184, -0.19222529, 1.9500875, -0.11965306, -0.8323144, 0.6948394, -0.42340943, -7.745658, -2.985886, 3.2203436, -2.992716, -0.35328436, 7.0508065, 10.26649, 22.070923], "split_indices": [20, 20, 0, 29, 27, 27, 23, 26, 23, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [142.0, 137.0, 5.0, 112.0, 25.0, 90.0, 22.0, 17.0, 8.0, 85.0, 5.0, 4.0, 18.0, 5.0, 12.0, 5.0, 3.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "17", "size_leaf_vector": "1"}}, {"base_weights": [94.77115, -331.7041, 94.61448, -597.2741, 1100.46, -712.7188, 139.93272, 630.96936, 19.492886, -7.9928794, -5.1189437, 3.0799468, -2.9138253, -0.05015376, 8.627016], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 48, "left_children": [1, 3, -1, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [550407230.0, 50862084.0, 0.0, 9631732.0, 7138660.0, 1277088.0, 1249549.1, 2215085.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7], "right_children": [2, 4, -1, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, 0.092575856, 94.61448, 0.0526399, 1.9500875, -0.41024357, 4.0, 2.0, 19.492886, -7.9928794, -5.1189437, 3.0799468, -2.9138253, -0.05015376, 8.627016], "split_indices": [29, 20, 0, 27, 27, 27, 8, 11, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [136.0, 131.0, 5.0, 111.0, 20.0, 96.0, 15.0, 14.0, 6.0, 65.0, 31.0, 11.0, 4.0, 4.0, 10.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-81.36021, -399.58307, 4736.3696, -591.6791, 979.888, 73.13945, 12.115003, -718.27185, -81.89172, 1529.6326, 442.09363, -7.9808865, -4.847329, 0.8229516, -4.4108825, 18.103504, 7.96302, -1.0751696, 6.9301486], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 49, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, 15, 17, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [222660220.0, 36618508.0, 72908850.0, 7685344.0, 4349604.0, 0.0, 0.0, 1402488.0, 1510719.2, 205024.0, 1453659.2, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9, 10, 10], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, 16, 18, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.1965036, 0.092575856, -0.9297316, -0.19252256, 2.0, 73.13945, 12.115003, -0.33963278, 0.0222285, 5.0, 0.8186169, -7.9808865, -4.847329, 0.8229516, -4.4108825, 18.103504, 7.96302, -1.0751696, 6.9301486], "split_indices": [29, 20, 22, 29, 8, 0, 0, 27, 23, 11, 26, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [143.0, 135.0, 8.0, 119.0, 16.0, 4.0, 4.0, 95.0, 24.0, 7.0, 9.0, 69.0, 26.0, 17.0, 7.0, 4.0, 3.0, 3.0, 6.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "19", "size_leaf_vector": "1"}}, {"base_weights": [127.05139, -221.15521, 79.40541, -568.95544, 1807.1388, -697.8261, 171.4363, 930.13745, 50.825848, -7.9468803, -4.800177, 3.9915662, -0.09754503, 11.870909, -2.017092], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 50, "left_children": [1, 3, -1, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [382656100.0, 96262610.0, 0.0, 11187800.0, 52723304.0, 1707044.0, 746627.4, 5183690.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7], "right_children": [2, 4, -1, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [2.8577383, 0.20579594, 79.40541, 0.0526399, -0.37959817, -0.41024357, 3.0, 1.5613428, 50.825848, -7.9468803, -4.800177, 3.9915662, -0.09754503, 11.870909, -2.017092], "split_indices": [20, 20, 0, 27, 23, 27, 8, 18, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [139.0, 134.0, 5.0, 115.0, 19.0, 98.0, 17.0, 16.0, 3.0, 66.0, 32.0, 7.0, 10.0, 13.0, 3.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [84.362915, -371.45874, 7114.9126, -551.45404, 1415.5231, 25.49333, 102.57489, -716.7466, 6.8799186, 5.1830397, 1756.6866, -7.7037735, -5.293255, -4.7025604, 1.4576209, 8.899268, 20.417288], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 51, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, -1, 15, -1, -1, -1, -1, -1, -1], "loss_changes": [473731070.0, 45489630.0, 102978110.0, 11725632.0, 3068556.0, 0.0, 0.0, 577140.0, 2056483.5, 0.0, 406286.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 10, 10], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, -1, 16, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, 1.1800077, 0.49706092, -0.19222529, 1.5831932, 25.49333, 102.57489, -0.33963278, -0.36327416, 5.1830397, -0.9625529, -7.7037735, -5.293255, -4.7025604, 1.4576209, 8.899268, 20.417288], "split_indices": [29, 27, 21, 29, 27, 0, 0, 27, 27, 0, 22, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [146.0, 138.0, 8.0, 126.0, 12.0, 4.0, 4.0, 97.0, 29.0, 4.0, 8.0, 73.0, 24.0, 6.0, 23.0, 3.0, 5.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "17", "size_leaf_vector": "1"}}, {"base_weights": [97.28009, -331.71835, 7056.3574, -544.2871, 1216.8458, 25.289385, 109.51517, -696.5825, 3.8196309, 537.05505, 16.858881, -7.8969245, -5.4186316, -1.5583646, 4.5944853, 7.2676773, 1.6562536], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 52, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, 15, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [411345760.0, 43455176.0, 113383100.0, 9602260.0, 4195974.0, 0.0, 0.0, 868492.0, 1962971.6, 443259.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, 16, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, 0.092575856, 0.49706092, -0.20835175, 1.5831932, 25.289385, 109.51517, -0.39440298, 4.0, 1.0, 16.858881, -7.8969245, -5.4186316, -1.5583646, 4.5944853, 7.2676773, 1.6562536], "split_indices": [29, 20, 21, 20, 27, 0, 0, 26, 0, 16, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [136.0, 129.0, 7.0, 114.0, 15.0, 4.0, 3.0, 89.0, 25.0, 7.0, 8.0, 53.0, 36.0, 19.0, 6.0, 4.0, 3.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "17", "size_leaf_vector": "1"}}, {"base_weights": [-182.62782, -511.2817, 1850.795, -676.7592, 158.13765, 1235.0984, 38.508575, -701.7038, -0.92384857, -40.594196, 493.879, 1588.7411, -2.2298956, -7.63964, -5.7668505, -3.432925, 1.2819526, 5.885437, 2.2838714, 7.029554, 17.841711], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 53, "left_children": [1, 3, 5, 7, 9, 11, -1, 13, -1, 15, 17, 19, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [91165620.0, 13067612.0, 18640500.0, 1305328.0, 1621433.8, 8604684.0, 0.0, 259832.0, 0.0, 861506.5, 91697.0, 995976.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 9, 9, 10, 10, 11, 11], "right_children": [2, 4, 6, 8, 10, 12, -1, 14, -1, 16, 18, 20, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [0.23448703, 0.0526399, 2.8577383, -0.09288174, 0.2040027, 1.4532615, 38.508575, -0.43151668, -0.92384857, -0.12326148, 2.0, 1.5372862, -2.2298956, -7.63964, -5.7668505, -3.432925, 1.2819526, 5.885437, 2.2838714, 7.029554, 17.841711], "split_indices": [20, 27, 20, 29, 25, 18, 0, 27, 0, 29, 8, 27, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [134.0, 116.0, 18.0, 93.0, 23.0, 15.0, 3.0, 89.0, 4.0, 15.0, 8.0, 12.0, 3.0, 56.0, 33.0, 5.0, 10.0, 5.0, 3.0, 3.0, 9.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "21", "size_leaf_vector": "1"}}, {"base_weights": [59.06039, -306.70154, 7052.3657, -577.5393, 840.5261, 14.932467, 108.48368, -684.7292, -33.754097, 605.02814, 17.08592, -7.31959, -4.1758537, 5.5181103, -1.7804201, 2.9820006, 11.046897], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 54, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, 15, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [360267500.0, 42120896.0, 131516480.0, 6330240.0, 4281144.0, 0.0, 0.0, 842388.0, 1703517.5, 3043265.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, 16, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, -0.009964261, 0.49706092, -0.20835175, 0.8306982, 14.932467, 108.48368, -0.2291447, -0.46422407, 2.0008802, 17.08592, -7.31959, -4.1758537, 5.5181103, -1.7804201, 2.9820006, 11.046897], "split_indices": [29, 20, 21, 20, 29, 0, 0, 29, 24, 21, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [139.0, 133.0, 6.0, 108.0, 25.0, 3.0, 3.0, 90.0, 18.0, 21.0, 4.0, 75.0, 15.0, 3.0, 15.0, 14.0, 7.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "17", "size_leaf_vector": "1"}}, {"base_weights": [-106.79634, -313.86447, 69.08427, -472.50598, 2263.6204, -632.35376, 326.40857, 26.995308, 11.528022, -7.1575365, -2.7758825, -1.2042139, 5.015024], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 55, "left_children": [1, 3, -1, 5, 7, 9, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [202630480.0, 56270372.0, 0.0, 16552644.0, 761328.0, 2967068.0, 1781647.5, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, -1, 6, 8, 10, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [2.8455322, 1.9500875, 69.08427, 0.33140355, -0.00091510743, -0.2967835, -0.52717066, 26.995308, 11.528022, -7.1575365, -2.7758825, -1.2042139, 5.015024], "split_indices": [25, 27, 0, 27, 21, 27, 17, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [137.0, 134.0, 3.0, 127.0, 7.0, 106.0, 21.0, 4.0, 3.0, 85.0, 21.0, 6.0, 15.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [208.19751, -298.3635, 7653.9604, -570.1677, 913.6035, 107.29581, 36.62652, -700.4403, -94.21397, 375.10245, 1649.7493, -7.449767, -5.020446, 4.8054285, -2.4864006, 1.4152102, 5.8845234, 8.76416, 18.903297], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 56, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, 15, 17, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [473751040.0, 39392172.0, 58905856.0, 5948286.0, 8102108.0, 0.0, 0.0, 297388.0, 2072123.0, 614332.4, 17450.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9, 10, 10], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, 16, 18, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, 0.41567117, -0.011381618, -0.21219923, 1.418313, 107.29581, 36.62652, -0.2967835, -0.44094184, 0.1790324, -0.3717641, -7.449767, -5.020446, 4.8054285, -2.4864006, 1.4152102, 5.8845234, 8.76416, 18.903297], "split_indices": [29, 27, 17, 29, 27, 0, 0, 27, 25, 25, 17, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [124.0, 117.0, 7.0, 96.0, 21.0, 3.0, 4.0, 75.0, 21.0, 13.0, 8.0, 59.0, 16.0, 4.0, 17.0, 7.0, 6.0, 3.0, 5.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "19", "size_leaf_vector": "1"}}, {"base_weights": [131.61511, -318.20782, 6814.366, -542.82336, 1069.8716, 90.677734, 17.30638, -685.25885, -91.88384, 533.2374, 16.068722, -7.644003, -5.497072, -3.6677148, 1.2372339, 7.380339, 3.1076312], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 57, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, 15, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [429028770.0, 42359784.0, 87407300.0, 7387612.0, 4618228.0, 0.0, 0.0, 523716.0, 1764167.8, 271722.75, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, 16, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, 0.092575856, -0.9297316, -0.20007384, 1.5831932, 90.677734, 17.30638, -0.44001678, -0.2066361, -0.6716731, 16.068722, -7.644003, -5.497072, -3.6677148, 1.2372339, 7.380339, 3.1076312], "split_indices": [29, 20, 22, 29, 27, 0, 0, 27, 20, 23, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [141.0, 133.0, 8.0, 115.0, 18.0, 5.0, 3.0, 87.0, 28.0, 10.0, 8.0, 52.0, 35.0, 12.0, 16.0, 4.0, 6.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "17", "size_leaf_vector": "1"}}, {"base_weights": [67.144, -367.1366, 6735.8276, -546.1971, 781.48175, 23.762592, 105.013054, -642.10065, -0.69595015, 921.5328, 0.95907706, -7.0558066, -4.654932, 1.1662085, -1.849658, 11.106077, 5.737852], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 58, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, 15, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [378963970.0, 25724500.0, 106371780.0, 5597246.0, 1543777.0, 0.0, 0.0, 691648.0, 389082.88, 283086.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, 16, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, 0.092575856, 0.49706092, -0.11083611, 1.1764319, 23.762592, 105.013054, -0.2589894, 3.0, 2.0, 0.95907706, -7.0558066, -4.654932, 1.1662085, -1.849658, 11.106077, 5.737852], "split_indices": [29, 20, 21, 29, 18, 0, 0, 29, 0, 8, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [129.0, 122.0, 7.0, 106.0, 16.0, 4.0, 3.0, 90.0, 16.0, 13.0, 3.0, 64.0, 26.0, 10.0, 6.0, 7.0, 6.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "17", "size_leaf_vector": "1"}}, {"base_weights": [93.20501, -305.84204, 6915.2476, -574.37384, 862.66956, 97.04507, 16.998363, -652.2235, -100.95949, 467.8231, 1692.5837, -6.952936, -3.7328537, -2.9378722, 1.0307486, 1.4758428, 8.896909, 8.464632, 20.309435], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 59, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, 15, 17, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [394129150.0, 43483670.0, 99879870.0, 4061852.0, 7727860.0, 0.0, 0.0, 893680.0, 699140.4, 2435462.2, 570938.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9, 10, 10], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, 16, 18, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, -0.009964261, -0.9297316, -0.11083611, 1.9500875, 97.04507, 16.998363, -0.2337211, 0.0526399, 0.32695737, -0.89633024, -6.952936, -3.7328537, -2.9378722, 1.0307486, 1.4758428, 8.896909, 8.464632, 20.309435], "split_indices": [29, 20, 22, 29, 27, 0, 0, 27, 27, 24, 22, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [143.0, 136.0, 7.0, 111.0, 25.0, 4.0, 3.0, 95.0, 16.0, 18.0, 7.0, 81.0, 14.0, 8.0, 8.0, 11.0, 7.0, 3.0, 4.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "19", "size_leaf_vector": "1"}}, {"base_weights": [-22.556572, -315.18942, 4991.5884, -636.80756, 480.65216, 66.36851, 16.870874, -700.729, -432.83926, 218.99603, 14.998897, -7.254988, -4.0877433, -4.612076, -1.6148645, -3.593488, 3.6083004], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 60, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, 15, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [212864530.0, 35372216.0, 32296384.0, 974304.0, 10338948.0, 0.0, 0.0, 231236.0, 125602.5, 2836620.2, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, 16, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, -0.2066361, -0.9297316, -0.33963278, 1.9500875, 66.36851, 16.870874, -0.2271967, -0.06969563, 0.13784045, 14.998897, -7.254988, -4.0877433, -4.612076, -1.6148645, -3.593488, 3.6083004], "split_indices": [29, 20, 22, 27, 27, 0, 0, 18, 29, 26, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [143.0, 136.0, 7.0, 97.0, 39.0, 4.0, 3.0, 72.0, 25.0, 32.0, 7.0, 65.0, 7.0, 22.0, 3.0, 6.0, 26.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "17", "size_leaf_vector": "1"}}, {"base_weights": [63.278442, -337.63763, 6820.8403, -527.0847, 1480.7876, 16.744343, 95.73776, -643.1621, 102.86704, 5.7096534, 18.217014, -6.7494164, -3.1311343, 2.5129359, -2.853272], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 61, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, -1, -1, -1, -1, -1, -1], "loss_changes": [387012200.0, 47287280.0, 97310050.0, 9060360.0, 2991856.0, 0.0, 0.0, 879404.0, 1224064.2, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, 1.1800077, 0.49706092, -0.11083611, 1.5831932, 16.744343, 95.73776, -0.11965306, -0.29694942, 5.7096534, 18.217014, -6.7494164, -3.1311343, 2.5129359, -2.853272], "split_indices": [29, 27, 21, 29, 27, 0, 0, 27, 28, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [141.0, 134.0, 7.0, 122.0, 12.0, 3.0, 4.0, 103.0, 19.0, 4.0, 8.0, 93.0, 10.0, 14.0, 5.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [135.75664, -278.13025, 6585.782, -499.21387, 981.5528, 22.908493, 103.07977, -599.8515, 68.85312, 453.98953, 1650.191, 1.3910781, -6.2662516, 1.7059674, -1.4609299, -1.499776, 6.7198715, 19.881233, 8.152029], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 62, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, 15, 17, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [354217060.0, 35301864.0, 104277340.0, 6158428.0, 5952852.0, 0.0, 0.0, 1887622.0, 396705.16, 1680796.0, 636352.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9, 10, 10], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, 16, 18, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, 0.092575856, 0.49706092, 0.0526399, 1.9500875, 22.908493, 103.07977, -0.9199369, 5.0, 0.8186169, 2.0008802, 1.3910781, -6.2662516, 1.7059674, -1.4609299, -1.499776, 6.7198715, 19.881233, 8.152029], "split_indices": [29, 20, 21, 27, 27, 0, 0, 23, 8, 26, 21, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [131.0, 124.0, 7.0, 106.0, 18.0, 4.0, 3.0, 90.0, 16.0, 11.0, 7.0, 3.0, 87.0, 11.0, 5.0, 3.0, 8.0, 4.0, 3.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "19", "size_leaf_vector": "1"}}, {"base_weights": [70.53512, -343.41263, 4805.715, -567.3382, 539.6564, 5.7474303, 7141.293, -614.45325, 57.168484, 212.89561, 965.876, 102.30668, 22.6657, -6.717707, -3.5283813, -3.209435, 5.155413, 4.355328, 0.35750014, 11.615394, 4.98752], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 63, "left_children": [1, 3, 5, 7, 9, -1, 11, 13, 15, 17, 19, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [268226740.0, 25221628.0, 104594190.0, 3006970.0, 3415320.0, 0.0, 82229150.0, 1172412.0, 1552009.2, 615409.75, 425903.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10], "right_children": [2, 4, 6, 8, 10, -1, 12, 14, 16, 18, 20, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [0.6820698, 0.21398789, 1.7035879, 0.4472843, 0.14085093, 5.7474303, 0.4095176, -0.2685201, 3.0, 3.0, 2.0, 102.30668, 22.6657, -6.717707, -3.5283813, -3.209435, 5.155413, 4.355328, 0.35750014, 11.615394, 4.98752], "split_indices": [18, 27, 21, 26, 20, 0, 24, 27, 11, 8, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [135.0, 125.0, 10.0, 100.0, 25.0, 4.0, 6.0, 93.0, 7.0, 15.0, 10.0, 3.0, 3.0, 75.0, 18.0, 4.0, 3.0, 6.0, 9.0, 6.0, 4.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "21", "size_leaf_vector": "1"}}, {"base_weights": [116.487854, -273.89352, 6494.434, -535.82336, 804.505, 22.679245, 101.539375, -633.03687, -86.14868, 1113.0167, 98.08091, -6.5579233, -2.7927501, 4.2376075, -3.3495705, 5.3590927, 15.211018, -1.9525307, 5.1358666], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 64, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, 15, 17, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [345376130.0, 37433936.0, 100705790.0, 4579120.0, 5557158.0, 0.0, 0.0, 517112.0, 2679327.5, 3423782.0, 1197248.9, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9, 10, 10], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, 16, 18, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, -0.061758023, 0.49706092, -0.18306975, 4.0, 22.679245, 101.539375, -0.086669296, -0.5566443, 0.022172607, -0.37846264, -6.5579233, -2.7927501, 4.2376075, -3.3495705, 5.3590927, 15.211018, -1.9525307, 5.1358666], "split_indices": [29, 18, 21, 29, 8, 0, 0, 21, 17, 29, 17, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [137.0, 130.0, 7.0, 105.0, 25.0, 4.0, 3.0, 86.0, 19.0, 17.0, 8.0, 80.0, 6.0, 6.0, 13.0, 8.0, 9.0, 5.0, 3.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "19", "size_leaf_vector": "1"}}, {"base_weights": [49.652626, -329.74393, 7305.3076, -538.7685, 702.4473, 27.039442, 100.803185, -617.43304, -62.847176, -3.1588435, 870.9963, -6.9824853, -4.6334195, 5.4859486, -2.3306296, 5.225458, 13.283413], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 65, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, -1, 15, -1, -1, -1, -1, -1, -1], "loss_changes": [387797440.0, 29289392.0, 62123904.0, 4154198.0, 4222882.0, 0.0, 0.0, 871840.0, 1897136.2, 0.0, 2492937.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 10, 10], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, -1, 16, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, -0.031931423, 3.0, 0.4472843, -0.09733688, 27.039442, 100.803185, -0.41024357, 0.49679533, -3.1588435, 1.5831932, -6.9824853, -4.6334195, 5.4859486, -2.3306296, 5.225458, 13.283413], "split_indices": [29, 20, 13, 26, 26, 0, 0, 27, 26, 0, 27, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [139.0, 133.0, 6.0, 111.0, 22.0, 3.0, 3.0, 95.0, 16.0, 3.0, 19.0, 60.0, 35.0, 3.0, 13.0, 12.0, 7.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "17", "size_leaf_vector": "1"}}, {"base_weights": [-124.02718, -387.815, 2012.123, -597.87354, 105.23069, 3017.8525, 431.5818, -6.741627, -387.80908, -129.06194, 604.1038, 14.699813, 45.85171, 10.155079, -2.6021483, -1.4158058, -4.756934, -2.2898421, 4.7773166, 7.5153766, 1.213268], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 66, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, 15, 17, 19, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [76851380.0, 12594748.0, 22541164.0, 1100626.0, 4402659.5, 15093344.0, 3092033.0, 0.0, 473580.5, 1685805.4, 762863.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 8, 8, 9, 9, 10, 10], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, 16, 18, 20, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [0.43779635, 0.19972922, 3.0, -0.43144402, -0.03467891, -0.77572244, 1.4532615, -6.741627, -0.5124338, 0.05719311, -0.2913814, 14.699813, 45.85171, 10.155079, -2.6021483, -1.4158058, -4.756934, -2.2898421, 4.7773166, 7.5153766, 1.213268], "split_indices": [18, 26, 0, 21, 18, 28, 18, 0, 17, 22, 17, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [134.0, 120.0, 14.0, 84.0, 36.0, 8.0, 6.0, 60.0, 24.0, 25.0, 11.0, 5.0, 3.0, 3.0, 3.0, 7.0, 17.0, 22.0, 3.0, 8.0, 3.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "21", "size_leaf_vector": "1"}}, {"base_weights": [90.19299, -288.1401, 6368.719, -485.05692, 1084.0544, 21.875746, 100.02944, -598.40717, -43.9877, 5.1420665, 1533.4407, -6.1973715, -0.9903146, -2.0922866, 3.3329043, 18.150995, 7.2756686], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 67, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, -1, 15, -1, -1, -1, -1, -1, -1], "loss_changes": [334388100.0, 36463456.0, 99678370.0, 5823062.0, 3564680.0, 0.0, 0.0, 929160.0, 1628266.8, 0.0, 721974.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 10, 10], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, -1, 16, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2432504, 0.092575856, 0.49706092, -0.18306975, 1.5831932, 21.875746, 100.02944, 2.0, 1.0, 5.1420665, 7.0, -6.1973715, -0.9903146, -2.0922866, 3.3329043, 18.150995, 7.2756686], "split_indices": [29, 20, 21, 29, 27, 0, 0, 4, 6, 0, 11, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [139.0, 132.0, 7.0, 116.0, 16.0, 4.0, 3.0, 92.0, 24.0, 8.0, 8.0, 88.0, 4.0, 17.0, 7.0, 5.0, 3.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "17", "size_leaf_vector": "1"}}, {"base_weights": [52.59235, -319.21442, 6320.27, -495.97845, 857.30066, 21.700739, 99.27923, -603.4057, -26.017988, 464.29092, 17.862476, -6.6120086, -3.7894454, -1.5686531, 3.7084365, -0.88724357, 6.943795], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 68, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, 15, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [332930000.0, 28500518.0, 98234180.0, 5941648.0, 5741971.0, 0.0, 0.0, 994724.0, 1252501.6, 1843063.8, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, 16, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2220678, 0.04001509, 0.49706092, -0.18306975, 2.2059762, 21.700739, 99.27923, -0.33963278, 1.0, 2.0, 17.862476, -6.6120086, -3.7894454, -1.5686531, 3.7084365, -0.88724357, 6.943795], "split_indices": [29, 20, 21, 29, 27, 0, 0, 27, 6, 11, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [141.0, 134.0, 7.0, 117.0, 17.0, 4.0, 3.0, 95.0, 22.0, 13.0, 4.0, 74.0, 21.0, 17.0, 5.0, 4.0, 9.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "17", "size_leaf_vector": "1"}}, {"base_weights": [29.935513, -250.95535, 61.109314, -583.34894, 459.3752, -6.4291935, -414.2737, 226.75241, 12.871196, -4.4116483, -1.5605634, -2.4024882, 3.7968926], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 69, "left_children": [1, 3, -1, 5, 7, -1, 9, 11, -1, -1, -1, -1, -1], "loss_changes": [232127170.0, 30962200.0, 0.0, 629946.0, 7795184.0, 0.0, 111631.5, 2519571.8, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 6, 6, 7, 7], "right_children": [2, 4, -1, 6, 8, -1, 10, 12, -1, -1, -1, -1, -1], "split_conditions": [2.3330073, -0.18306975, 61.109314, -0.3564305, 1.9500875, -6.4291935, 8.0, 0.13784045, 12.871196, -4.4116483, -1.5605634, -2.4024882, 3.7968926], "split_indices": [20, 29, 0, 27, 27, 0, 14, 26, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [134.0, 129.0, 5.0, 88.0, 41.0, 63.0, 25.0, 33.0, 8.0, 22.0, 3.0, 8.0, 25.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [-223.87953, -531.3087, 958.9313, -599.5656, -35.054764, 440.2045, 2005.0985, -657.6503, -453.72653, 5.1296916, -231.1585, 710.01495, -53.25057, 30.174257, 5.917315, -6.7184873, -3.5287926, -4.935818, -2.5224745, -3.803234, 1.2493602, 8.775284, 1.137026, -2.8980594, 1.4662384], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 70, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, 17, -1, 19, 21, 23, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [49235940.0, 3603304.0, 14312002.0, 497772.0, 1623122.5, 2700661.5, 11091240.0, 44088.0, 116321.0, 0.0, 631826.6, 1198698.0, 420757.7, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 10, 10, 11, 11, 12, 12], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, 18, -1, 20, 22, 24, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [-0.08745988, 0.4472843, 0.8306982, -0.26600468, 0.5834396, 4.0, 3.0, -0.23053838, 9.0, 5.1296916, -0.12326148, -0.42340943, 2.0, 30.174257, 5.917315, -6.7184873, -3.5287926, -4.935818, -2.5224745, -3.803234, 1.2493602, 8.775284, 1.137026, -2.8980594, 1.4662384], "split_indices": [18, 26, 29, 29, 26, 8, 0, 18, 11, 0, 29, 23, 11, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [133.0, 106.0, 27.0, 93.0, 13.0, 19.0, 8.0, 64.0, 29.0, 3.0, 10.0, 12.0, 7.0, 4.0, 4.0, 60.0, 4.0, 23.0, 6.0, 7.0, 3.0, 9.0, 3.0, 3.0, 4.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "25", "size_leaf_vector": "1"}}, {"base_weights": [197.50601, -229.34724, 6151.5176, -434.08096, 1205.3208, 21.104994, 89.62212, -577.8355, 34.552975, 5.661294, 1532.2126, -5.989478, -0.0054871524, -3.174094, 1.9945866, 8.327693, 18.596966], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 71, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, -1, 15, -1, -1, -1, -1, -1, -1], "loss_changes": [341970980.0, 37532468.0, 83306780.0, 7497894.0, 2475502.0, 0.0, 0.0, 1035584.0, 1630394.4, 0.0, 741598.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 10, 10], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, -1, 16, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, 0.20579594, 0.49706092, -0.18306975, 1.5831932, 21.104994, 89.62212, 0.8281771, -0.36327416, 5.661294, -0.9625529, -5.989478, -0.0054871524, -3.174094, 1.9945866, 8.327693, 18.596966], "split_indices": [29, 20, 21, 29, 27, 0, 0, 21, 27, 0, 22, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [133.0, 125.0, 8.0, 110.0, 15.0, 4.0, 4.0, 84.0, 26.0, 6.0, 9.0, 81.0, 3.0, 8.0, 18.0, 4.0, 5.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "17", "size_leaf_vector": "1"}}, {"base_weights": [29.684994, -286.3659, 72.41249, -571.76, 309.8101, -590.7934, -0.0054459535, -80.396065, 721.4118, -6.3870893, -4.4827785, 3.7196853, -1.6273712, 4.770087, 14.569607], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 72, "left_children": [1, 3, -1, 5, 7, 9, -1, 11, 13, -1, -1, -1, -1, -1, -1], "loss_changes": [325723780.0, 23487044.0, 0.0, 1010732.0, 7285491.5, 328154.0, 0.0, 954468.6, 3259774.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 7, 7, 8, 8], "right_children": [2, 4, -1, 6, 8, 10, -1, 12, 14, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, -0.19222529, 72.41249, 0.8281771, -0.009964261, -0.26600468, -0.0054459535, -0.4404167, 0.8306982, -6.3870893, -4.4827785, 3.7196853, -1.6273712, 4.770087, 14.569607], "split_indices": [29, 29, 0, 21, 20, 29, 0, 25, 29, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [141.0, 136.0, 5.0, 92.0, 44.0, 89.0, 3.0, 23.0, 21.0, 64.0, 25.0, 3.0, 20.0, 17.0, 4.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-44.267895, -273.51242, 50.828876, -514.0506, 655.45184, -586.4969, -65.350044, 979.1389, 142.26587, -6.765554, -5.042085, 2.6310437, -2.3857515, 4.9708815, 17.80034, -4.2978644, 3.8071513], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 73, "left_children": [1, 3, -1, 5, 7, 9, 11, 13, 15, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [164766510.0, 30279408.0, 0.0, 3439822.0, 4511716.0, 340548.0, 973112.7, 5678165.0, 1800486.8, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8], "right_children": [2, 4, -1, 6, 8, 10, 12, 14, 16, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, -0.14510837, 50.828876, -0.15922968, 4.0, -0.29254115, -0.6880392, 0.41031995, 0.21210697, -6.765554, -5.042085, 2.6310437, -2.3857515, 4.9708815, 17.80034, -4.2978644, 3.8071513], "split_indices": [29, 18, 0, 29, 8, 29, 28, 18, 26, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [138.0, 133.0, 5.0, 106.0, 27.0, 91.0, 15.0, 16.0, 11.0, 40.0, 51.0, 5.0, 10.0, 11.0, 5.0, 3.0, 8.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "17", "size_leaf_vector": "1"}}, {"base_weights": [22.151514, -238.64374, 68.50577, -485.74777, 647.2273, -605.7706, -187.44077, 336.77734, 1299.6852, -6.508949, -4.793703, -3.7969682, 1.0795974, 0.897171, 8.180668, 17.404753, 5.989382], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 74, "left_children": [1, 3, -1, 5, 7, 9, 11, 13, 15, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [242046350.0, 28990572.0, 0.0, 3574154.0, 5436243.0, 104238.0, 1801588.0, 2423572.0, 1737269.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8], "right_children": [2, 4, -1, 6, 8, 10, 12, 14, 16, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [2.1903315, -0.0155352615, 68.50577, -0.31818703, 1.9500875, -0.39440298, -0.19460368, 0.44564942, 7.0, -6.508949, -4.793703, -3.7969682, 1.0795974, 0.897171, 8.180668, 17.404753, 5.989382], "split_indices": [29, 29, 0, 27, 27, 26, 29, 24, 11, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [134.0, 130.0, 4.0, 102.0, 28.0, 72.0, 30.0, 20.0, 8.0, 50.0, 22.0, 18.0, 12.0, 14.0, 6.0, 4.0, 4.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "17", "size_leaf_vector": "1"}}, {"base_weights": [-2.2715604, -270.86197, 63.5473, -389.67126, 1528.1609, -558.29596, 67.48522, 7.1921415, 18.127485, -6.0569983, -2.774886, -3.0404043, 1.9776634], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 75, "left_children": [1, 3, -1, 5, 7, 9, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [252712820.0, 30946534.0, 0.0, 10367500.0, 767938.0, 1136350.0, 1851021.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, -1, 6, 8, 10, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, 1.9500875, 63.5473, -0.18306975, -0.89633024, -0.2197353, 0.13784045, 7.1921415, 18.127485, -6.0569983, -2.774886, -3.0404043, 1.9776634], "split_indices": [29, 27, 0, 29, 22, 27, 26, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [146.0, 141.0, 5.0, 133.0, 8.0, 97.0, 36.0, 3.0, 5.0, 82.0, 15.0, 9.0, 27.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [108.615326, -340.63083, 4779.2197, -550.6606, 521.51056, 3.1326184, 6233.054, -621.48816, -392.80655, 284.97446, 14.106402, 29.385382, 96.1654, -6.3165026, -3.009566, -0.9974538, -4.179465, 0.6617326, 5.693456], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 76, "left_children": [1, 3, 5, 7, 9, -1, 11, 13, 15, 17, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [286990560.0, 22921594.0, 75959970.0, 884996.0, 4855616.0, 0.0, 72062690.0, 31304.0, 208756.0, 1268897.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 6, 6, 7, 7, 8, 8, 9, 9], "right_children": [2, 4, 6, 8, 10, -1, 12, 14, 16, 18, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [0.6820698, 0.009196514, 0.17150046, -0.41024357, 1.5831932, 3.1326184, 4.0, 2.0, -0.3907906, -0.104928374, 14.106402, 29.385382, 96.1654, -6.3165026, -3.009566, -0.9974538, -4.179465, 0.6617326, 5.693456], "split_indices": [18, 27, 24, 27, 27, 0, 13, 3, 27, 18, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [135.0, 124.0, 11.0, 100.0, 24.0, 3.0, 8.0, 67.0, 33.0, 20.0, 4.0, 5.0, 3.0, 64.0, 3.0, 3.0, 30.0, 12.0, 8.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "19", "size_leaf_vector": "1"}}, {"base_weights": [90.83784, -280.5283, 5857.5625, -512.23, 429.2091, 18.940111, 86.49581, -562.8872, 30.357725, 184.28262, 1023.0871, -6.1313567, -4.171593, -2.9325862, 5.8912187, 0.23028709, 4.3997602, 5.7359605, 12.271317], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 77, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, 15, 17, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [318595620.0, 23242496.0, 83213280.0, 2930654.0, 4902317.0, 0.0, 0.0, 454414.0, 1981047.0, 1061842.2, 213105.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9, 10, 10], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, 16, 18, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, 0.0526399, 0.49706092, 0.4472843, 1.455611, 18.940111, 86.49581, -0.25530338, -0.040956832, 0.2040027, 0.18499485, -6.1313567, -4.171593, -2.9325862, 5.8912187, 0.23028709, 4.3997602, 5.7359605, 12.271317], "split_indices": [29, 27, 21, 26, 27, 0, 0, 29, 22, 25, 29, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [147.0, 139.0, 8.0, 105.0, 34.0, 4.0, 4.0, 96.0, 9.0, 25.0, 9.0, 69.0, 27.0, 6.0, 3.0, 16.0, 9.0, 4.0, 5.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "19", "size_leaf_vector": "1"}}, {"base_weights": [-74.13079, -259.66672, 39.581528, -399.62524, 1149.8859, -545.693, 51.895535, 4.856677, 15.549321, -5.7866673, -2.8313296, 4.7743855, -0.7437933], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 78, "left_children": [1, 3, -1, 5, 7, 9, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [102025460.0, 26105708.0, 0.0, 7876730.0, 2473083.0, 615258.0, 1647613.6, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, -1, 6, 8, 10, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [2.8455322, 1.1800077, 39.581528, -0.18306975, 0.33892554, -0.2197353, -0.76370597, 4.856677, 15.549321, -5.7866673, -2.8313296, 4.7743855, -0.7437933], "split_indices": [25, 27, 0, 29, 20, 27, 23, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [134.0, 129.0, 5.0, 118.0, 11.0, 89.0, 29.0, 5.0, 6.0, 78.0, 11.0, 6.0, 23.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [27.484045, -238.23444, 61.34445, -424.4057, 819.2105, -528.67737, 29.2631, 431.41388, 15.331589, -6.04773, -3.8504915, -1.9241279, 3.9548256, -2.6999652, 6.292745], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 79, "left_children": [1, 3, -1, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [233512750.0, 27515128.0, 0.0, 5597486.0, 5152580.0, 816264.0, 1943302.9, 2251660.5, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7], "right_children": [2, 4, -1, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, 0.092575856, 61.34445, -0.20835175, 2.1555429, -0.43151668, 1.0, -0.56870174, 15.331589, -6.04773, -3.8504915, -1.9241279, 3.9548256, -2.6999652, 6.292745], "split_indices": [29, 20, 0, 20, 27, 27, 5, 17, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [142.0, 137.0, 5.0, 117.0, 20.0, 95.0, 22.0, 14.0, 6.0, 60.0, 35.0, 14.0, 8.0, 3.0, 11.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [114.607864, -333.24637, 4137.1167, -520.0382, 370.9141, 2021.4003, 75.411064, -546.5402, -146.93544, 121.63603, 672.36694, 31.83246, 7.1627865, -6.132798, -4.508949, -3.6830385, 0.59526384, 3.7206361, -1.1447028, 4.517737, 8.908228], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 80, "left_children": [1, 3, 5, 7, 9, 11, -1, 13, 15, 17, 19, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [251787200.0, 16765867.0, 85581740.0, 908418.0, 1947067.2, 12883024.0, 0.0, 317020.0, 387587.75, 988656.56, 175694.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 8, 8, 9, 9, 10, 10], "right_children": [2, 4, 6, 8, 10, 12, -1, 14, 16, 18, 20, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [0.5547066, 0.0526399, 7.0, -0.15545082, 0.092575856, -1.0127255, 75.411064, -0.47049406, 0.8186169, 2.0, 1.5831932, 31.83246, 7.1627865, -6.132798, -4.508949, -3.6830385, 0.59526384, 3.7206361, -1.1447028, 4.517737, 8.908228], "split_indices": [18, 27, 13, 18, 20, 22, 0, 27, 26, 8, 27, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [138.0, 125.0, 13.0, 99.0, 26.0, 9.0, 4.0, 92.0, 7.0, 15.0, 11.0, 4.0, 5.0, 51.0, 41.0, 3.0, 4.0, 7.0, 8.0, 7.0, 4.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "21", "size_leaf_vector": "1"}}, {"base_weights": [74.27646, -275.54398, 5880.769, -481.6769, 591.29456, 11.845725, 84.615524, -520.5469, -48.6867, 856.3032, 81.64028, -5.771655, -3.8396194, -1.4802547, 1.3730283, 4.7014904, 11.473015, -1.8488944, 3.8516786], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 81, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, 15, 17, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [285993120.0, 24049772.0, 86934660.0, 1792272.0, 3441630.0, 0.0, 0.0, 543888.0, 205085.1, 1370729.0, 880224.7, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9, 10, 10], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, 16, 18, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, -0.009964261, 0.49706092, 0.42965758, 4.0, 11.845725, 84.615524, -0.25051507, 0.15533018, 0.33892554, 0.32695737, -5.771655, -3.8396194, -1.4802547, 1.3730283, 4.7014904, 11.473015, -1.8488944, 3.8516786], "split_indices": [29, 20, 21, 21, 8, 0, 0, 29, 25, 20, 24, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [139.0, 132.0, 7.0, 107.0, 25.0, 3.0, 4.0, 98.0, 9.0, 16.0, 9.0, 67.0, 31.0, 6.0, 3.0, 8.0, 8.0, 5.0, 4.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "19", "size_leaf_vector": "1"}}, {"base_weights": [65.061005, -315.7045, 5642.404, -517.672, 331.61816, 17.624464, 83.9386, -588.9216, -389.3004, 160.54335, 12.232936, -5.9994617, -2.7629676, -4.1622467, -0.49631888, -0.46966034, 6.0384827], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 82, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, 15, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [299094200.0, 17429124.0, 81285060.0, 694424.0, 4684853.0, 0.0, 0.0, 51284.0, 335963.0, 2604134.2, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, 16, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.1965036, 0.0526399, 0.49706092, -0.43151668, 1.9500875, 17.624464, 83.9386, 2.0, -0.0155352615, 0.9795277, 12.232936, -5.9994617, -2.7629676, -4.1622467, -0.49631888, -0.46966034, 6.0384827], "split_indices": [29, 27, 21, 27, 27, 0, 0, 3, 29, 26, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [139.0, 131.0, 8.0, 100.0, 31.0, 4.0, 4.0, 62.0, 38.0, 27.0, 4.0, 59.0, 3.0, 35.0, 3.0, 19.0, 8.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "17", "size_leaf_vector": "1"}}, {"base_weights": [46.60828, -281.65723, 5785.441, -445.6579, 571.9792, 11.624699, 83.2671, -522.4182, -86.95291, 830.01025, -136.3648, -5.7086816, -3.6815221, -2.1719427, 3.9928658, 4.561149, 11.936155, 0.5857883, -2.9724224], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 83, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, 15, 17, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [278557600.0, 19854466.0, 84305180.0, 3203562.0, 4317074.0, 0.0, 0.0, 514738.0, 1479931.5, 1766565.0, 236970.22, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9, 10, 10], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, 16, 18, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, -0.031931423, 0.49706092, -0.18306975, 5.0, 11.624699, 83.2671, -0.33963278, 0.05719311, 0.33892554, 0.41567117, -5.7086816, -3.6815221, -2.1719427, 3.9928658, 4.561149, 11.936155, 0.5857883, -2.9724224], "split_indices": [29, 20, 21, 29, 8, 0, 0, 27, 22, 20, 27, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [146.0, 139.0, 7.0, 117.0, 22.0, 3.0, 4.0, 96.0, 21.0, 16.0, 6.0, 71.0, 25.0, 17.0, 4.0, 9.0, 7.0, 3.0, 3.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "19", "size_leaf_vector": "1"}}, {"base_weights": [71.55606, -254.35922, 6579.654, -450.86862, 820.1353, 22.743574, 92.400116, -518.90784, 96.48297, 372.60275, 1425.1599, -5.8276067, -3.9426668, 2.2226496, -1.743576, -2.3994815, 5.614846, 7.583861, 16.321323], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 84, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, 15, 17, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [311353120.0, 29930498.0, 59159200.0, 4481830.0, 5425703.0, 0.0, 0.0, 613762.0, 515694.56, 1754556.4, 4010.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9, 10, 10], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, 16, 18, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, 0.04001509, 4.0, 0.0526399, 1.9500875, 22.743574, 92.400116, -0.4363957, 6.0, 0.8186169, -0.86693746, -5.8276067, -3.9426668, 2.2226496, -1.743576, -2.3994815, 5.614846, 7.583861, 16.321323], "split_indices": [29, 20, 13, 27, 27, 0, 0, 27, 14, 26, 28, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [145.0, 139.0, 6.0, 118.0, 21.0, 3.0, 3.0, 105.0, 13.0, 13.0, 8.0, 67.0, 38.0, 9.0, 4.0, 3.0, 10.0, 3.0, 5.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "19", "size_leaf_vector": "1"}}, {"base_weights": [16.574877, -308.6798, 5822.3403, -495.6347, 356.68307, 11.018656, 90.87205, -5.8439403, -368.51242, 38.93327, 924.72943, -4.189575, 0.12375494, 3.5748165, -1.3557996, 5.294442, 13.852711], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 85, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, 11, 13, 15, -1, -1, -1, -1, -1, -1], "loss_changes": [249171950.0, 15725906.0, 97868060.0, 890310.0, 5017806.0, 0.0, 0.0, 0.0, 831425.5, 1104716.8, 1086838.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 8, 8, 9, 9, 10, 10], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, 12, 14, 16, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, 0.4472843, 0.49706092, -0.39440298, 0.23448703, 11.018656, 90.87205, -5.8439403, 0.8281771, -0.772293, -0.31147707, -4.189575, 0.12375494, 3.5748165, -1.3557996, 5.294442, 13.852711], "split_indices": [29, 26, 21, 26, 20, 0, 0, 0, 21, 23, 17, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [130.0, 124.0, 6.0, 97.0, 27.0, 3.0, 3.0, 55.0, 42.0, 18.0, 9.0, 37.0, 5.0, 6.0, 12.0, 6.0, 3.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "17", "size_leaf_vector": "1"}}, {"base_weights": [7.983395, -282.6836, 5778.673, -492.14468, 308.0482, 10.936017, 90.190506, -520.3911, 0.5122132, -221.98256, 486.3467, -5.9885497, -4.330867, -0.22290961, -3.5137844, 3.203617, 9.385215], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 86, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, -1, 13, 15, -1, -1, -1, -1, -1, -1], "loss_changes": [244849660.0, 17350832.0, 96405570.0, 1607560.0, 3604624.2, 0.0, 0.0, 411866.0, 0.0, 250522.81, 1800726.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 9, 9, 10, 10], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, -1, 14, 16, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, -0.11083611, 0.49706092, 0.8281771, -0.20219812, 10.936017, 90.190506, -0.5305583, 0.5122132, -0.54592663, 4.0, -5.9885497, -4.330867, -0.22290961, -3.5137844, 3.203617, 9.385215], "split_indices": [29, 29, 21, 21, 18, 0, 0, 26, 0, 17, 13, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [144.0, 138.0, 6.0, 102.0, 36.0, 3.0, 3.0, 97.0, 5.0, 9.0, 27.0, 48.0, 49.0, 4.0, 5.0, 21.0, 6.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "17", "size_leaf_vector": "1"}}, {"base_weights": [134.07176, -211.2217, 5413.6855, -388.0165, 916.4888, 73.96912, 10.853996, -507.40103, 63.115997, 437.22644, 1400.4106, -5.4098277, -2.0176888, -0.4913155, 3.2182693, 0.01747334, 6.858077, 6.3340964, 16.78326], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 87, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, 15, 17, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [267304380.0, 27869080.0, 69226300.0, 6495056.0, 3794104.0, 0.0, 0.0, 865822.0, 770870.5, 1189504.8, 855154.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9, 10, 10], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, 16, 18, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, 0.20579594, -0.9297316, -0.19222529, 1.9500875, 73.96912, 10.853996, -0.20826858, 0.04446191, 0.17150046, -0.89633024, -5.4098277, -2.0176888, -0.4913155, 3.2182693, 0.01747334, 6.858077, 6.3340964, 16.78326], "split_indices": [29, 20, 22, 29, 27, 0, 0, 27, 19, 24, 22, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [145.0, 137.0, 8.0, 119.0, 18.0, 5.0, 3.0, 94.0, 25.0, 10.0, 8.0, 84.0, 10.0, 18.0, 7.0, 4.0, 6.0, 3.0, 5.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "19", "size_leaf_vector": "1"}}, {"base_weights": [119.905914, -218.31137, 74.27979, -469.37903, 425.43378, -517.7754, -95.514145, 106.31188, 1081.4034, -5.7394257, -4.0504203, 3.8349822, -3.8297012, 4.891685, -0.910171, 5.233028, 12.092811], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 88, "left_children": [1, 3, -1, 5, 7, 9, 11, 13, 15, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [335304100.0, 21210758.0, 0.0, 1651294.0, 7630283.5, 292980.0, 1799207.9, 2008829.1, 223429.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8], "right_children": [2, 4, -1, 6, 8, 10, 12, 14, 16, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, 0.4472843, 74.27979, -0.11965306, 1.455611, -0.40678072, 2.0, -0.75929564, -0.87875676, -5.7394257, -4.0504203, 3.8349822, -3.8297012, 4.891685, -0.910171, 5.233028, 12.092811], "split_indices": [29, 26, 0, 27, 27, 26, 8, 23, 28, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [134.0, 129.0, 5.0, 93.0, 36.0, 82.0, 11.0, 25.0, 11.0, 52.0, 30.0, 4.0, 7.0, 8.0, 17.0, 3.0, 8.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "17", "size_leaf_vector": "1"}}, {"base_weights": [77.92594, -275.11554, 4624.3784, -415.0164, 508.08997, 6355.3896, 4.3898883, -507.1186, -140.27513, 704.73474, -2.227712, 88.402214, 22.816854, -5.7160892, -4.3208704, -2.4309566, 4.0186257, 10.126223, 3.5274189], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 89, "left_children": [1, 3, 5, 7, 9, 11, -1, 13, 15, 17, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [222774580.0, 14345530.0, 69658930.0, 2712270.0, 3031443.0, 50685570.0, 0.0, 149698.0, 1714219.0, 1376657.5, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 8, 8, 9, 9], "right_children": [2, 4, 6, 8, 10, 12, -1, 14, 16, 18, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2473981, -0.031931423, -0.9297316, -0.28688815, 5.0, -0.011381618, 4.3898883, -0.40619367, 0.43023095, -0.79761463, -2.227712, 88.402214, 22.816854, -5.7160892, -4.3208704, -2.4309566, 4.0186257, 10.126223, 3.5274189], "split_indices": [25, 20, 22, 20, 8, 17, 0, 25, 22, 28, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [137.0, 128.0, 9.0, 109.0, 19.0, 6.0, 3.0, 81.0, 28.0, 15.0, 4.0, 3.0, 3.0, 40.0, 41.0, 24.0, 4.0, 7.0, 8.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "19", "size_leaf_vector": "1"}}, {"base_weights": [47.172832, -194.30547, 60.747158, -335.14346, 1458.8358, -465.625, 212.69603, 20.916315, 4.0769377, -5.1446805, -2.1412587, -0.8700073, 4.4993205], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 90, "left_children": [1, 3, -1, 5, 7, 9, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [188943140.0, 29591946.0, 0.0, 8391082.0, 5798586.0, 1044462.0, 1674454.9, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, -1, 6, 8, 10, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [2.9190056, 1.9500875, 60.747158, 0.4472843, 4.0, -0.2358289, 3.0, 20.916315, 4.0769377, -5.1446805, -2.1412587, -0.8700073, 4.4993205], "split_indices": [18, 27, 0, 26, 11, 27, 11, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [128.0, 124.0, 4.0, 115.0, 9.0, 93.0, 22.0, 5.0, 4.0, 77.0, 16.0, 10.0, 12.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [134.16307, -220.2062, 5237.9346, -468.33685, 370.77142, 15.706971, 78.575645, -514.69476, -275.70862, 205.08081, 1098.9026, -5.556114, -3.5879877, -3.1081212, -0.5756652, 4.792461, -0.50009507, 13.260247, 5.9702973], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 91, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, 15, 17, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [250743470.0, 19236812.0, 74118450.0, 679560.0, 4479640.5, 0.0, 0.0, 266204.0, 135221.88, 2331928.0, 6037.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9, 10, 10], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, 16, 18, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, -0.20332481, 0.49706092, -0.28713, 0.858101, 15.706971, 78.575645, -0.25530338, -0.15922968, 0.032106534, 1.0, -5.556114, -3.5879877, -3.1081212, -0.5756652, 4.792461, -0.50009507, 13.260247, 5.9702973], "split_indices": [29, 20, 21, 20, 25, 0, 0, 29, 29, 24, 1, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [137.0, 129.0, 8.0, 91.0, 38.0, 4.0, 4.0, 72.0, 19.0, 32.0, 6.0, 55.0, 17.0, 16.0, 3.0, 15.0, 17.0, 3.0, 3.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "19", "size_leaf_vector": "1"}}, {"base_weights": [-63.88772, -258.68774, 2800.2666, -467.46338, 233.18031, 0.9686721, 41.35805, -483.1815, 0.030352874, -31.1192, 499.04077, -5.681513, -4.1016026, -1.3468783, 4.4269443, 6.3558397, -2.0600314], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 92, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, -1, 13, 15, -1, -1, -1, -1, -1, -1], "loss_changes": [78835360.0, 13664114.0, 32093408.0, 689282.0, 2826248.2, 0.0, 0.0, 325284.0, 0.0, 1090111.6, 2056354.5, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 9, 9, 10, 10], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, -1, 14, 16, -1, -1, -1, -1, -1, -1], "split_conditions": [0.8306982, -0.18306975, -0.46722066, 0.8281771, -0.009964261, 0.9686721, 41.35805, -0.29357222, 0.030352874, 11.0, 0.48093703, -5.681513, -4.1016026, -1.3468783, 4.4269443, 6.3558397, -2.0600314], "split_indices": [29, 29, 23, 21, 20, 0, 0, 29, 0, 11, 18, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [139.0, 131.0, 8.0, 92.0, 39.0, 3.0, 5.0, 89.0, 3.0, 20.0, 19.0, 38.0, 51.0, 17.0, 3.0, 16.0, 3.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "17", "size_leaf_vector": "1"}}, {"base_weights": [42.54474, -255.68968, 5276.886, -411.88715, 480.11627, 6.314756, 86.030495, -498.9936, -163.8525, 253.19102, 12.01004, -5.3386517, -3.5890224, -2.7125401, 1.9708837, 0.8315711, 6.7862773], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 93, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, 15, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [202726580.0, 14334264.0, 102726240.0, 2121924.0, 3294699.5, 0.0, 0.0, 174696.0, 1138905.2, 1245587.2, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, 16, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, -0.0155352615, 0.49706092, -0.28713, 0.8306982, 6.314756, 86.030495, -0.25051507, 4.0, 2.0008802, 12.01004, -5.3386517, -3.5890224, -2.7125401, 1.9708837, 0.8315711, 6.7862773], "split_indices": [29, 29, 21, 20, 29, 0, 0, 29, 0, 21, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [128.0, 122.0, 6.0, 101.0, 21.0, 3.0, 3.0, 74.0, 27.0, 17.0, 4.0, 57.0, 17.0, 21.0, 6.0, 13.0, 4.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "17", "size_leaf_vector": "1"}}, {"base_weights": [37.45046, -238.34088, 5390.346, -393.70752, 682.4661, 74.20897, 20.12184, -508.41846, -98.721504, 347.05194, 1142.1934, -5.2264647, -0.931715, -2.614829, 1.0072149, -1.3552992, 5.766028, 5.9602933, 13.506659], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 94, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, 15, 17, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [210917330.0, 19725192.0, 33083552.0, 3908808.0, 2687432.0, 0.0, 0.0, 447526.0, 1130048.2, 1518293.0, 105650.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9, 10, 10], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, 16, 18, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, 0.092575856, -0.011381618, -0.2967835, 1.9500875, 74.20897, 20.12184, -0.08898436, -0.20332481, 2.0, 0.8139121, -5.2264647, -0.931715, -2.614829, 1.0072149, -1.3552992, 5.766028, 5.9602933, 13.506659], "split_indices": [29, 20, 17, 27, 27, 0, 0, 25, 20, 11, 20, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [141.0, 135.0, 6.0, 116.0, 19.0, 3.0, 3.0, 83.0, 33.0, 12.0, 7.0, 80.0, 3.0, 18.0, 15.0, 4.0, 8.0, 3.0, 4.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "19", "size_leaf_vector": "1"}}, {"base_weights": [134.31438, -222.77356, 5786.3086, -386.26358, 788.70276, 22.467085, 87.64206, -466.44583, -44.32792, 469.49857, 15.24714, -5.676, -3.9669957, -1.5335505, 4.84608, 5.7777414, 1.5902764], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 95, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, 15, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [271736300.0, 21306122.0, 64632816.0, 2995163.0, 3512833.0, 0.0, 0.0, 402642.0, 1342988.0, 378674.25, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, 16, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2220678, -0.031931423, 4.0, -0.18306975, 2.2059762, 22.467085, 87.64206, -0.6172026, 0.05719311, 1.0, 15.24714, -5.676, -3.9669957, -1.5335505, 4.84608, 5.7777414, 1.5902764], "split_indices": [29, 20, 13, 29, 27, 0, 0, 26, 22, 10, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [133.0, 126.0, 7.0, 109.0, 17.0, 4.0, 3.0, 88.0, 21.0, 13.0, 4.0, 33.0, 55.0, 18.0, 3.0, 9.0, 4.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "17", "size_leaf_vector": "1"}}, {"base_weights": [-79.68271, -270.35165, 51.605633, -424.0458, 343.09854, 1.9752799, -443.3201, 488.82654, -6.53451, -5.148876, -3.291441, 2.7188392, 11.304808], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 96, "left_children": [1, 3, -1, 5, 7, -1, 9, 11, -1, -1, -1, -1, -1], "loss_changes": [142275140.0, 13062363.0, 0.0, 1405436.0, 4385713.0, 0.0, 704772.0, 3172554.5, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 6, 6, 7, 7], "right_children": [2, 4, -1, 6, 8, -1, 10, 12, -1, -1, -1, -1, -1], "split_conditions": [2.9190056, 0.21398789, 51.605633, -0.9199369, 0.8360413, 1.9752799, -0.4363957, 1.5831932, -6.53451, -5.148876, -3.291441, 2.7188392, 11.304808], "split_indices": [18, 27, 0, 23, 18, 0, 27, 27, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [140.0, 136.0, 4.0, 109.0, 27.0, 3.0, 106.0, 24.0, 3.0, 63.0, 43.0, 19.0, 5.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [-40.08409, -215.5134, 3499.2754, -410.3016, 386.13672, 9.119513, 52.117554, -472.17166, -163.80789, 200.4995, 12.499499, -4.8178897, -1.5694882, 5.51245, -2.9863307, 0.6697664, 4.608375], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 97, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, 15, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [92027190.0, 16682838.0, 26261712.0, 1554402.0, 5361696.5, 0.0, 0.0, 182006.0, 2381959.5, 1011914.1, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, 16, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, 0.0526399, 0.49706092, -0.21219923, 1.9500875, 9.119513, 52.117554, 2.0, -0.44094184, 0.44564942, 12.499499, -4.8178897, -1.5694882, 5.51245, -2.9863307, 0.6697664, 4.608375], "split_indices": [29, 27, 21, 29, 27, 0, 0, 3, 25, 24, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [146.0, 140.0, 6.0, 106.0, 34.0, 3.0, 3.0, 84.0, 22.0, 29.0, 5.0, 81.0, 3.0, 3.0, 19.0, 20.0, 9.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "17", "size_leaf_vector": "1"}}, {"base_weights": [148.96356, -201.33684, 4997.7065, -403.19562, 373.63602, 14.514941, 75.44357, -5.059905, -233.93602, -185.5712, 560.00916, 2.9830923, -3.0749328, -5.8346066, 1.106003, 2.964629, 9.256722], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 98, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, 11, 13, 15, -1, -1, -1, -1, -1, -1], "loss_changes": [226909400.0, 14658564.0, 70327200.0, 1499667.0, 3543259.0, 0.0, 0.0, 0.0, 1540297.8, 1125170.0, 2134679.5, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 8, 8, 9, 9, 10, 10], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, 12, 14, 16, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, 0.0526399, 0.49706092, -0.41024357, 0.19972922, 14.514941, 75.44357, -5.059905, -0.8276071, -0.88444257, 1.455611, 2.9830923, -3.0749328, -5.8346066, 1.106003, 2.964629, 9.256722], "split_indices": [29, 27, 21, 27, 26, 0, 0, 0, 23, 22, 27, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [132.0, 124.0, 8.0, 92.0, 32.0, 4.0, 4.0, 56.0, 36.0, 8.0, 24.0, 4.0, 32.0, 3.0, 5.0, 15.0, 9.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "17", "size_leaf_vector": "1"}}, {"base_weights": [7.1110463, -240.0009, 2971.8171, -419.21292, 383.29037, 53.496166, 1411.4276, -501.72955, -251.70058, 226.60023, 13.451012, 22.436394, 0.1828103, -5.1660895, -2.9825096, 2.0919168, -2.9994204, -1.8499031, 3.7991142], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 99, "left_children": [1, 3, 5, 7, 9, -1, 11, 13, 15, 17, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [104744740.0, 14905254.0, 33261960.0, 1297362.0, 4216231.0, 0.0, 9233900.0, 51718.0, 863182.5, 1774038.2, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 6, 6, 7, 7, 8, 8, 9, 9], "right_children": [2, 4, 6, 8, 10, -1, 12, 14, 16, 18, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [0.57345134, 0.02337902, 1.0741702, -0.41024357, 2.1555429, 53.496166, 3.0, -0.2271967, -0.6772184, 0.19972922, 13.451012, 22.436394, 0.1828103, -5.1660895, -2.9825096, 2.0919168, -2.9994204, -1.8499031, 3.7991142], "split_indices": [29, 27, 26, 27, 27, 0, 0, 18, 17, 26, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [141.0, 131.0, 10.0, 102.0, 29.0, 3.0, 7.0, 67.0, 35.0, 26.0, 3.0, 4.0, 3.0, 61.0, 6.0, 3.0, 32.0, 7.0, 19.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "19", "size_leaf_vector": "1"}}, {"base_weights": [65.69993, -271.72824, 4932.3735, -436.92456, 269.9253, 67.59362, 21.1889, 0.37147376, -451.97058, -22.664108, 803.97546, -5.093103, -3.4521935, 5.1639366, -1.4120115, 9.780512, 4.958871], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 100, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, 11, 13, 15, -1, -1, -1, -1, -1, -1], "loss_changes": [227957810.0, 11750245.0, 31938592.0, 730120.0, 4862280.0, 0.0, 0.0, 0.0, 417838.0, 1414742.8, 104786.5, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 8, 8, 9, 9, 10, 10], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, 12, 14, 16, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, 0.4472843, 4.1228113, -0.8636659, 0.14085093, 67.59362, 21.1889, 0.37147376, -0.39440298, 0.49679533, 2.0, -5.093103, -3.4521935, 5.1639366, -1.4120115, 9.780512, 4.958871], "split_indices": [29, 26, 25, 28, 20, 0, 0, 0, 26, 26, 8, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [137.0, 129.0, 8.0, 99.0, 30.0, 4.0, 4.0, 3.0, 96.0, 20.0, 10.0, 60.0, 36.0, 3.0, 17.0, 5.0, 5.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "17", "size_leaf_vector": "1"}}, {"base_weights": [117.587074, -232.78232, 4892.9146, -412.32867, 565.777, 13.891093, 74.18117, 2.3034844, -441.20657, 299.9165, 11.305986, -4.5713053, 0.4328298, 4.089174, -1.565407], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 101, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, 11, 13, -1, -1, -1, -1, -1], "loss_changes": [220319310.0, 17868816.0, 69324900.0, 1976128.0, 3114539.5, 0.0, 0.0, 0.0, 768180.0, 909859.5, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 8, 8, 9, 9], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, 12, 14, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, -0.08539489, 0.49706092, -0.89072937, 1.9500875, 13.891093, 74.18117, 2.3034844, 0.42965758, 1.4594278, 11.305986, -4.5713053, 0.4328298, 4.089174, -1.565407], "split_indices": [29, 19, 21, 23, 27, 0, 0, 0, 21, 19, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [130.0, 122.0, 8.0, 100.0, 22.0, 4.0, 4.0, 4.0, 96.0, 16.0, 6.0, 93.0, 3.0, 13.0, 3.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [88.40187, -212.13397, 4853.771, -301.31192, 13.303955, 13.779964, 73.587715, -442.22623, 73.73355, -4.7461166, -1.9146764, -0.69488686, 2.7501342], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 102, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, 11, -1, -1, -1, -1], "loss_changes": [217286910.0, 20071788.0, 68220100.0, 7220145.0, 0.0, 0.0, 0.0, 713032.0, 1114585.5, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 8, 8], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, 12, -1, -1, -1, -1], "split_conditions": [1.2440649, 1.9500875, 0.49706092, -0.18306975, 13.303955, 13.779964, 73.587715, -0.2197353, -0.009964261, -4.7461166, -1.9146764, -0.69488686, 2.7501342], "split_indices": [29, 27, 21, 29, 0, 0, 0, 27, 20, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [150.0, 142.0, 8.0, 135.0, 7.0, 4.0, 4.0, 98.0, 37.0, 86.0, 12.0, 22.0, 15.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [128.61966, -176.7009, 4959.5186, -393.10773, 536.1148, 13.669726, 82.10296, -458.9341, -144.8424, 223.39151, 1240.9701, -5.0317154, -3.809979, 3.4024515, -2.3934808, -2.4310853, 3.7490377, 5.278001, 15.095717], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 103, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, 15, 17, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [198523170.0, 19801526.0, 82204350.0, 1535023.0, 6335372.0, 0.0, 0.0, 73676.0, 1089984.5, 1646126.1, 927072.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9, 10, 10], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, 16, 18, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, 0.21398789, 0.49706092, -0.22761919, 1.9500875, 13.669726, 82.10296, -0.48221627, -0.44094184, -0.52717066, -0.89633024, -5.0317154, -3.809979, 3.4024515, -2.3934808, -2.4310853, 3.7490377, 5.278001, 15.095717], "split_indices": [29, 27, 21, 29, 27, 0, 0, 27, 25, 17, 22, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [133.0, 126.0, 7.0, 97.0, 29.0, 4.0, 3.0, 76.0, 21.0, 21.0, 8.0, 45.0, 31.0, 3.0, 18.0, 5.0, 16.0, 3.0, 5.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "19", "size_leaf_vector": "1"}}, {"base_weights": [161.46252, -171.81412, 4772.3755, -351.00943, 596.9207, 13.560368, 72.34219, -448.71854, -112.746056, 253.26375, 1252.5642, -4.963886, -3.4528341, 1.9337199, -2.100918, 0.15976694, 4.623993, 15.245631, 5.993994], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 104, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, 15, 17, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [205202800.0, 17428710.0, 65883696.0, 2323964.0, 5090214.0, 0.0, 0.0, 183535.0, 964405.6, 836192.0, 507247.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9, 10, 10], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, 16, 18, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, -0.009964261, 0.49706092, -0.2967835, 1.9500875, 13.560368, 72.34219, -0.2864904, -0.26642376, 0.17150046, 7.0, -4.963886, -3.4528341, 1.9337199, -2.100918, 0.15976694, 4.623993, 15.245631, 5.993994], "split_indices": [29, 20, 21, 27, 27, 0, 0, 18, 24, 24, 11, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [132.0, 124.0, 8.0, 101.0, 23.0, 4.0, 4.0, 71.0, 30.0, 16.0, 7.0, 46.0, 25.0, 7.0, 23.0, 8.0, 8.0, 4.0, 3.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "19", "size_leaf_vector": "1"}}, {"base_weights": [74.02231, -205.80162, 4716.855, -398.90833, 368.20935, 19.59093, 69.84818, -452.14206, -177.1128, 116.093155, 1055.6217, -5.354682, -3.9958782, 1.5065501, -3.5176492, 4.5375376, -0.59181225, 12.960127, 4.3110476], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 105, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, 15, 17, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [182855780.0, 14889320.0, 36351216.0, 1100556.0, 5769797.0, 0.0, 0.0, 136271.0, 1255167.6, 1565657.9, 792264.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9, 10, 10], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, 16, 18, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2432504, 0.4472843, 4.0, -0.2197353, 1.455611, 19.59093, 69.84818, -0.81524664, 2.0, -0.75929564, 5.0, -5.354682, -3.9958782, 1.5065501, -3.5176492, 4.5375376, -0.59181225, 12.960127, 4.3110476], "split_indices": [29, 26, 13, 27, 27, 0, 0, 26, 8, 23, 11, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [139.0, 132.0, 7.0, 99.0, 33.0, 4.0, 3.0, 79.0, 20.0, 25.0, 8.0, 27.0, 52.0, 7.0, 13.0, 8.0, 17.0, 5.0, 3.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "19", "size_leaf_vector": "1"}}, {"base_weights": [95.79803, -194.4978, 4278.327, -313.08426, 1434.6073, 69.42759, 7.5820975, -418.18442, 91.05801, 5.1163764, 18.108025, -5.367007, -3.6957736, 2.4306366, -2.8132515], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 106, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, -1, -1, -1, -1, -1, -1], "loss_changes": [168388080.0, 25563716.0, 79147180.0, 5220112.0, 2198242.0, 0.0, 0.0, 360346.0, 1540089.1, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2473981, 1.9500875, -0.6227321, 0.0526399, -0.89633024, 69.42759, 7.5820975, -0.81524664, 5.0, 5.1163764, 18.108025, -5.367007, -3.6957736, 2.4306366, -2.8132515], "split_indices": [25, 27, 28, 27, 22, 0, 0, 26, 8, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [137.0, 129.0, 8.0, 121.0, 8.0, 4.0, 4.0, 96.0, 25.0, 3.0, 5.0, 25.0, 71.0, 18.0, 7.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [108.518555, -205.34679, 3750.9136, -358.3125, 508.54935, 5053.324, 2.0833876, -419.16586, -37.51459, 229.74585, 1002.5752, 65.72966, 18.904165, -4.502161, -2.4935446, 5.083499, -1.8058413, -3.6464937, 3.949716, 5.078003, 11.9786], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 107, "left_children": [1, 3, 5, 7, 9, 11, -1, 13, 15, 17, 19, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [158535170.0, 14160013.0, 49699424.0, 2053522.0, 2937470.0, 26025392.0, 0.0, 356936.0, 1497505.6, 1715380.0, 164531.5, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 8, 8, 9, 9, 10, 10], "right_children": [2, 4, 6, 8, 10, 12, -1, 14, 16, 18, 20, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2473981, -0.04208925, -0.9297316, 0.4472843, 1.9500875, 1.9326148, 2.0833876, -0.2684185, 0.49679533, -0.9617159, 0.8139121, 65.72966, 18.904165, -4.502161, -2.4935446, 5.083499, -1.8058413, -3.6464937, 3.949716, 5.078003, 11.9786], "split_indices": [25, 20, 22, 26, 27, 26, 0, 27, 26, 22, 20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [137.0, 127.0, 10.0, 105.0, 22.0, 7.0, 3.0, 88.0, 17.0, 15.0, 7.0, 4.0, 3.0, 73.0, 15.0, 3.0, 14.0, 3.0, 12.0, 3.0, 4.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "21", "size_leaf_vector": "1"}}, {"base_weights": [46.03409, -204.84512, 4591.1377, -383.5824, 361.0343, 61.98089, 18.36377, 2.0515032, -404.55527, 527.81616, -6.1177454, -4.6341343, -3.0135443, 2.9419155, 10.458881], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 108, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, 11, 13, -1, -1, -1, -1, -1], "loss_changes": [152597090.0, 12983747.0, 19604528.0, 1280714.0, 5352549.0, 0.0, 0.0, 0.0, 431247.0, 2960102.5, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 8, 8, 9, 9], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, 12, 14, -1, -1, -1, -1, -1], "split_conditions": [2.8577383, 0.02337902, 2.0, -0.9199369, 1.4594278, 61.98089, 18.36377, 2.0515032, -0.4363957, 1.455611, -6.1177454, -4.6341343, -3.0135443, 2.9419155, 10.458881], "split_indices": [20, 27, 8, 23, 19, 0, 0, 0, 27, 27, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [132.0, 126.0, 6.0, 96.0, 30.0, 3.0, 3.0, 3.0, 93.0, 26.0, 4.0, 57.0, 36.0, 19.0, 7.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [58.071857, -232.3279, 4601.2266, -285.8298, 12.800701, 12.558718, 70.26316, -410.77853, 76.12856, -5.088194, -3.306993, 4.5006256, -0.4677981], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 109, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, 11, -1, -1, -1, -1], "loss_changes": [197649760.0, 11774965.0, 64190080.0, 6227301.0, 0.0, 0.0, 0.0, 632456.0, 1675640.4, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 8, 8], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, 12, -1, -1, -1, -1], "split_conditions": [1.2432504, 1.9500875, 0.49706092, -0.18306975, 12.800701, 12.558718, 70.26316, -0.29357222, -0.75929564, -5.088194, -3.306993, 4.5006256, -0.4677981], "split_indices": [29, 27, 21, 29, 0, 0, 0, 29, 23, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [148.0, 140.0, 8.0, 136.0, 4.0, 4.0, 4.0, 101.0, 35.0, 43.0, 58.0, 8.0, 27.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [11.487326, -177.20059, 52.924717, -244.1359, 21.596088, -369.77847, 197.40576, -4.0768304, -0.3571872, 3.3375325, -4.263177], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 110, "left_children": [1, 3, -1, 5, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [144428270.0, 22425156.0, 0.0, 7673285.0, 0.0, 1340269.0, 2778613.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6], "right_children": [2, 4, -1, 6, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [2.1903315, 3.5387294, 52.924717, 0.0526399, 21.596088, 0.4472843, 1.4532615, -4.0768304, -0.3571872, 3.3375325, -4.263177], "split_indices": [29, 27, 0, 27, 0, 26, 18, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [143.0, 139.0, 4.0, 136.0, 3.0, 106.0, 30.0, 95.0, 11.0, 25.0, 5.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [59.600246, -186.04993, 4893.918, -330.0858, 792.5216, 7.0772934, 78.56602, -445.79004, -117.46948, 389.03885, 12.4823265, -5.18627, -3.9209611, -2.1130037, 0.599346, 5.13099, 2.0012221], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 111, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, 15, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [171918320.0, 19712568.0, 81255300.0, 2924251.0, 2823914.0, 0.0, 0.0, 101607.0, 745102.7, 155053.38, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, 16, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, 0.092575856, 0.49706092, -0.25019056, 1.8196839, 7.0772934, 78.56602, -0.81524664, 0.02337902, -0.6716731, 12.4823265, -5.18627, -3.9209611, -2.1130037, 0.599346, 5.13099, 2.0012221], "split_indices": [29, 20, 21, 29, 27, 0, 0, 26, 27, 23, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [143.0, 137.0, 6.0, 120.0, 17.0, 3.0, 3.0, 77.0, 43.0, 10.0, 7.0, 29.0, 48.0, 28.0, 15.0, 5.0, 5.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "17", "size_leaf_vector": "1"}}, {"base_weights": [91.0182, -181.5072, 5489.6055, -365.16263, 419.47885, 18.091076, 77.97677, -412.98315, -44.324684, 209.9838, 1047.8904, -4.7426105, -3.3396087, 5.2735004, -2.3119752, -0.8669679, 3.0332782, 4.7468185, 12.968591], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 112, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, 15, 17, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [214328500.0, 15495208.0, 45356176.0, 1623310.0, 4124254.5, 0.0, 0.0, 291870.0, 1724349.9, 746346.5, 525915.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9, 10, 10], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, 16, 18, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, -0.1536302, 4.0, -0.18306975, 1.9500875, 18.091076, 77.97677, -0.49849156, -0.5392281, -0.56870174, -0.89633024, -4.7426105, -3.3396087, 5.2735004, -2.3119752, -0.8669679, 3.0332782, 4.7468185, 12.968591], "split_indices": [29, 20, 11, 29, 27, 0, 0, 27, 17, 17, 22, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [144.0, 138.0, 6.0, 106.0, 32.0, 3.0, 3.0, 92.0, 14.0, 25.0, 7.0, 49.0, 43.0, 3.0, 11.0, 6.0, 19.0, 3.0, 4.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "19", "size_leaf_vector": "1"}}, {"base_weights": [70.80266, -212.92528, 4601.612, -366.7574, 404.67215, 12.005141, 77.02556, -437.92136, -178.99098, 25.255304, 919.7537, -4.4823513, -1.4534662, -3.7285419, -0.6341168, 1.2139634, -2.9349186, 5.2639556, 12.864842], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 113, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, 15, 17, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [174525040.0, 12309222.0, 75125010.0, 1297860.0, 5057872.0, 0.0, 0.0, 158090.0, 648510.2, 525926.56, 909441.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9, 10, 10], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, 16, 18, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, 0.4472843, 0.49706092, -0.25530338, 0.1790324, 12.005141, 77.02556, 0.23686248, 2.0, 0.41726226, -0.8306949, -4.4823513, -1.4534662, -3.7285419, -0.6341168, 1.2139634, -2.9349186, 5.2639556, 12.864842], "split_indices": [29, 26, 21, 29, 25, 0, 0, 26, 13, 24, 22, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [134.0, 127.0, 7.0, 102.0, 25.0, 4.0, 3.0, 73.0, 29.0, 15.0, 10.0, 70.0, 3.0, 10.0, 19.0, 12.0, 3.0, 6.0, 4.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "19", "size_leaf_vector": "1"}}, {"base_weights": [16.143131, -229.012, 4776.865, -338.52664, 463.17963, 76.94611, 6.6487765, -410.62735, -67.60385, 565.22174, -0.60758686, -4.929649, -3.47421, 3.4485211, -1.3640038, 2.5163922, 7.281973], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 114, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, 15, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [166824780.0, 10466283.0, 78867340.0, 2277171.0, 1050204.2, 0.0, 0.0, 312646.0, 784781.06, 634359.5, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, 16, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, 0.092575856, -0.9297316, -0.11261157, 1.1764319, 76.94611, 6.6487765, -0.29357222, -0.09649406, 0.33892554, -0.60758686, -4.929649, -3.47421, 3.4485211, -1.3640038, 2.5163922, 7.281973], "split_indices": [29, 20, 22, 25, 18, 0, 0, 29, 25, 20, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [141.0, 135.0, 6.0, 117.0, 18.0, 3.0, 3.0, 92.0, 25.0, 15.0, 3.0, 37.0, 55.0, 3.0, 22.0, 6.0, 9.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "17", "size_leaf_vector": "1"}}, {"base_weights": [67.44828, -179.62376, 56.97702, -294.3344, 595.08746, -381.83817, 21.2058, 352.9672, 13.542416, -3.9458547, 0.03598123, 1.7295445, -2.1523354, 4.9992695, -1.7612888], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 115, "left_children": [1, 3, -1, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [198557100.0, 12349990.0, 0.0, 3321447.0, 2830344.5, 463309.0, 1005963.94, 1254421.5, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7], "right_children": [2, 4, -1, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2432504, 0.092575856, 56.97702, -0.18306975, 2.2059762, 0.8281771, 0.0222285, 1.4532615, 13.542416, -3.9458547, 0.03598123, 1.7295445, -2.1523354, 4.9992695, -1.7612888], "split_indices": [29, 20, 0, 29, 27, 21, 23, 18, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [141.0, 136.0, 5.0, 119.0, 17.0, 93.0, 26.0, 14.0, 3.0, 90.0, 3.0, 16.0, 10.0, 11.0, 3.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [2.213608, -211.2379, 4301.435, -372.34378, 251.66086, 58.725254, 16.549608, -386.09164, -0.20147932, 345.9709, -6.128647, -4.950871, -3.4372225, 1.2578814, 8.618207], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 116, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, -1, 13, -1, -1, -1, -1, -1], "loss_changes": [135807310.0, 10608891.0, 19385392.0, 500585.0, 3228744.2, 0.0, 0.0, 296169.0, 0.0, 3753256.2, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 9, 9], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, -1, 14, -1, -1, -1, -1, -1], "split_conditions": [2.8577383, -0.11083611, 2.0, 0.8281771, 1.1764319, 58.725254, 16.549608, -0.81524664, -0.20147932, 0.33892554, -6.128647, -4.950871, -3.4372225, 1.2578814, 8.618207], "split_indices": [20, 29, 8, 21, 18, 0, 0, 26, 0, 20, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [146.0, 140.0, 6.0, 104.0, 36.0, 3.0, 3.0, 100.0, 4.0, 33.0, 3.0, 25.0, 75.0, 24.0, 9.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-75.014755, -243.55, 1379.3826, -374.0385, 117.588806, 1947.3999, 2.0277338, -420.04022, -220.44421, 176.53252, -3.8333304, 8.920531, 25.022722, -4.2909827, -1.5875337, -2.6630917, 0.75432897, -1.2410867, 3.3992593], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 117, "left_children": [1, 3, 5, 7, 9, 11, -1, 13, 15, 17, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [35387596.0, 6123064.5, 9629922.0, 578573.0, 1101066.8, 3623328.0, 0.0, 102226.0, 345797.12, 1614138.8, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 8, 8, 9, 9], "right_children": [2, 4, 6, 8, 10, 12, -1, 14, 16, 18, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.5831932, -0.19222529, 7.0, -0.25051507, 0.45794836, -0.2927211, 2.0277338, 2.0, -0.017417625, -0.20332481, -3.8333304, 8.920531, 25.022722, -4.2909827, -1.5875337, -2.6630917, 0.75432897, -1.2410867, 3.3992593], "split_indices": [27, 29, 11, 29, 29, 17, 0, 3, 27, 20, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [142.0, 128.0, 14.0, 94.0, 34.0, 9.0, 5.0, 71.0, 23.0, 31.0, 3.0, 4.0, 5.0, 68.0, 3.0, 20.0, 3.0, 11.0, 20.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "19", "size_leaf_vector": "1"}}, {"base_weights": [76.060776, -190.74478, 4268.7534, -380.97656, 335.20224, 2.565441, 66.2475, -440.6478, -304.50748, 174.45758, 10.56251, -4.5536256, -1.5756272, -2.2342482, -3.4728081, -0.8974392, 3.1823962], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 118, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, 15, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [149592940.0, 12734240.0, 73921790.0, 290104.0, 3756367.5, 0.0, 0.0, 136466.0, 74951.75, 1130218.2, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, 16, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2432504, -0.18306975, 0.49706092, -0.48221627, 1.9500875, 2.565441, 66.2475, 2.0, 2.0, 3.0, 10.56251, -4.5536256, -1.5756272, -2.2342482, -3.4728081, -0.8974392, 3.1823962], "split_indices": [29, 29, 21, 27, 27, 0, 0, 3, 8, 11, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [132.0, 125.0, 7.0, 92.0, 33.0, 3.0, 4.0, 49.0, 43.0, 28.0, 5.0, 46.0, 3.0, 17.0, 26.0, 10.0, 18.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "17", "size_leaf_vector": "1"}}, {"base_weights": [-106.750015, -308.32416, 819.94116, -375.55194, -40.521534, 540.2277, 18.866722, -392.72507, -103.54334, -196.2962, 95.88686, 754.599, -6.130677, -4.5764937, -3.3537462, 1.1340563, -2.9463146, -2.427209, -0.8273747, -0.9485313, 3.6603236, 3.0332763, 9.395374], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 119, "left_children": [1, 3, 5, 7, 9, 11, -1, 13, 15, 17, 19, 21, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [27151518.0, 2127191.0, 6738380.0, 403055.0, 550056.6, 5901769.5, 0.0, 177822.0, 323625.62, 43148.47, 756132.25, 1300580.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11], "right_children": [2, 4, 6, 8, 10, 12, -1, 14, 16, 18, 20, 22, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [0.092575856, -0.06859587, 2.9190056, -0.18520685, -0.20332481, 1.4532615, 18.866722, -0.6172026, 1.0, -0.6907123, 3.0, 0.33892554, -6.130677, -4.5764937, -3.3537462, 1.1340563, -2.9463146, -2.427209, -0.8273747, -0.9485313, 3.6603236, 3.0332763, 9.395374], "split_indices": [20, 21, 18, 18, 20, 18, 0, 26, 1, 22, 0, 20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [143.0, 118.0, 25.0, 94.0, 24.0, 21.0, 4.0, 88.0, 6.0, 11.0, 13.0, 18.0, 3.0, 38.0, 50.0, 3.0, 3.0, 7.0, 4.0, 8.0, 5.0, 6.0, 12.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "23", "size_leaf_vector": "1"}}, {"base_weights": [23.17924, -241.96402, 1679.1707, -347.4058, 240.74756, 543.6435, 3314.4934, -426.00446, -218.15646, 21.020239, 4.081439, 1082.0687, -72.4308, 45.802044, 16.390059, -4.370657, -1.5356001, -2.6165447, 1.4734029, -1.1728374, 1.558375, 5.268016, 13.667935, 4.071096, -5.338885], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 120, "left_children": [1, 3, 5, 7, 9, 11, 13, 15, 17, 19, -1, 21, 23, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [63633236.0, 6445893.0, 35336784.0, 953360.0, 837353.4, 4390705.0, 9457864.0, 122680.0, 690397.5, 223384.48, 0.0, 386471.0, 1766377.2, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 11, 11, 12, 12], "right_children": [2, 4, 6, 8, 10, 12, 14, 16, 18, 20, -1, 22, 24, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [0.3882633, 0.0526399, -0.37959817, -0.4363957, -0.12031009, 2.0, 0.36153162, 2.0, 11.0, 0.1637558, 4.081439, 1.0, 2.0, 45.802044, 16.390059, -4.370657, -1.5356001, -2.6165447, 1.4734029, -1.1728374, 1.558375, 5.268016, 13.667935, 4.071096, -5.338885], "split_indices": [20, 27, 23, 27, 18, 8, 23, 3, 11, 24, 0, 16, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [143.0, 124.0, 19.0, 102.0, 22.0, 12.0, 7.0, 62.0, 40.0, 10.0, 12.0, 6.0, 6.0, 3.0, 4.0, 59.0, 3.0, 36.0, 4.0, 5.0, 5.0, 3.0, 3.0, 3.0, 3.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "25", "size_leaf_vector": "1"}}, {"base_weights": [22.515244, -162.41757, 49.002583, -337.18637, 312.99612, -391.96475, -106.68648, 438.59048, -5.5338697, -4.286984, -2.7184012, 4.4637065, -2.3054779, 2.06301, 11.096586], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 121, "left_children": [1, 3, -1, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [123475816.0, 11073090.0, 0.0, 1182894.0, 4159959.8, 227576.0, 1472935.8, 4759172.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7], "right_children": [2, 4, -1, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [2.1903315, 0.0526399, 49.002583, -0.2967835, 1.4532615, -0.27062544, -0.7446919, 1.5831932, -5.5338697, -4.286984, -2.7184012, 4.4637065, -2.3054779, 2.06301, 11.096586], "split_indices": [29, 27, 0, 27, 18, 26, 23, 27, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [135.0, 131.0, 4.0, 96.0, 35.0, 77.0, 19.0, 31.0, 4.0, 57.0, 20.0, 3.0, 16.0, 24.0, 7.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [11.917991, -202.07211, 4167.7515, -313.17902, 458.00183, 15.302157, 57.633247, -356.6854, 62.301792, 726.49786, -7.9758162, -4.2593446, -2.8070452, 3.6405857, -1.7179099, 4.152096, 12.279747], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 122, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, 15, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [127124216.0, 10117496.0, 20638808.0, 1933542.0, 7321817.5, 0.0, 0.0, 432836.0, 980869.25, 2143735.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, 16, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [2.3330073, 0.04001509, 1.8196839, 0.0526399, 1.4532615, 15.302157, 57.633247, -0.47092557, 2.0, 2.1555429, -7.9758162, -4.2593446, -2.8070452, 3.6405857, -1.7179099, 4.152096, 12.279747], "split_indices": [20, 20, 27, 27, 18, 0, 0, 27, 8, 27, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [141.0, 135.0, 6.0, 116.0, 19.0, 3.0, 3.0, 104.0, 12.0, 16.0, 3.0, 52.0, 52.0, 5.0, 7.0, 11.0, 5.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "17", "size_leaf_vector": "1"}}, {"base_weights": [-20.437937, -222.77107, 43.66836, -337.60068, 124.98562, -380.12067, -163.5883, 0.21818213, 514.8441, -3.9781349, -2.0026293, 1.9943297, -2.3517702, -1.4383638, 6.3403463, 6.3008323, 2.4205916], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 123, "left_children": [1, 3, -1, 5, 7, 9, 11, 13, 15, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [120810670.0, 5233475.0, 0.0, 662865.0, 1605010.4, 168484.0, 592658.3, 2465154.2, 98879.5, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8], "right_children": [2, 4, -1, 6, 8, 10, 12, 14, 16, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [2.8577383, 0.4472843, 43.66836, -0.2685201, 1.448768, -0.035448108, -0.8636659, 0.05719311, -0.6716731, -3.9781349, -2.0026293, 1.9943297, -2.3517702, -1.4383638, 6.3403463, 6.3008323, 2.4205916], "split_indices": [20, 26, 0, 27, 21, 26, 28, 22, 23, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [134.0, 129.0, 5.0, 97.0, 32.0, 77.0, 20.0, 25.0, 7.0, 69.0, 8.0, 3.0, 17.0, 21.0, 4.0, 4.0, 3.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "17", "size_leaf_vector": "1"}}, {"base_weights": [114.771355, -90.65779, 47.17815, -195.44884, 1853.4283, -336.2805, 260.60544, 36.597042, -4.1617947, -3.755742, -1.6228654, -0.39234242, 6.7879376], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 124, "left_children": [1, 3, -1, 5, 7, 9, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [132812080.0, 27864668.0, 0.0, 8372912.0, 30220178.0, 615844.0, 3913769.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, -1, 6, 8, 10, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [2.8577383, 1.2473981, 47.17815, -0.20332481, 1.0, -0.28713, 4.0, 36.597042, -4.1617947, -3.755742, -1.6228654, -0.39234242, 6.7879376], "split_indices": [20, 25, 0, 20, 16, 20, 13, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [139.0, 134.0, 5.0, 128.0, 6.0, 98.0, 30.0, 3.0, 3.0, 79.0, 19.0, 18.0, 12.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [45.780468, -165.76537, 4058.6255, -344.56973, 258.56653, 56.30793, 14.717769, -4.270511, -275.5622, 376.28946, -6.4073186, -2.9974477, -1.5377164, 1.1903718, 8.80866], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 125, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, 11, 13, -1, -1, -1, -1, -1], "loss_changes": [118670360.0, 10180131.0, 20180736.0, 417284.0, 4475796.0, 0.0, 0.0, 0.0, 112904.0, 4567968.5, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 8, 8, 9, 9], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, 12, 14, -1, -1, -1, -1, -1], "split_conditions": [2.8577383, -0.20332481, 2.0, -0.5305583, 1.4594278, 56.30793, 14.717769, -4.270511, 8.0, 0.33892554, -6.4073186, -2.9974477, -1.5377164, 1.1903718, 8.80866], "split_indices": [20, 20, 8, 26, 19, 0, 0, 0, 11, 20, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [138.0, 132.0, 6.0, 93.0, 39.0, 3.0, 3.0, 40.0, 53.0, 35.0, 4.0, 43.0, 10.0, 24.0, 11.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-7.525011, -157.50652, 2711.8179, -228.82365, 1092.39, 1.7967947, 41.95145, -330.20187, 147.14133, 12.9366045, 5.6767936, 2.2315834, -3.4796922, 0.15323499, 4.092346], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 126, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, -1, -1, -1, -1, -1, -1], "loss_changes": [62420244.0, 13175041.0, 29293696.0, 5308427.0, 110300.0, 0.0, 0.0, 1149326.0, 1030143.8, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, 1.9500875, -0.46722066, 0.0526399, 2.0008802, 1.7967947, 41.95145, -0.9199369, 0.9795277, 12.9366045, 5.6767936, 2.2315834, -3.4796922, 0.15323499, 4.092346], "split_indices": [29, 27, 23, 27, 21, 0, 0, 23, 26, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [151.0, 144.0, 7.0, 137.0, 7.0, 3.0, 4.0, 108.0, 29.0, 4.0, 3.0, 3.0, 105.0, 20.0, 9.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [118.86368, -145.5553, 4101.332, -293.071, 521.1333, 9.952329, 63.87145, -364.04507, -119.53801, 228.10262, 1016.7208, -3.8937705, -1.9015185, 1.3928022, -1.9180126, -0.12830094, 5.007682, 12.27247, 4.4672623], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 127, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, 15, 17, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [152235170.0, 13527903.0, 57542240.0, 1335864.0, 3398517.5, 0.0, 0.0, 273901.0, 662623.0, 1123272.8, 531579.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9, 10, 10], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, 16, 18, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, -0.009964261, 0.49706092, -0.33963278, 1.9500875, 9.952329, 63.87145, -0.2271967, -0.46422407, 0.32695737, 7.0, -3.8937705, -1.9015185, 1.3928022, -1.9180126, -0.12830094, 5.007682, 12.27247, 4.4672623], "split_indices": [29, 20, 21, 27, 27, 0, 0, 18, 24, 24, 11, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [143.0, 135.0, 8.0, 111.0, 24.0, 4.0, 4.0, 78.0, 33.0, 16.0, 8.0, 67.0, 11.0, 7.0, 26.0, 9.0, 7.0, 5.0, 3.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "19", "size_leaf_vector": "1"}}, {"base_weights": [-17.144693, -191.11826, 46.835503, -327.4038, 276.65594, -397.3702, -229.02359, 135.59058, 9.830577, -4.356054, -3.2641277, -2.5847816, 0.64179695, 2.2472115, -4.3306556], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 128, "left_children": [1, 3, -1, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [114568400.0, 8696950.0, 0.0, 631678.0, 2955708.2, 20520.0, 413872.75, 1465783.8, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7], "right_children": [2, 4, -1, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [3.5387294, 0.4472843, 46.835503, -0.39440298, 1.9821259, -0.505889, 0.2040027, 1.4594278, 9.830577, -4.356054, -3.2641277, -2.5847816, 0.64179695, 2.2472115, -4.3306556], "split_indices": [27, 26, 0, 26, 26, 27, 25, 19, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [138.0, 134.0, 4.0, 104.0, 30.0, 59.0, 45.0, 26.0, 4.0, 35.0, 24.0, 41.0, 4.0, 23.0, 3.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [31.431734, -163.91595, 42.58743, -301.8483, 286.31143, -396.69257, -149.66206, -81.52478, 492.7026, -4.3408375, -3.2548187, 2.9279919, -2.069605, 2.524868, -3.5618832, 1.8824323, 7.2468357], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 129, "left_children": [1, 3, -1, 5, 7, 9, 11, 13, 15, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [112180870.0, 8159543.0, 0.0, 1383975.0, 2393671.2, 21059.0, 1074683.2, 1190832.9, 1276067.5, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8], "right_children": [2, 4, -1, 6, 8, 10, 12, 14, 16, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [3.5387294, 0.21398789, 42.58743, -0.41024357, -0.7446919, -0.55880785, -0.8276071, -0.0019383027, 0.87322295, -4.3408375, -3.2548187, 2.9279919, -2.069605, 2.524868, -3.5618832, 1.8824323, 7.2468357], "split_indices": [27, 27, 0, 27, 23, 27, 23, 24, 27, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [134.0, 129.0, 5.0, 99.0, 30.0, 60.0, 39.0, 11.0, 19.0, 36.0, 24.0, 4.0, 35.0, 5.0, 6.0, 9.0, 10.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "17", "size_leaf_vector": "1"}}, {"base_weights": [99.40307, -147.90778, 4784.1226, -283.1466, 511.19302, 11.141608, 72.580284, -344.92416, 15.18255, 644.03485, -2.8078942, -4.033744, -2.8293908, -0.8006315, 4.1620617, 2.7771916, 9.8256855], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 130, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, 15, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [161750960.0, 11999810.0, 55466544.0, 2051005.0, 2600667.5, 0.0, 0.0, 219120.0, 797272.0, 2207199.5, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, 16, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, -0.031931423, 4.0, -0.18306975, 5.0, 11.141608, 72.580284, -0.29386264, 11.0, 0.33892554, -2.8078942, -4.033744, -2.8293908, -0.8006315, 4.1620617, 2.7771916, 9.8256855], "split_indices": [29, 20, 11, 29, 8, 0, 0, 29, 11, 20, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [138.0, 132.0, 6.0, 110.0, 22.0, 3.0, 3.0, 91.0, 19.0, 19.0, 3.0, 44.0, 47.0, 16.0, 3.0, 10.0, 9.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "17", "size_leaf_vector": "1"}}, {"base_weights": [56.26221, -121.90633, 42.04168, -198.10356, 25.183004, -351.2868, 169.13496, -4.2329373, -2.8610735, 0.23096931, 6.858038], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 131, "left_children": [1, 3, -1, 5, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [107671720.0, 28663354.0, 0.0, 7766339.5, 0.0, 334079.0, 3077674.5, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6], "right_children": [2, 4, -1, 6, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [3.5387294, 2.8455322, 42.04168, 0.19972922, 25.183004, -0.505889, 1.455611, -4.2329373, -2.8610735, 0.23096931, 6.858038], "split_indices": [27, 25, 0, 26, 0, 27, 27, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [144.0, 139.0, 5.0, 136.0, 3.0, 96.0, 40.0, 43.0, 53.0, 32.0, 8.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [65.58084, -205.84644, 2283.799, -294.28024, 180.88016, 812.4196, 64.948074, 2.248876, -311.4196, 8.173257, 20.727413, 1277.005, 231.76004, -3.3659387, -0.903589, 1.4622926, -5.4918776, 4.112239, 18.235098, -1.3439227, 5.399973], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 132, "left_children": [1, 3, 5, 7, 9, 11, -1, -1, 13, -1, 15, 17, 19, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [88374720.0, 4533403.0, 93858620.0, 1022150.0, 2531187.8, 3210847.0, 0.0, 0.0, 548308.0, 0.0, 1582300.6, 2561980.0, 862644.6, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 8, 8, 10, 10, 11, 11, 12, 12], "right_children": [2, 4, 6, 8, 10, 12, -1, -1, 14, -1, 16, 18, 20, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.7035879, -0.1536302, 4.0, -0.9199369, -0.8509167, 3.0, 64.948074, 2.248876, -0.18520685, 8.173257, 0.43779635, -0.79761463, 2.6527653, -3.3659387, -0.903589, 1.4622926, -5.4918776, 4.112239, 18.235098, -1.3439227, 5.399973], "split_indices": [21, 20, 13, 23, 28, 0, 0, 0, 18, 0, 18, 28, 21, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [145.0, 130.0, 15.0, 106.0, 24.0, 12.0, 3.0, 3.0, 103.0, 4.0, 20.0, 6.0, 6.0, 92.0, 11.0, 17.0, 3.0, 3.0, 3.0, 3.0, 3.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "21", "size_leaf_vector": "1"}}, {"base_weights": [134.93575, -123.15548, 4118.495, -286.215, 335.16367, 4.721122, 62.11882, -318.90353, 13.711435, 136.14621, 940.06244, -3.7557445, -2.344811, 2.6063352, -2.8531733, 3.607687, 0.16265713, 3.6365323, 12.131574], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 133, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, 15, 17, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [135191550.0, 9362853.0, 58132960.0, 906385.5, 3844625.2, 0.0, 0.0, 308970.0, 812728.75, 694218.4, 817988.5, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9, 10, 10], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, 16, 18, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2220678, 0.4472843, 0.49706092, -0.11083611, 1.5831932, 4.721122, 62.11882, -0.39440298, 2.0, -0.75929564, -0.99255466, -3.7557445, -2.344811, 2.6063352, -2.8531733, 3.607687, 0.16265713, 3.6365323, 12.131574], "split_indices": [29, 26, 21, 29, 27, 0, 0, 26, 8, 23, 22, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [130.0, 123.0, 7.0, 91.0, 32.0, 3.0, 4.0, 82.0, 9.0, 25.0, 7.0, 47.0, 35.0, 5.0, 4.0, 8.0, 17.0, 3.0, 4.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "19", "size_leaf_vector": "1"}}, {"base_weights": [94.160934, -169.91577, 3927.493, -316.3543, 293.0114, 55.788418, 4.6857133, -327.2052, -0.44726557, 535.05817, -154.97977, -3.3499222, -0.643306, 3.0299468, 8.10799, -3.334822, 1.6160009], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 134, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, -1, 13, 15, -1, -1, -1, -1, -1, -1], "loss_changes": [141376540.0, 8973238.0, 48792290.0, 280068.0, 3552875.8, 0.0, 0.0, 174923.0, 0.0, 1097997.5, 732031.25, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 9, 9, 10, 10], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, -1, 14, 16, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, 0.0526399, -0.9297316, -0.06969563, 4.0, 55.788418, 4.6857133, 2.0, -0.44726557, 0.18499485, -0.38547096, -3.3499222, -0.643306, 3.0299468, 8.10799, -3.334822, 1.6160009], "split_indices": [29, 27, 22, 29, 8, 0, 0, 3, 0, 29, 23, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [138.0, 130.0, 8.0, 99.0, 31.0, 5.0, 3.0, 95.0, 4.0, 20.0, 11.0, 92.0, 3.0, 12.0, 8.0, 7.0, 4.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "17", "size_leaf_vector": "1"}}, {"base_weights": [118.45736, -128.38779, 4485.164, -266.2539, 417.99237, 12.134896, 74.53442, -338.01447, -63.213837, 177.14037, 933.52563, -4.0009255, -2.8779755, 2.5330756, -1.061771, -3.227483, 3.1375232, 11.3383465, 3.9965553], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 135, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, 15, 17, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [161183420.0, 10807755.0, 68644350.0, 1639604.0, 3435373.0, 0.0, 0.0, 146645.0, 448443.4, 1535368.4, 509155.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9, 10, 10], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, 16, 18, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, -0.031931423, 4.0, -0.22761919, 1.9500875, 12.134896, 74.53442, -0.6172026, -0.9345406, 0.13784045, 7.0, -4.0009255, -2.8779755, 2.5330756, -1.061771, -3.227483, 3.1375232, 11.3383465, 3.9965553], "split_indices": [29, 20, 13, 29, 27, 0, 0, 26, 23, 26, 11, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [148.0, 141.0, 7.0, 113.0, 28.0, 4.0, 3.0, 83.0, 30.0, 20.0, 8.0, 34.0, 49.0, 3.0, 27.0, 4.0, 16.0, 5.0, 3.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "19", "size_leaf_vector": "1"}}, {"base_weights": [28.424366, -148.23712, 40.869175, -312.5195, 262.0652, -371.84952, -236.07512, 148.32103, 12.486574, -3.8303578, -1.8559706, -2.9983108, -1.5927867, 2.5941582, -8.330401], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 136, "left_children": [1, 3, -1, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [103134290.0, 9383856.0, 0.0, 361164.0, 4303423.0, 41360.5, 177424.0, 4249940.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7], "right_children": [2, 4, -1, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [3.5387294, -0.20332481, 40.869175, -0.47092557, 2.9190056, -0.2669924, -0.25019056, 1.1764319, 12.486574, -3.8303578, -1.8559706, -2.9983108, -1.5927867, 2.5941582, -8.330401], "split_indices": [27, 20, 0, 27, 18, 18, 29, 18, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [142.0, 137.0, 5.0, 98.0, 39.0, 53.0, 45.0, 36.0, 3.0, 49.0, 4.0, 23.0, 22.0, 33.0, 3.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [96.57312, -143.1543, 3841.5835, -209.2706, 9.821755, 54.832222, 4.1870437, -316.45184, 80.26813, -3.7494698, -2.5030267, -0.81263995, 2.9736428], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 137, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, 11, -1, -1, -1, -1], "loss_changes": [134319780.0, 10696236.0, 48275744.0, 4183863.0, 0.0, 0.0, 0.0, 285288.0, 1321702.8, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 8, 8], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, 12, -1, -1, -1, -1], "split_conditions": [1.2440649, 1.9500875, -0.9297316, 0.19972922, 9.821755, 54.832222, 4.1870437, -0.48221627, -0.06937444, -3.7494698, -2.5030267, -0.81263995, 2.9736428], "split_indices": [29, 27, 22, 26, 0, 0, 0, 27, 20, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [148.0, 140.0, 8.0, 133.0, 7.0, 5.0, 3.0, 97.0, 36.0, 49.0, 48.0, 21.0, 15.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [-52.67394, -154.63493, 19.50359, -286.1503, 304.29022, -327.5847, -100.86266, 128.0919, 794.8364, -3.8085341, -2.7182443, 2.2528193, -3.1890411, -2.2952335, 2.3428662, 3.4973238, 9.919324], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 138, "left_children": [1, 3, -1, 5, 7, 9, 11, 13, 15, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [29506480.0, 8355804.0, 0.0, 788385.0, 2577531.8, 154824.0, 1565226.6, 965219.1, 354780.5, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8], "right_children": [2, 4, -1, 6, 8, 10, 12, 14, 16, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.1965036, 0.4472843, 19.50359, -0.2197353, 1.5831932, -0.29357222, 2.0, -0.24111849, -0.99255466, -3.8085341, -2.7182443, 2.2528193, -3.1890411, -2.2952335, 2.3428662, 3.4973238, 9.919324], "split_indices": [29, 26, 0, 27, 27, 29, 8, 20, 22, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [142.0, 136.0, 6.0, 106.0, 30.0, 86.0, 20.0, 23.0, 7.0, 41.0, 45.0, 8.0, 12.0, 5.0, 18.0, 3.0, 4.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "17", "size_leaf_vector": "1"}}, {"base_weights": [48.700634, -159.00317, 3781.2942, -247.10051, 433.58453, 0.5039675, 60.097332, -286.37228, 116.28286, 571.72925, -2.2736554, -3.3404443, -1.4175211, -1.399332, 2.457159, 2.9545813, 8.847124], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 139, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, 15, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [117667960.0, 7624696.5, 66209150.0, 1831427.0, 1864854.8, 0.0, 0.0, 738557.0, 465511.12, 1121977.5, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, 16, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, 0.092575856, 0.49706092, -0.06969563, 1.4594278, 0.5039675, 60.097332, -0.2967835, -0.1586123, 1.5831932, -2.2736554, -3.3404443, -1.4175211, -1.399332, 2.457159, 2.9545813, 8.847124], "split_indices": [29, 20, 21, 29, 19, 0, 0, 27, 20, 27, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [150.0, 143.0, 7.0, 125.0, 18.0, 3.0, 4.0, 113.0, 12.0, 15.0, 3.0, 84.0, 29.0, 4.0, 8.0, 9.0, 6.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "17", "size_leaf_vector": "1"}}, {"base_weights": [-46.13864, -188.63594, 2399.8826, -274.07224, 430.55975, 41.828373, 4.9352226, -322.36627, 2.151838, 587.3041, -2.2566032, -4.152398, -2.8889403, 1.3792584, -1.6402215, 3.7118108, 9.020693], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 140, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, 15, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [50643890.0, 7365533.0, 25126840.0, 1614863.0, 1881166.5, 0.0, 0.0, 200163.0, 451300.34, 617434.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, 16, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, 0.092575856, 1.0741702, -0.12326148, 1.4532615, 41.828373, 4.9352226, -0.6003495, 5.0, 0.45794836, -2.2566032, -4.152398, -2.8889403, 1.3792584, -1.6402215, 3.7118108, 9.020693], "split_indices": [29, 20, 26, 29, 18, 0, 0, 27, 14, 29, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [143.0, 136.0, 7.0, 120.0, 16.0, 3.0, 4.0, 102.0, 18.0, 13.0, 3.0, 24.0, 78.0, 10.0, 8.0, 9.0, 4.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "17", "size_leaf_vector": "1"}}, {"base_weights": [-24.963531, -233.28262, 1126.9354, -305.48212, 151.3875, 451.98593, 2412.2505, -3.6756015, -243.49652, 59.587635, 4.8071504, 821.97577, -4.523688, 2.0898073, 40.12433, -2.550338, -0.41265684, -1.9309716, 1.9200773, 4.1977835, 12.206369], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 141, "left_children": [1, 3, 5, 7, 9, 11, 13, -1, 15, 17, -1, 19, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [32897708.0, 3267927.5, 17127390.0, 287537.0, 545713.5, 5390904.0, 23840488.0, 0.0, 110772.75, 572444.8, 0.0, 1251185.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 8, 8, 9, 9, 11, 11], "right_children": [2, 4, 6, 8, 10, 12, 14, -1, 16, 18, -1, 20, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [0.33892554, -0.018760122, 6.0, -0.47092557, 6.0, 5.0, 3.0, -3.6756015, 8.0, -0.1586123, 4.8071504, 0.9082265, -4.523688, 2.0898073, 40.12433, -2.550338, -0.41265684, -1.9309716, 1.9200773, 4.1977835, 12.206369], "split_indices": [20, 27, 13, 27, 13, 8, 11, 0, 13, 20, 0, 25, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [135.0, 115.0, 20.0, 97.0, 18.0, 14.0, 6.0, 46.0, 51.0, 15.0, 3.0, 10.0, 4.0, 3.0, 3.0, 48.0, 3.0, 5.0, 10.0, 6.0, 4.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "21", "size_leaf_vector": "1"}}, {"base_weights": [8.550617, -136.93329, 2371.6077, -225.97133, 681.7838, 41.325344, 4.8852453, -317.84314, 20.29678, 931.2851, 2.354681, -4.0588737, -2.772133, 2.5391366, -1.0804864, 4.4887447, 11.309365], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 142, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, 15, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [47423760.0, 9630624.0, 24508464.0, 2676242.5, 1228227.5, 0.0, 0.0, 195056.0, 1016910.4, 262705.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, 16, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2432504, 1.1800077, 1.0741702, -0.20714763, 7.0, 41.325344, 4.8852453, -0.81524664, -0.4990367, -0.89633024, 2.354681, -4.0588737, -2.772133, 2.5391366, -1.0804864, 4.4887447, 11.309365], "split_indices": [29, 27, 26, 29, 11, 0, 0, 26, 17, 22, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [136.0, 129.0, 7.0, 117.0, 12.0, 3.0, 4.0, 85.0, 32.0, 7.0, 5.0, 24.0, 61.0, 11.0, 21.0, 3.0, 4.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "17", "size_leaf_vector": "1"}}, {"base_weights": [23.38638, -161.64893, 39.66925, -302.04935, 154.7807, 0.21071504, -313.56717, -79.39305, 417.78818, -3.4368463, -2.0397575, -2.0206168, 1.6052227, 5.5671782, 0.23970982], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 143, "left_children": [1, 3, -1, 5, 7, -1, 9, 11, 13, -1, -1, -1, -1, -1, -1], "loss_changes": [97690920.0, 5733403.0, 0.0, 337864.0, 2496781.5, 0.0, 213695.0, 679901.44, 1026141.25, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 6, 6, 7, 7, 8, 8], "right_children": [2, 4, -1, 6, 8, -1, 10, 12, 14, -1, -1, -1, -1, -1, -1], "split_conditions": [2.4534156, 0.19972922, 39.66925, -0.8618013, -0.04208925, 0.21071504, -0.27062544, 1.0, -0.4547159, -3.4368463, -2.0397575, -2.0206168, 1.6052227, 5.5671782, 0.23970982], "split_indices": [27, 26, 0, 28, 20, 0, 26, 5, 28, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [132.0, 127.0, 5.0, 88.0, 39.0, 3.0, 85.0, 21.0, 18.0, 65.0, 20.0, 14.0, 7.0, 13.0, 5.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-40.336956, -168.21182, 26.517414, -284.3651, 98.18823, -3.5922334, -220.28639, 6.433186, -0.89619285, -2.3797324, -0.13490175, 0.8895684, -5.3985515], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 144, "left_children": [1, 3, -1, 5, 7, -1, 9, -1, 11, -1, -1, -1, -1], "loss_changes": [45542444.0, 3926788.5, 0.0, 342931.0, 2107183.8, 0.0, 179643.25, 0.0, 1694590.4, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 6, 6, 8, 8], "right_children": [2, 4, -1, 6, 8, -1, 10, -1, 12, -1, -1, -1, -1], "split_conditions": [2.8455322, -0.18306975, 26.517414, -0.40619367, -0.65176386, -3.5922334, 2.0, 6.433186, 0.25419647, -2.3797324, -0.13490175, 0.8895684, -5.3985515], "split_indices": [25, 29, 0, 25, 17, 0, 4, 0, 29, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [130.0, 125.0, 5.0, 87.0, 38.0, 38.0, 49.0, 5.0, 33.0, 45.0, 4.0, 29.0, 4.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [-79.15386, -203.78757, 1074.9034, -298.53818, 105.373604, 1659.9056, 0.18232927, -358.2292, -244.27028, -2.4654727, 475.47406, 8.984809, 20.893293, -3.791987, -2.4690604, -1.860048, -3.1724374, -1.9952852, 1.4220512, -0.096424274, 8.41747], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 145, "left_children": [1, 3, 5, 7, 9, 11, -1, 13, 15, 17, 19, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [20643456.0, 3810301.0, 8623731.0, 233126.0, 1238469.8, 1065242.0, 0.0, 11358.5, 174791.25, 741110.5, 1251995.1, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 8, 8, 9, 9, 10, 10], "right_children": [2, 4, 6, 8, 10, 12, -1, 14, 16, 18, 20, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [0.41031995, -0.18306975, 3.0, -0.29357222, 7.0, 1.2440649, 0.18232927, -0.28617167, 4.0, 0.0526399, 1.0, 8.984809, 20.893293, -3.791987, -2.4690604, -1.860048, -3.1724374, -1.9952852, 1.4220512, -0.096424274, 8.41747], "split_indices": [18, 29, 0, 29, 13, 29, 0, 18, 8, 27, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [141.0, 128.0, 13.0, 98.0, 30.0, 8.0, 5.0, 44.0, 54.0, 24.0, 6.0, 4.0, 4.0, 35.0, 9.0, 32.0, 22.0, 10.0, 14.0, 3.0, 3.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "21", "size_leaf_vector": "1"}}, {"base_weights": [114.45491, -131.05652, 3675.3042, -246.24316, 520.53644, 7.4626007, 58.692673, -291.12003, -78.42282, 705.5093, 76.21407, -3.0486352, -0.5246513, 2.8898265, -1.6217065, 3.022956, 8.959174, 3.7462332, -2.412237], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 146, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, 15, 17, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [121999890.0, 9960330.0, 53455290.0, 820633.0, 1589904.0, 0.0, 0.0, 272605.0, 816087.25, 803900.5, 753465.9, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9, 10, 10], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, 16, 18, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, 0.092575856, 0.49706092, -0.20714763, 4.0, 7.4626007, 58.692673, 2.0, -0.2639286, 0.3882633, 0.2505919, -3.0486352, -0.5246513, 2.8898265, -1.6217065, 3.022956, 8.959174, 3.7462332, -2.412237], "split_indices": [29, 20, 21, 29, 8, 0, 0, 4, 18, 20, 29, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [138.0, 130.0, 8.0, 111.0, 19.0, 4.0, 4.0, 87.0, 24.0, 13.0, 6.0, 82.0, 5.0, 4.0, 20.0, 5.0, 8.0, 3.0, 3.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "19", "size_leaf_vector": "1"}}, {"base_weights": [16.63913, -119.97975, 2285.8606, -180.28561, 844.82263, -0.5947341, 37.04976, -281.34918, 70.414566, 11.11227, 3.0058644, -3.0766327, -1.404114, 3.5769637, -0.21166505], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 147, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, -1, -1, -1, -1, -1, -1], "loss_changes": [43677064.0, 7890614.0, 26847104.0, 3212251.0, 825734.5, 0.0, 0.0, 290534.0, 981059.3, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, 1.9500875, -0.46722066, -0.18306975, 2.0008802, -0.5947341, 37.04976, -0.2197353, -0.75929564, 11.11227, 3.0058644, -3.0766327, -1.404114, 3.5769637, -0.21166505], "split_indices": [29, 27, 23, 29, 21, 0, 0, 27, 23, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [139.0, 132.0, 7.0, 125.0, 7.0, 3.0, 4.0, 89.0, 36.0, 4.0, 3.0, 74.0, 15.0, 8.0, 28.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [98.95929, -104.75866, 3626.2012, -196.28983, 664.9784, -0.07236963, 58.077312, -281.07672, 38.90383, 9.833577, 343.45236, -3.4118686, -2.3245687, 1.2464345, -1.7932559, 4.644463, 2.0553102], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 148, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, -1, 15, -1, -1, -1, -1, -1, -1], "loss_changes": [105290350.0, 9923718.0, 63454264.0, 2503627.5, 1197639.5, 0.0, 0.0, 189505.0, 658517.2, 0.0, 54663.75, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 10, 10], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, -1, 16, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, 1.1800077, 0.49706092, -0.18306975, 7.0, -0.07236963, 58.077312, -0.40619367, 0.0222285, 9.833577, 2.2768104, -3.4118686, -2.3245687, 1.2464345, -1.7932559, 4.644463, 2.0553102], "split_indices": [29, 27, 21, 29, 11, 0, 0, 25, 23, 0, 21, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [145.0, 138.0, 7.0, 124.0, 14.0, 3.0, 4.0, 91.0, 33.0, 6.0, 8.0, 38.0, 53.0, 24.0, 9.0, 3.0, 5.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "17", "size_leaf_vector": "1"}}, {"base_weights": [118.59236, -120.86076, 4234.347, -181.5944, 888.74774, 13.993685, 67.19458, -280.6098, 68.11459, 10.955082, 4.0808516, -3.176797, -1.6558578, -2.0347588, 1.4732459], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 149, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, -1, -1, -1, -1, -1, -1], "loss_changes": [143414080.0, 8623095.0, 46958096.0, 3258114.0, 347845.0, 0.0, 0.0, 348157.5, 847453.2, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, 1.9500875, 4.0, -0.18306975, 2.0008802, 13.993685, 67.19458, -0.26600468, -0.36327416, 10.955082, 4.0808516, -3.176797, -1.6558578, -2.0347588, 1.4732459], "split_indices": [29, 27, 11, 29, 21, 0, 0, 29, 27, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [144.0, 137.0, 7.0, 130.0, 7.0, 4.0, 3.0, 93.0, 37.0, 4.0, 3.0, 69.0, 24.0, 8.0, 29.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [58.61299, -144.20847, 53.608322, -228.86928, 436.97397, -289.09045, -11.604851, 204.50514, 672.9257, -3.505113, -2.3959644, 1.9813088, -1.1583356, 0.05354364, 2.8907628, 2.9178536, 8.432329], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 150, "left_children": [1, 3, -1, 5, 7, 9, 11, 13, 15, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [145953140.0, 6545631.0, 0.0, 1501265.0, 794769.75, 189557.0, 591314.6, 166846.81, 273131.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8], "right_children": [2, 4, -1, 6, 8, 10, 12, 14, 16, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, 0.092575856, 53.608322, -0.18306975, 1.8196839, -0.29357222, -0.2243672, 0.8531112, 0.8139121, -3.505113, -2.3959644, 1.9813088, -1.1583356, 0.05354364, 2.8907628, 2.9178536, 8.432329], "split_indices": [29, 20, 0, 29, 27, 29, 24, 27, 20, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [134.0, 130.0, 4.0, 114.0, 16.0, 89.0, 25.0, 9.0, 7.0, 37.0, 52.0, 8.0, 17.0, 3.0, 6.0, 3.0, 4.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "17", "size_leaf_vector": "1"}}, {"base_weights": [131.75743, -113.08771, 4155.245, -175.51291, 876.4295, 13.452868, 66.28857, -277.23877, 73.919945, 5.3108726, 10.889749, -3.1611285, -1.5877928, 3.7975776, -0.10097474], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 151, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, -1, -1, -1, -1, -1, -1], "loss_changes": [137403580.0, 8307500.5, 46687440.0, 3192236.0, 8704.5, 0.0, 0.0, 359605.5, 954612.6, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, 1.9500875, 4.0, -0.18306975, -0.5258309, 13.452868, 66.28857, -0.25530338, -0.76370597, 5.3108726, 10.889749, -3.1611285, -1.5877928, 3.7975776, -0.10097474], "split_indices": [29, 27, 11, 29, 17, 0, 0, 29, 23, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [138.0, 131.0, 7.0, 124.0, 7.0, 4.0, 3.0, 88.0, 36.0, 4.0, 3.0, 65.0, 23.0, 7.0, 29.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-22.153473, -159.17569, 46.078598, -247.90672, 307.84314, -272.46494, 3.4193547, 108.22505, 9.648796, -2.4682448, -7.6152983, -2.1315122, 2.4823525], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 152, "left_children": [1, 3, -1, 5, 7, 9, -1, 11, -1, -1, -1, -1, -1], "loss_changes": [88282230.0, 5670583.5, 0.0, 1744503.5, 2780908.8, 1191318.5, 0.0, 862842.3, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 7, 7], "right_children": [2, 4, -1, 6, 8, 10, -1, 12, -1, -1, -1, -1, -1], "split_conditions": [2.830827, 0.86266834, 46.078598, 2.0, -0.29674023, 0.8531112, 3.4193547, 0.21210697, 9.648796, -2.4682448, -7.6152983, -2.1315122, 2.4823525], "split_indices": [29, 21, 0, 3, 17, 27, 0, 26, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [137.0, 134.0, 3.0, 113.0, 21.0, 109.0, 4.0, 17.0, 4.0, 105.0, 4.0, 5.0, 12.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [89.88407, -137.79381, 3469.7788, -189.80234, 832.88965, 6.350682, 56.105137, -278.1824, -20.606148, 11.276474, 3.2988443, -3.0314593, -1.431338, 0.7690915, -2.4203353], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 153, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, -1, -1, -1, -1, -1, -1], "loss_changes": [109751320.0, 6922850.0, 51051590.0, 1908295.0, 665714.0, 0.0, 0.0, 239753.0, 990296.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, 1.9500875, 0.49706092, -0.2967835, 7.0, 6.350682, 56.105137, -0.20020312, 5.0, 11.276474, 3.2988443, -3.0314593, -1.431338, 0.7690915, -2.4203353], "split_indices": [29, 27, 21, 27, 11, 0, 0, 25, 8, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [141.0, 133.0, 8.0, 127.0, 6.0, 4.0, 4.0, 83.0, 44.0, 3.0, 3.0, 69.0, 14.0, 31.0, 13.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [87.67839, -113.49383, 46.665142, -255.55905, 307.82367, -306.28702, -156.79057, 77.95358, 741.90356, -3.1539094, -0.8402759, 0.03005166, -2.5419257, 3.9815578, -0.27260458, 3.6991584, 9.547496], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 154, "left_children": [1, 3, -1, 5, 7, 9, 11, 13, 15, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [131331190.0, 8278737.0, 0.0, 467994.5, 3434453.8, 109028.5, 576672.6, 819445.44, 596804.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8], "right_children": [2, 4, -1, 6, 8, 10, 12, 14, 16, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, 0.4472843, 46.665142, -0.26607066, 0.33892554, 2.0, 2.0, -0.75929564, -0.88131464, -3.1539094, -0.8402759, 0.03005166, -2.5419257, 3.9815578, -0.27260458, 3.6991584, 9.547496], "split_indices": [29, 26, 0, 29, 20, 3, 8, 23, 22, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [141.0, 136.0, 5.0, 102.0, 34.0, 66.0, 36.0, 23.0, 11.0, 63.0, 3.0, 14.0, 22.0, 5.0, 18.0, 5.0, 6.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "17", "size_leaf_vector": "1"}}, {"base_weights": [44.45128, -152.30241, 43.984325, -239.30516, 455.43427, -273.17294, -24.007948, 205.95448, 7.823976, -2.8171194, 0.04300714, 0.5716285, -3.0212874, 4.1597376, 0.75188017], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 155, "left_children": [1, 3, -1, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [118889660.0, 7141309.0, 0.0, 846534.0, 1225465.8, 240543.0, 401074.9, 270772.9, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7], "right_children": [2, 4, -1, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, 0.04001509, 43.984325, 0.0526399, 1.8196839, -0.079770885, 6.0, -0.76370597, 7.823976, -2.8171194, 0.04300714, 0.5716285, -3.0212874, 4.1597376, 0.75188017], "split_indices": [29, 20, 0, 27, 27, 29, 14, 23, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [137.0, 132.0, 5.0, 116.0, 16.0, 100.0, 16.0, 10.0, 6.0, 97.0, 3.0, 13.0, 3.0, 3.0, 7.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-39.878685, -170.346, 1863.5653, -260.29095, 154.9843, 2.0609977, 31.482977, -3.5435534, -230.36662, 40.260002, 5.880341, -2.3774095, -0.28950486, 1.4787179, -3.9821913], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 156, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, 11, 13, -1, -1, -1, -1, -1], "loss_changes": [34836156.0, 3873650.8, 18515396.0, 208236.0, 1417021.6, 0.0, 0.0, 0.0, 113400.5, 1191313.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 8, 8, 9, 9], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, 12, 14, -1, -1, -1, -1, -1], "split_conditions": [1.2473981, 0.0526399, -0.34237808, -0.9018909, 1.9500875, 2.0609977, 31.482977, -3.5435534, 2.0, 0.87322295, 5.880341, -2.3774095, -0.28950486, 1.4787179, -3.9821913], "split_indices": [25, 27, 23, 26, 27, 0, 0, 0, 3, 27, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [138.0, 130.0, 8.0, 102.0, 28.0, 4.0, 4.0, 22.0, 80.0, 23.0, 5.0, 77.0, 3.0, 19.0, 4.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [115.66958, -113.18854, 3905.9895, -176.47202, 778.3141, 7.443719, 68.81489, -271.7552, 76.30346, 2.6582906, 9.90235, 0.35331672, -2.826267, -0.76353866, 3.1075041], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 157, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, -1, -1, -1, -1, -1, -1], "loss_changes": [121884870.0, 7640803.0, 70135990.0, 3031411.5, 714098.0, 0.0, 0.0, 313814.0, 1276402.1, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, 1.9500875, 4.0, 0.19972922, -0.89633024, 7.443719, 68.81489, -0.8636659, -0.04208925, 2.6582906, 9.90235, 0.35331672, -2.826267, -0.76353866, 3.1075041], "split_indices": [29, 27, 13, 26, 22, 0, 0, 28, 20, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [139.0, 132.0, 7.0, 124.0, 8.0, 4.0, 3.0, 90.0, 34.0, 3.0, 5.0, 3.0, 87.0, 21.0, 13.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-54.919117, -189.79099, 1144.5994, -167.26166, -8.745212, 328.40808, 33.06998, -257.12183, 115.967026, 75.934074, 8.143476, -3.3304827, -2.0221715, 0.08471998, 4.3738055, -2.7824407, 2.9941385], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 158, "left_children": [1, 3, 5, 7, -1, 9, -1, 11, 13, 15, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [23998988.0, 1905347.0, 25387558.0, 3338458.0, 0.0, 1410319.5, 0.0, 345302.0, 1101861.4, 795677.1, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 7, 7, 8, 8, 9, 9], "right_children": [2, 4, 6, 8, -1, 10, -1, 12, 14, 16, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.7035879, 0.9694332, 1.2432504, -0.18306975, -8.745212, 2.2059762, 33.06998, -0.29357222, 0.1796442, 0.82446826, 8.143476, -3.3304827, -2.0221715, 0.08471998, 4.3738055, -2.7824407, 2.9941385], "split_indices": [21, 29, 29, 29, 0, 27, 0, 29, 29, 26, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [146.0, 132.0, 14.0, 129.0, 3.0, 11.0, 3.0, 98.0, 31.0, 8.0, 3.0, 39.0, 59.0, 24.0, 7.0, 3.0, 5.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "17", "size_leaf_vector": "1"}}, {"base_weights": [32.750885, -129.71729, 36.828426, -245.2762, 182.98409, -309.66287, -181.12358, 524.3281, 18.398987, -3.232126, -0.79787487, -1.9137806, -0.102001585, 1.6413324, 7.5814824, -6.0895505, 1.2670844], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 159, "left_children": [1, 3, -1, 5, 7, 9, 11, 13, 15, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [83501410.0, 4923171.0, 0.0, 352791.0, 2068963.9, 123679.0, 89169.75, 886120.25, 1843769.1, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8], "right_children": [2, 4, -1, 6, 8, 10, 12, 14, 16, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [2.2059762, -0.2066361, 36.828426, -0.48221627, 2.0, -0.2669924, -0.0155352615, 0.1516057, 1.0, -3.232126, -0.79787487, -1.9137806, -0.102001585, 1.6413324, 7.5814824, -6.0895505, 1.2670844], "split_indices": [27, 20, 0, 27, 8, 18, 29, 29, 11, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [139.0, 134.0, 5.0, 98.0, 36.0, 47.0, 51.0, 11.0, 25.0, 44.0, 3.0, 48.0, 3.0, 5.0, 6.0, 3.0, 22.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "17", "size_leaf_vector": "1"}}, {"base_weights": [-15.15761, -135.18616, 33.00642, -194.02985, 498.8333, -253.69397, -1.0766456, 7.421168, 219.0284, -2.6527202, -0.33864745, 3.5489676, -1.1969376, 0.27744532, 3.5553017], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 160, "left_children": [1, 3, -1, 5, 7, 9, 11, -1, 13, -1, -1, -1, -1, -1, -1], "loss_changes": [56960556.0, 5245268.0, 0.0, 1461774.0, 654222.0, 237871.0, 1351416.1, 0.0, 172871.75, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 8, 8], "right_children": [2, 4, -1, 6, 8, 10, 12, -1, 14, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, 1.4375046, 33.00642, -0.18306975, -0.83306056, 2.0, -0.75929564, 7.421168, 5.0, -2.6527202, -0.33864745, 3.5489676, -1.1969376, 0.27744532, 3.5553017], "split_indices": [29, 26, 0, 29, 28, 4, 23, 0, 14, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [141.0, 137.0, 4.0, 126.0, 11.0, 96.0, 30.0, 5.0, 6.0, 91.0, 5.0, 7.0, 23.0, 3.0, 3.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [24.549952, -120.27156, 39.588047, -250.93037, 158.62485, -205.01875, -328.19034, 89.388535, 8.286157, -2.3271034, 2.4773905, -3.6024437, -2.0267465, 1.7587371, -7.545428], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 161, "left_children": [1, 3, -1, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [80243580.0, 4995690.0, 0.0, 262545.0, 1966898.1, 822442.75, 59699.75, 3125135.8, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7], "right_children": [2, 4, -1, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [3.5387294, 0.19972922, 39.588047, 5.0, 2.9190056, -0.017417625, 0.7918346, 1.4532615, 8.286157, -2.3271034, 2.4773905, -3.6024437, -2.0267465, 1.7587371, -7.545428], "split_indices": [27, 26, 0, 8, 18, 27, 24, 18, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [139.0, 135.0, 4.0, 92.0, 43.0, 60.0, 32.0, 40.0, 3.0, 57.0, 3.0, 24.0, 8.0, 37.0, 3.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [119.383965, -101.09544, 4196.961, -185.27414, 609.61194, 64.18305, 9.263513, -255.71559, 37.293564, 377.9239, 9.510456, -3.1396234, -2.017578, 1.5240126, -1.9664117, 4.8829975, 1.6986804], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 162, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, 15, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [122691660.0, 7890337.5, 44909720.0, 1843883.8, 747930.0, 0.0, 0.0, 216319.0, 810866.9, 146631.25, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, 16, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, 1.1800077, -0.6227321, -0.19222529, -0.31147707, 64.18305, 9.263513, -0.29386264, 0.0222285, 2.0, 9.510456, -3.1396234, -2.017578, 1.5240126, -1.9664117, 4.8829975, 1.6986804], "split_indices": [29, 27, 28, 29, 17, 0, 0, 29, 23, 8, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [135.0, 129.0, 6.0, 116.0, 13.0, 3.0, 3.0, 88.0, 28.0, 9.0, 4.0, 40.0, 48.0, 19.0, 9.0, 5.0, 4.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "17", "size_leaf_vector": "1"}}, {"base_weights": [-14.001725, -174.3366, 1290.9819, -231.01299, 145.06245, 152.33571, 29.735453, 2.3395674, -247.14136, -1.2697833, 226.07855, 656.2804, -7.676264, -3.0525258, -2.0566247, 3.2168727, 0.70841813, 3.194038, 8.290619], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 163, "left_children": [1, 3, 5, 7, 9, 11, -1, -1, 13, -1, 15, 17, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [28680830.0, 2242010.8, 28284380.0, 837739.5, 447470.97, 5139867.5, 0.0, 0.0, 180618.5, 0.0, 199801.69, 142522.25, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 8, 8, 10, 10, 11, 11], "right_children": [2, 4, 6, 8, 10, 12, -1, -1, 14, -1, 16, 18, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [0.33892554, -0.06969563, -0.37959817, -0.9345406, -0.1586123, 2.0, 29.735453, 2.3395674, -0.6172026, -1.2697833, -0.8306949, 0.57345134, -7.676264, -3.0525258, -2.0566247, 3.2168727, 0.70841813, 3.194038, 8.290619], "split_indices": [20, 29, 23, 23, 20, 8, 0, 0, 26, 0, 22, 29, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [135.0, 121.0, 14.0, 103.0, 18.0, 9.0, 5.0, 3.0, 100.0, 4.0, 14.0, 6.0, 3.0, 39.0, 61.0, 8.0, 6.0, 3.0, 3.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "19", "size_leaf_vector": "1"}}, {"base_weights": [16.900667, -123.8665, 43.06099, -182.83728, 487.8147, -241.75826, 31.874062, 2.298665, 7.5135922, -3.0560436, -1.6783316, 1.4635179, -3.5501404], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 164, "left_children": [1, 3, -1, 5, 7, 9, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [76006090.0, 4489860.5, 0.0, 1424639.0, 574978.75, 364007.5, 1159187.9, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, -1, 6, 8, 10, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [1.6165377, 1.0424473, 43.06099, -0.20835175, 0.8139121, -0.47092557, 8.0, 2.298665, 7.5135922, -3.0560436, -1.6783316, 1.4635179, -3.5501404], "split_indices": [18, 27, 0, 20, 20, 27, 14, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [124.0, 121.0, 3.0, 111.0, 10.0, 87.0, 24.0, 6.0, 4.0, 45.0, 42.0, 19.0, 5.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [108.53594, -103.1342, 3799.209, -188.45543, 485.6183, 10.839492, 62.434563, -266.6789, -60.67335, 598.5372, 1.6000762, -2.7398593, -0.6663072, -0.9908953, 1.4378123, 3.8452497, 9.839084], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 165, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, 15, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [115229384.0, 7124119.0, 46325816.0, 1213240.5, 565970.0, 0.0, 0.0, 92804.0, 391252.25, 693690.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, 16, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, 0.092575856, 4.0, -0.25019056, -0.77624285, 10.839492, 62.434563, 2.0, 0.09129919, -0.23109435, 1.6000762, -2.7398593, -0.6663072, -0.9908953, 1.4378123, 3.8452497, 9.839084], "split_indices": [29, 20, 11, 29, 28, 0, 0, 3, 29, 17, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [146.0, 139.0, 7.0, 122.0, 17.0, 4.0, 3.0, 75.0, 47.0, 12.0, 5.0, 72.0, 3.0, 40.0, 7.0, 9.0, 3.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "17", "size_leaf_vector": "1"}}, {"base_weights": [97.80721, -96.94371, 3298.9841, -209.56316, 247.26201, 52.54024, 0.30412742, 2.5327837, -226.48999, 532.0387, 9.419269, -2.4090633, 1.688917, 2.6594684, 6.798724, -0.87239146, 1.5324198], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 166, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, 11, 13, 15, -1, -1, -1, -1, -1, -1], "loss_changes": [86977200.0, 5168695.5, 50961184.0, 840806.0, 2230092.0, 0.0, 0.0, 0.0, 593590.5, 409156.0, 277507.16, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 8, 8, 9, 9, 10, 10], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, 12, 14, 16, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, 0.0526399, -0.9297316, -0.9199369, 2.0, 52.54024, 0.30412742, 2.5327837, 2.0, 0.032106534, 0.04001509, -2.4090633, 1.688917, 2.6594684, 6.798724, -0.87239146, 1.5324198], "split_indices": [29, 27, 22, 23, 8, 0, 0, 0, 3, 24, 20, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [138.0, 131.0, 7.0, 99.0, 32.0, 4.0, 3.0, 3.0, 96.0, 14.0, 18.0, 93.0, 3.0, 6.0, 8.0, 11.0, 7.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "17", "size_leaf_vector": "1"}}, {"base_weights": [90.45891, -111.27886, 3106.4856, -166.42863, 785.0149, 3.7966182, 52.11992, -232.32457, 77.207726, 9.67821, 3.6022847, -2.8226807, -1.5839223, 2.6943603, -0.83797026], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 167, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, -1, -1, -1, -1, -1, -1], "loss_changes": [87353830.0, 6803691.0, 49692770.0, 2072947.0, 272458.5, 0.0, 0.0, 328183.5, 889187.4, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, 1.9500875, 0.49706092, 0.4472843, 2.0008802, 3.7966182, 52.11992, -0.39440298, -0.6570694, 9.67821, 3.6022847, -2.8226807, -1.5839223, 2.6943603, -0.83797026], "split_indices": [29, 27, 21, 26, 21, 0, 0, 26, 23, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [142.0, 134.0, 8.0, 127.0, 7.0, 4.0, 4.0, 100.0, 27.0, 4.0, 3.0, 58.0, 42.0, 12.0, 15.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [98.90212, -109.96135, 3081.6338, -217.78265, 396.41867, 50.409527, 5.059684, -247.83951, 40.81246, 180.07474, 712.93427, -3.3144686, -2.1373062, -2.1826541, 2.5706313, 0.5874573, 4.109636, 3.2318323, 9.600784], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 168, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, 15, 17, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [85687920.0, 7129528.5, 42867830.0, 841799.5, 1446482.5, 0.0, 0.0, 202873.5, 728421.06, 396013.44, 556512.5, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9, 10, 10], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, 16, 18, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, -0.009964261, 4.1228113, 0.0526399, 1.8196839, 50.409527, 5.059684, -0.9018909, -0.20219812, 0.59401447, -0.89633024, -3.3144686, -2.1373062, -2.1826541, 2.5706313, 0.5874573, 4.109636, 3.2318323, 9.600784], "split_indices": [29, 20, 25, 27, 27, 0, 0, 26, 18, 24, 22, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [136.0, 128.0, 8.0, 106.0, 22.0, 4.0, 4.0, 95.0, 11.0, 14.0, 8.0, 25.0, 70.0, 5.0, 6.0, 10.0, 4.0, 4.0, 4.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "19", "size_leaf_vector": "1"}}, {"base_weights": [100.9143, -99.87829, 3056.981, -198.48952, 356.24728, 3.5443704, 51.481087, -260.33765, -64.12288, 574.6334, -6.946665, -2.8230221, -1.7509414, 0.65647537, -1.5994687, 6.7235293, 1.3779144, 1.610193, -2.9917543], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 169, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, 15, 17, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [83997060.0, 6052916.0, 49037050.0, 897410.0, 1907645.0, 0.0, 0.0, 90950.5, 458173.88, 547594.5, 539031.7, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9, 10, 10], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, 16, 18, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, -0.035381977, 0.49706092, -0.2967835, 4.0, 3.5443704, 51.481087, -0.27062544, 0.08957327, -0.4547159, 0.016043227, -2.8230221, -1.7509414, 0.65647537, -1.5994687, 6.7235293, 1.3779144, 1.610193, -2.9917543], "split_indices": [29, 29, 21, 27, 8, 0, 0, 26, 24, 28, 18, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [140.0, 132.0, 8.0, 109.0, 23.0, 4.0, 4.0, 74.0, 35.0, 14.0, 9.0, 57.0, 17.0, 15.0, 20.0, 11.0, 3.0, 6.0, 3.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "19", "size_leaf_vector": "1"}}, {"base_weights": [95.25095, -93.58092, 3246.2908, -209.02744, 252.11261, 51.95478, -0.017407471, 1.0251637, -222.93251, 75.31633, 668.882, -2.7971597, -1.385498, 4.6564503, 0.009194574, 3.8575714, 9.971051], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 170, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, 11, 13, 15, -1, -1, -1, -1, -1, -1], "loss_changes": [84201464.0, 5400515.5, 50657744.0, 460402.0, 2454779.5, 0.0, 0.0, 0.0, 425983.5, 725506.1, 544502.5, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 8, 8, 9, 9, 10, 10], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, 12, 14, 16, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2220678, 0.4472843, -0.9297316, -0.8636659, 0.33892554, 51.95478, -0.017407471, 1.0251637, -0.39440298, 0.49679533, -0.52563566, -2.7971597, -1.385498, 4.6564503, 0.009194574, 3.8575714, 9.971051], "split_indices": [29, 26, 22, 28, 20, 0, 0, 0, 26, 26, 23, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [140.0, 133.0, 7.0, 100.0, 33.0, 4.0, 3.0, 4.0, 96.0, 24.0, 9.0, 56.0, 40.0, 3.0, 21.0, 6.0, 3.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "17", "size_leaf_vector": "1"}}, {"base_weights": [44.03927, -102.47224, 34.929226, -230.74988, 264.97308, -3.0043232, -187.5799, -1.9879744, 341.0464, -1.9464698, -0.4525411, 1.1545578, 6.1340504], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 171, "left_children": [1, 3, -1, 5, 7, -1, 9, -1, 11, -1, -1, -1, -1], "loss_changes": [62005004.0, 5623071.0, 0.0, 208317.0, 1161510.8, 0.0, 48499.75, 0.0, 1588027.5, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 6, 6, 8, 8], "right_children": [2, 4, -1, 6, 8, -1, 10, -1, 12, -1, -1, -1, -1], "split_conditions": [3.5387294, 0.0526399, 34.929226, -0.6172026, -0.20219812, -3.0043232, 2.0, -1.9879744, 2.0, -1.9464698, -0.4525411, 1.1545578, 6.1340504], "split_indices": [27, 27, 0, 26, 18, 0, 1, 0, 13, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [121.0, 117.0, 4.0, 87.0, 30.0, 31.0, 56.0, 4.0, 26.0, 53.0, 3.0, 15.0, 11.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [-10.697618, -157.69815, 39.01636, -128.22351, -10.761745, -191.3714, 454.8207, -2.29447, 0.48333544, 2.0504885, 5.8528447], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 172, "left_children": [1, 3, -1, 5, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [79405680.0, 3462433.5, 0.0, 4873355.0, 0.0, 1088083.5, 303529.5, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6], "right_children": [2, 4, -1, 6, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [2.3330073, 1.4532615, 39.01636, 0.20579594, -10.761745, 0.02337902, -0.99255466, -2.29447, 0.48333544, 2.0504885, 5.8528447], "split_indices": [20, 18, 0, 20, 0, 27, 22, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [136.0, 132.0, 4.0, 129.0, 3.0, 117.0, 12.0, 101.0, 16.0, 5.0, 7.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [94.08096, -107.34093, 2970.6858, -225.27496, 142.31232, 3.1448965, 50.327244, -239.18106, 0.53382355, 45.931637, 546.1763, -2.7627792, -2.0409675, 2.8296576, -0.46998087, 2.1946573, 6.982894], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 173, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, -1, 13, 15, -1, -1, -1, -1, -1, -1], "loss_changes": [79698500.0, 3830165.2, 47711336.0, 353789.5, 1609690.5, 0.0, 0.0, 52892.0, 0.0, 784285.44, 244233.25, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 9, 9, 10, 10], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, -1, 14, 16, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, -0.18306975, 0.49706092, 2.0, 1.9500875, 3.1448965, 50.327244, -0.505889, 0.53382355, -0.75929564, 0.8139121, -2.7627792, -2.0409675, 2.8296576, -0.46998087, 2.1946573, 6.982894], "split_indices": [29, 29, 21, 4, 27, 0, 0, 27, 0, 23, 20, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [136.0, 128.0, 8.0, 87.0, 41.0, 4.0, 4.0, 83.0, 4.0, 34.0, 7.0, 37.0, 46.0, 9.0, 25.0, 3.0, 4.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "17", "size_leaf_vector": "1"}}, {"base_weights": [3.437896, -109.68664, 40.18524, -190.46504, 275.97092, -252.31151, -84.52719, 535.3923, -8.687729, -2.597449, -0.5726599, 4.319331, -1.3170372, 3.0589066, 12.085992], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 174, "left_children": [1, 3, -1, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [66300864.0, 4476252.0, 0.0, 751761.0, 7889375.5, 92383.0, 1153273.5, 2874694.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7], "right_children": [2, 4, -1, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [2.1903315, -0.08745988, 40.18524, -0.25530338, 1.4532615, 2.0, -0.6772184, 0.8306982, -8.687729, -2.597449, -0.5726599, 4.319331, -1.3170372, 3.0589066, 12.085992], "split_indices": [29, 18, 0, 29, 18, 3, 17, 29, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [144.0, 141.0, 3.0, 117.0, 24.0, 73.0, 44.0, 20.0, 4.0, 70.0, 3.0, 3.0, 41.0, 16.0, 4.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [52.00165, -110.29091, 2905.9204, -161.77628, 662.7919, 3.7504628, 53.430077, -218.65215, 59.313637, 2.1552565, 8.504874, -1.7349294, -2.9178221, 2.7040627, -0.3863873], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 175, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, -1, -1, -1, -1, -1, -1], "loss_changes": [68869900.0, 5719320.0, 47339240.0, 1685417.5, 572140.25, 0.0, 0.0, 299396.0, 589428.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, 1.9500875, 4.0, 0.35915467, -0.89633024, 3.7504628, 53.430077, 5.0, -0.75929564, 2.1552565, 8.504874, -1.7349294, -2.9178221, 2.7040627, -0.3863873], "split_indices": [29, 27, 13, 26, 22, 0, 0, 8, 23, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [147.0, 140.0, 7.0, 132.0, 8.0, 4.0, 3.0, 105.0, 27.0, 3.0, 5.0, 67.0, 38.0, 8.0, 19.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [5.8184285, -136.60162, 36.791122, -215.5024, 99.45521, -229.60167, 1.6968124, 291.7135, -218.75859, -2.3688333, -0.065180294, 0.5998855, 7.465793, 1.6878102, -8.725982], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 176, "left_children": [1, 3, -1, 5, 7, 9, -1, 11, 13, -1, -1, -1, -1, -1, -1], "loss_changes": [70081976.0, 2424077.8, 0.0, 565754.0, 2082737.2, 151127.0, 0.0, 2168612.0, 3441401.5, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 7, 7, 8, 8], "right_children": [2, 4, -1, 6, 8, 10, -1, 12, 14, -1, -1, -1, -1, -1, -1], "split_conditions": [3.5387294, 0.0526399, 36.791122, 2.0, 3.0, -0.0155352615, 1.6968124, 0.22625422, 0.558481, -2.3688333, -0.065180294, 0.5998855, 7.465793, 1.6878102, -8.725982], "split_indices": [27, 27, 0, 3, 0, 29, 0, 29, 19, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [132.0, 128.0, 4.0, 96.0, 32.0, 93.0, 3.0, 20.0, 12.0, 90.0, 3.0, 14.0, 6.0, 8.0, 4.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [101.31994, -76.57625, 39.685165, -180.4616, 394.09433, -231.5614, -33.270317, 634.46893, 177.07182, -3.0745604, -1.9554152, -1.0246805, 1.9883229, 2.4194558, 7.514095, -0.28549042, 3.3486857], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 177, "left_children": [1, 3, -1, 5, 7, 9, 11, 13, 15, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [93866790.0, 6476444.0, 0.0, 804585.75, 1139573.0, 158526.5, 486132.6, 323021.0, 463839.16, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8], "right_children": [2, 4, -1, 6, 8, 10, 12, 14, 16, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, -0.009964261, 39.685165, -0.22761919, 7.0, -0.6003495, 0.43023095, 0.20579594, 1.3137271, -3.0745604, -1.9554152, -1.0246805, 1.9883229, 2.4194558, 7.514095, -0.28549042, 3.3486857], "split_indices": [29, 20, 0, 29, 11, 27, 22, 20, 26, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [135.0, 130.0, 5.0, 107.0, 23.0, 79.0, 28.0, 10.0, 13.0, 23.0, 56.0, 22.0, 6.0, 3.0, 7.0, 6.0, 7.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "17", "size_leaf_vector": "1"}}, {"base_weights": [101.65723, -81.218956, 3083.56, -133.43959, 641.4764, 49.976704, -0.7994314, -208.34395, 46.29063, 2.0909848, 8.227989, -2.217259, 0.5544504, 1.2069832, -1.6666995], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 178, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, -1, -1, -1, -1, -1, -1], "loss_changes": [75504760.0, 5029437.0, 48842384.0, 1665558.0, 533449.5, 0.0, 0.0, 319421.0, 606410.7, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, 1.9500875, -0.9297316, -0.18306975, -0.89633024, 49.976704, -0.7994314, 2.0, -0.30668214, 2.0909848, 8.227989, -2.217259, 0.5544504, 1.2069832, -1.6666995], "split_indices": [29, 27, 22, 29, 22, 0, 0, 4, 28, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [137.0, 130.0, 7.0, 122.0, 8.0, 4.0, 3.0, 86.0, 36.0, 3.0, 5.0, 82.0, 4.0, 27.0, 9.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [24.555449, -105.27153, 36.02889, -207.01154, 178.0434, -156.52768, -284.5463, 341.9888, -34.944637, -1.8554815, 3.251415, -1.5024105, -3.0440967, 0.88755155, 6.1166024, -2.0050962, 4.024933], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 179, "left_children": [1, 3, -1, 5, 7, 9, 11, 13, 15, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [66347750.0, 4014636.0, 0.0, 354671.0, 1303962.5, 954924.6, 58262.0, 1379730.0, 1311898.5, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8], "right_children": [2, 4, -1, 6, 8, 10, 12, 14, 16, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [3.5387294, 0.4472843, 36.02889, 5.0, 4.0, -0.147082, -0.57941943, 0.8531112, 1.1987189, -1.8554815, 3.251415, -1.5024105, -3.0440967, 0.88755155, 6.1166024, -2.0050962, 4.024933], "split_indices": [27, 26, 0, 8, 8, 18, 17, 27, 20, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [141.0, 137.0, 4.0, 101.0, 36.0, 63.0, 38.0, 20.0, 16.0, 60.0, 3.0, 6.0, 32.0, 11.0, 9.0, 12.0, 4.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "17", "size_leaf_vector": "1"}}, {"base_weights": [55.302982, -59.404266, 33.01948, -128.60146, 15.304821, -205.04787, 109.01411, 0.5769235, -2.1890132, -0.07667511, 3.8982675], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 180, "left_children": [1, 3, -1, 5, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [54568884.0, 15818907.0, 0.0, 2510948.0, 0.0, 391191.0, 1117060.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6], "right_children": [2, 4, -1, 6, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [3.5387294, 2.8455322, 33.01948, 0.4472843, 15.304821, -0.8636659, 1.1800077, 0.5769235, -2.1890132, -0.07667511, 3.8982675], "split_indices": [27, 25, 0, 26, 0, 28, 27, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [145.0, 141.0, 4.0, 136.0, 5.0, 103.0, 33.0, 5.0, 98.0, 24.0, 9.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [56.887634, -120.886604, 2783.068, -186.58202, 330.10666, -4.795756, 48.365894, -220.52878, 8.099389, -0.9308764, 530.9393, -2.981535, -1.9623942, -1.0935931, 1.2466927, 2.0835583, 6.258511], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 181, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, -1, 15, -1, -1, -1, -1, -1, -1], "loss_changes": [63340504.0, 3705845.0, 55919210.0, 715558.25, 1409327.1, 0.0, 0.0, 114903.0, 246401.77, 0.0, 206303.75, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 10, 10], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, -1, 16, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, 0.092575856, 0.49706092, -0.17754829, -0.9617159, -4.795756, 48.365894, -0.6035461, -0.12193202, -0.9308764, 0.3882633, -2.981535, -1.9623942, -1.0935931, 1.2466927, 2.0835583, 6.258511], "split_indices": [29, 20, 21, 18, 22, 0, 0, 27, 29, 0, 20, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [129.0, 122.0, 7.0, 107.0, 15.0, 3.0, 4.0, 91.0, 16.0, 5.0, 10.0, 19.0, 72.0, 8.0, 8.0, 3.0, 7.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "17", "size_leaf_vector": "1"}}, {"base_weights": [66.577286, -130.81102, 3290.5012, -201.87874, 104.94867, 2.5982432, 62.56197, -214.87926, 0.90687054, 283.00192, -58.32471, -2.7051463, -1.7760894, -0.90553886, 3.8394537, -3.752956, 1.0501279], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 182, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, -1, 13, 15, -1, -1, -1, -1, -1, -1], "loss_changes": [88249100.0, 2216089.0, 70278350.0, 403653.5, 917740.56, 0.0, 0.0, 153043.5, 0.0, 600417.1, 919582.75, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 9, 9, 10, 10], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, -1, 14, 16, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2432504, 0.0526399, 4.0, -0.0155352615, 3.0, 2.5982432, 62.56197, -0.6172026, 0.90687054, -0.21838841, 0.19972922, -2.7051463, -1.7760894, -0.90553886, 3.8394537, -3.752956, 1.0501279], "split_indices": [29, 27, 13, 29, 8, 0, 0, 26, 0, 19, 26, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [137.0, 130.0, 7.0, 100.0, 30.0, 4.0, 3.0, 96.0, 4.0, 14.0, 16.0, 36.0, 60.0, 3.0, 11.0, 5.0, 11.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "17", "size_leaf_vector": "1"}}, {"base_weights": [130.30666, -74.88223, 44.517704, -150.85413, 502.42477, -217.16167, 31.542025, 2.4387782, 647.6808, -1.8015847, -2.7341673, -0.5351133, 3.3081229, 8.448689, 3.2093642], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 183, "left_children": [1, 3, -1, 5, 7, 9, 11, -1, 13, -1, -1, -1, -1, -1, -1], "loss_changes": [117380360.0, 5645863.5, 0.0, 1373517.5, 405288.5, 123470.5, 806802.06, 0.0, 308605.25, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 8, 8], "right_children": [2, 4, -1, 6, 8, 10, 12, -1, 14, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, 1.1800077, 44.517704, -0.20714763, 0.3882633, 5.0, 0.2799022, 2.4387782, 2.0008802, -1.8015847, -2.7341673, -0.5351133, 3.3081229, 8.448689, 3.2093642], "split_indices": [29, 27, 0, 29, 20, 8, 22, 0, 21, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [131.0, 126.0, 5.0, 112.0, 14.0, 82.0, 30.0, 6.0, 8.0, 52.0, 30.0, 24.0, 6.0, 4.0, 4.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [60.72066, -103.07062, 2878.5427, -184.23509, 237.42534, -1.631625, 47.362183, -214.22185, -44.086666, 459.2871, -36.82482, -2.6763663, -1.7829359, 2.713029, -1.1172953, 2.460613, 7.380989, 1.4718215, -3.6069243], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 184, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, 15, 17, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [67219496.0, 3857528.5, 45977250.0, 461221.0, 1659787.5, 0.0, 0.0, 128795.0, 478307.5, 710032.25, 827829.6, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9, 10, 10], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, 16, 18, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, -0.0155352615, 0.49706092, -0.11261157, 4.0, -1.631625, 47.362183, -0.29357222, -0.08898436, 0.41371, 0.2505919, -2.6763663, -1.7829359, 2.713029, -1.1172953, 2.460613, 7.380989, 1.4718215, -3.6069243], "split_indices": [29, 29, 21, 25, 8, 0, 0, 29, 25, 19, 29, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [144.0, 137.0, 7.0, 111.0, 26.0, 3.0, 4.0, 91.0, 20.0, 14.0, 12.0, 34.0, 57.0, 3.0, 17.0, 9.0, 5.0, 8.0, 4.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "19", "size_leaf_vector": "1"}}, {"base_weights": [-95.88368, -133.21614, 9.835151, -103.55569, -11.30588, -163.63101, 368.49408, -2.1103165, -0.14618438, 5.2623906, 1.4495672], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 185, "left_children": [1, 3, -1, 5, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [6014240.5, 4083514.0, 0.0, 4002114.5, 0.0, 872740.75, 487841.5, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6], "right_children": [2, 4, -1, 6, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [2.3330073, 1.4532615, 9.835151, 0.092575856, -11.30588, -0.20714763, 2.0, -2.1103165, -0.14618438, 5.2623906, 1.4495672], "split_indices": [20, 18, 0, 20, 0, 29, 8, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [145.0, 141.0, 4.0, 138.0, 3.0, 123.0, 15.0, 93.0, 30.0, 8.0, 7.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [99.79906, -74.23285, 1976.8364, -150.64212, 479.5921, 372.2223, 50.929844, -190.09178, 6.854134, 235.55713, 11.527827, 8.01994, -3.6546583, -1.4556029, -2.7726815, -1.3994486, 1.9307522, 4.0197997, 0.3546895], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 186, "left_children": [1, 3, 5, 7, 9, 11, -1, 13, 15, 17, -1, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [46206616.0, 5573471.0, 58106308.0, 715836.25, 2356827.8, 3146482.5, 0.0, 316751.0, 683114.9, 418588.7, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 8, 8, 9, 9], "right_children": [2, 4, 6, 8, 10, 12, -1, 14, 16, 18, -1, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2473981, 1.066172, 4.0, 0.19972922, -0.12927626, 4.0, 50.929844, 5.0, -0.5506989, -0.4990367, 11.527827, 8.01994, -3.6546583, -1.4556029, -2.7726815, -1.3994486, 1.9307522, 4.0197997, 0.3546895], "split_indices": [25, 26, 13, 26, 17, 0, 0, 8, 28, 17, 0, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [140.0, 129.0, 11.0, 114.0, 15.0, 8.0, 3.0, 91.0, 23.0, 12.0, 3.0, 5.0, 3.0, 62.0, 29.0, 13.0, 10.0, 6.0, 6.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "19", "size_leaf_vector": "1"}}, {"base_weights": [43.881214, -91.089775, 36.793415, -201.5527, 134.3128, -2.7161376, -141.27512, 37.463947, 469.52747, -1.1198174, -4.5745196, 0.927735, -3.2398996, 7.0260773, 1.9702264], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 187, "left_children": [1, 3, -1, 5, 7, -1, 9, 11, 13, -1, -1, -1, -1, -1, -1], "loss_changes": [68540264.0, 3388392.8, 0.0, 345889.75, 1443291.1, 0.0, 421073.12, 749741.3, 496635.5, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 6, 6, 7, 7, 8, 8], "right_children": [2, 4, -1, 6, 8, -1, 10, 12, 14, -1, -1, -1, -1, -1, -1], "split_conditions": [2.4534156, 0.19972922, 36.793415, -0.505889, 1.455611, -2.7161376, -0.049914382, 0.91349506, 7.0, -1.1198174, -4.5745196, 0.927735, -3.2398996, 7.0260773, 1.9702264], "split_indices": [27, 26, 0, 27, 27, 0, 18, 27, 11, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [138.0, 134.0, 4.0, 90.0, 44.0, 40.0, 50.0, 35.0, 9.0, 47.0, 3.0, 31.0, 4.0, 4.0, 5.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [56.35228, -109.15978, 2007.29, -139.52681, 5.4166327, 236.92802, 50.461662, -208.09157, -13.007408, 6.784426, -3.7417226, -2.8712544, -1.7747021, 0.566213, -2.3337991], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 188, "left_children": [1, 3, 5, 7, -1, 9, -1, 11, 13, -1, -1, -1, -1, -1, -1], "loss_changes": [45425828.0, 2644799.2, 57982924.0, 1081617.8, 0.0, 2412362.8, 0.0, 145027.75, 703723.44, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 7, 7, 8, 8], "right_children": [2, 4, 6, 8, -1, 10, -1, 12, 14, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2473981, 1.9500875, 4.0, -0.2967835, 5.4166327, 4.0, 50.461662, -0.6003495, 5.0, 6.784426, -3.7417226, -2.8712544, -1.7747021, 0.566213, -2.3337991], "split_indices": [25, 27, 13, 27, 0, 0, 0, 27, 8, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [139.0, 129.0, 10.0, 124.0, 5.0, 7.0, 3.0, 80.0, 44.0, 4.0, 3.0, 20.0, 60.0, 34.0, 10.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [65.60642, -85.92305, 2614.478, -181.1865, 202.39148, 1.764884, 50.083202, -232.04852, -99.85154, 375.79065, -16.163282, -2.4116044, -0.39610657, -1.402063, 2.18724, -0.90698093, 4.689322, -3.0769188, 0.7477346], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 189, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, 15, 17, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [55069436.0, 3744556.0, 45804870.0, 398756.5, 1294615.5, 0.0, 0.0, 99135.0, 557756.25, 868108.75, 447201.1, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9, 10, 10], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, 16, 18, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, 0.0526399, 4.0, -0.4363957, 2.0, 1.764884, 50.083202, 2.0, 11.0, -0.22010031, -0.8325524, -2.4116044, -0.39610657, -1.402063, 2.18724, -0.90698093, 4.689322, -3.0769188, 0.7477346], "split_indices": [29, 27, 13, 27, 8, 0, 0, 3, 11, 18, 28, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [141.0, 134.0, 7.0, 101.0, 33.0, 4.0, 3.0, 61.0, 40.0, 18.0, 15.0, 58.0, 3.0, 36.0, 4.0, 3.0, 15.0, 3.0, 12.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "19", "size_leaf_vector": "1"}}, {"base_weights": [59.310055, -95.981895, 2594.8145, -161.82971, 220.2483, -5.800779, 46.157856, -213.65697, -35.992508, 380.44708, -127.66776, -2.8282, -1.8612217, -1.0261996, 2.2612658, 2.566169, 6.8775835, 1.1182147, -3.9513736], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 190, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, 15, 17, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [54585896.0, 2763459.5, 54008840.0, 703160.75, 1330517.9, 0.0, 0.0, 93952.25, 599515.56, 432285.75, 556661.94, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9, 10, 10], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, 16, 18, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2220678, -0.0155352615, 0.49706092, -0.2967835, 5.0, -5.800779, 46.157856, -0.6035461, 0.4568698, 6.0, 0.22625422, -2.8282, -1.8612217, -1.0261996, 2.2612658, 2.566169, 6.8775835, 1.1182147, -3.9513736], "split_indices": [29, 29, 21, 27, 8, 0, 0, 27, 22, 13, 29, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [137.0, 130.0, 7.0, 108.0, 22.0, 3.0, 4.0, 76.0, 32.0, 15.0, 7.0, 19.0, 57.0, 26.0, 6.0, 12.0, 3.0, 4.0, 3.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "19", "size_leaf_vector": "1"}}, {"base_weights": [24.626007, -114.748276, 30.635433, -201.85674, 112.007164, -182.61826, -347.1119, -2.7176077, 180.46655, -2.0459096, -0.14196123, -1.742102, -4.33328, 0.89088637, 6.5606956], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 191, "left_children": [1, 3, -1, 5, 7, 9, 11, -1, 13, -1, -1, -1, -1, -1, -1], "loss_changes": [57954220.0, 2610171.5, 0.0, 202034.5, 1021117.9, 315333.0, 73518.25, 0.0, 1332184.8, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 8, 8], "right_children": [2, 4, -1, 6, 8, 10, 12, -1, 14, -1, -1, -1, -1, -1, -1], "split_conditions": [3.2148452, 0.4472843, 30.635433, -0.2066361, 1.0, -0.27623388, 1.0, -2.7176077, 6.0, -2.0459096, -0.14196123, -1.742102, -4.33328, 0.89088637, 6.5606956], "split_indices": [27, 26, 0, 20, 11, 20, 2, 0, 13, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [135.0, 130.0, 5.0, 94.0, 36.0, 85.0, 9.0, 5.0, 31.0, 75.0, 10.0, 4.0, 5.0, 27.0, 4.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-85.158356, -125.25206, 10.945707, -95.28578, -9.452308, -152.41103, 360.52463, -2.1505191, -0.560734, 1.3770366, 4.8043685], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 192, "left_children": [1, 3, -1, 5, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [7184658.0, 3472727.0, 0.0, 3726325.0, 0.0, 748283.25, 361283.5, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6], "right_children": [2, 4, -1, 6, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [2.8577383, 1.4532615, 10.945707, 0.092575856, -9.452308, -0.25530338, 0.33892554, -2.1505191, -0.560734, 1.3770366, 4.8043685], "split_indices": [20, 18, 0, 20, 0, 29, 20, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [148.0, 144.0, 4.0, 140.0, 4.0, 125.0, 15.0, 75.0, 50.0, 6.0, 9.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [61.511623, -91.36767, 3219.524, -182.62674, 200.66162, 6.924833, 49.416584, -237.8641, -110.47789, 64.52472, 491.8126, -2.1919804, -4.0679755, -1.4447415, 1.1898113, 4.5370436, -0.09163606, 6.5344577, 1.6714361], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 193, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, 15, 17, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [73196460.0, 3900941.5, 27040752.0, 416056.0, 1355484.0, 0.0, 0.0, 106274.0, 407229.62, 721151.9, 467948.75, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9, 10, 10], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, 16, 18, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, 0.4472843, 4.0, -0.39440298, 0.33892554, 6.924833, 49.416584, -0.24877441, 0.2040027, 0.49679533, 2.5527406, -2.1919804, -4.0679755, -1.4447415, 1.1898113, 4.5370436, -0.09163606, 6.5344577, 1.6714361], "split_indices": [29, 26, 13, 26, 20, 0, 0, 19, 25, 26, 21, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [150.0, 144.0, 6.0, 110.0, 34.0, 3.0, 3.0, 61.0, 49.0, 24.0, 10.0, 57.0, 4.0, 43.0, 6.0, 3.0, 21.0, 6.0, 4.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "19", "size_leaf_vector": "1"}}, {"base_weights": [75.57783, -89.63711, 2545.4155, -133.76561, 543.01355, 0.38550606, 45.43177, -190.88249, 15.912726, 7.852499, 1.9215442, -1.9961829, 0.5118491, 4.3433256, -0.35284004], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 194, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, -1, -1, -1, -1, -1, -1], "loss_changes": [58580144.0, 3841514.5, 44897468.0, 1088798.5, 613930.75, 0.0, 0.0, 204780.5, 786547.06, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, 1.9500875, 0.49706092, 0.19972922, 2.0008802, 0.38550606, 45.43177, 2.0, -0.44094184, 7.852499, 1.9215442, -1.9961829, 0.5118491, 4.3433256, -0.35284004], "split_indices": [29, 27, 21, 26, 21, 0, 0, 4, 25, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [142.0, 134.0, 8.0, 126.0, 8.0, 4.0, 4.0, 91.0, 35.0, 4.0, 4.0, 88.0, 3.0, 3.0, 32.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [14.46717, -110.31976, 34.057983, -77.575874, -11.714491, -138.14708, 492.1619, -1.7901071, 0.6496123, 1.9278529, 6.551616], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 195, "left_children": [1, 3, -1, 5, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [59623190.0, 4634387.0, 0.0, 4657746.0, 0.0, 1015909.25, 507987.75, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6], "right_children": [2, 4, -1, 6, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [2.3330073, 1.4594278, 34.057983, 1.1800077, -11.714491, 0.0526399, 0.3882633, -1.7901071, 0.6496123, 1.9278529, 6.551616], "split_indices": [20, 19, 0, 27, 0, 27, 20, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [139.0, 135.0, 4.0, 132.0, 3.0, 120.0, 12.0, 100.0, 20.0, 5.0, 7.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [46.409992, -59.48665, 38.206375, -118.20054, 13.008306, -177.63837, 76.29975, -1.5964686, -3.5379703, 5.547316, -0.08816078], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 196, "left_children": [1, 3, -1, 5, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [58580640.0, 11574981.0, 0.0, 1608936.8, 0.0, 279418.0, 1348775.4, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6], "right_children": [2, 4, -1, 6, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [3.5387294, 2.8455322, 38.206375, 0.4472843, 13.008306, -0.2066361, -0.44094184, -1.5964686, -3.5379703, 5.547316, -0.08816078], "split_indices": [27, 25, 0, 26, 0, 20, 25, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [145.0, 142.0, 3.0, 137.0, 5.0, 105.0, 32.0, 97.0, 8.0, 4.0, 28.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [88.3657, -38.600925, 29.515648, -99.15852, 12.8999, -188.18861, 35.41575, -2.0465062, -0.54125756, 0.91036373, -6.68671], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 197, "left_children": [1, 3, -1, 5, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [51372056.0, 11069849.0, 0.0, 1576212.0, 0.0, 163245.75, 2136388.5, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6], "right_children": [2, 4, -1, 6, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [3.5387294, 2.8455322, 29.515648, -0.2967835, 12.8999, -0.2271967, 1.1764319, -2.0465062, -0.54125756, 0.91036373, -6.68671], "split_indices": [27, 25, 0, 27, 0, 18, 18, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [140.0, 135.0, 5.0, 130.0, 5.0, 78.0, 52.0, 69.0, 9.0, 49.0, 3.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [30.546196, -99.8168, 34.6604, -150.5019, 419.56775, -185.36897, -1.8529805, 1.4963741, 5.9098406, -2.5060554, -1.6202035, 1.0085568, -1.8425138], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 198, "left_children": [1, 3, -1, 5, 7, 9, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [61255450.0, 3538027.0, 0.0, 626775.0, 466738.0, 108627.75, 468204.72, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, -1, 6, 8, 10, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [3.2148452, 1.4375046, 34.6604, 0.0526399, 0.8139121, -0.9018909, 3.0, 1.4963741, 5.9098406, -2.5060554, -1.6202035, 1.0085568, -1.8425138], "split_indices": [27, 26, 0, 27, 20, 26, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [135.0, 131.0, 4.0, 120.0, 11.0, 97.0, 23.0, 5.0, 6.0, 23.0, 74.0, 15.0, 8.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [66.75583, -80.437614, 2689.5066, -114.18278, 503.27246, -0.23548846, 54.08474, -174.35071, 24.627613, 8.008567, 1.6453058, -2.6388147, -1.4695609, -0.34669304, 4.7037864], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 199, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, -1, -1, -1, -1, -1, -1], "loss_changes": [58124356.0, 2874153.8, 59141584.0, 1140171.8, 674572.0, 0.0, 0.0, 194182.75, 1126481.2, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2432504, 1.9500875, 0.49706092, -0.20922844, 2.0008802, -0.23548846, 54.08474, -0.6003495, 0.2799022, 8.008567, 1.6453058, -2.6388147, -1.4695609, -0.34669304, 4.7037864], "split_indices": [29, 27, 21, 29, 21, 0, 0, 27, 22, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [149.0, 142.0, 7.0, 135.0, 7.0, 4.0, 3.0, 94.0, 41.0, 3.0, 4.0, 20.0, 74.0, 37.0, 4.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-9.869841, -77.241066, 1300.4526, -117.48192, 503.64444, 29.233007, -6.4748363, -186.96967, 29.251814, 7.4332566, 1.6321433, -2.5229764, -1.5498729, 0.6381314, -3.1500905], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 200, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, -1, -1, -1, -1, -1, -1], "loss_changes": [12635807.0, 3238174.5, 24021448.0, 1310598.1, 612940.5, 0.0, 0.0, 142629.25, 519797.28, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, 1.9500875, 1.0741702, -0.20714763, 2.0008802, 29.233007, -6.4748363, -0.9018909, 1.4532615, 7.4332566, 1.6321433, -2.5229764, -1.5498729, 0.6381314, -3.1500905], "split_indices": [29, 27, 26, 29, 21, 0, 0, 26, 18, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [141.0, 135.0, 6.0, 127.0, 8.0, 3.0, 3.0, 86.0, 41.0, 4.0, 4.0, 26.0, 60.0, 38.0, 3.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [38.973557, -84.28733, 32.359818, -157.57999, 135.92078, -110.01033, -256.83987, 5.6527667, 57.203518, -1.4948744, 2.5534558, -2.8160923, -1.3167223, -0.7499342, 2.8035245], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 201, "left_children": [1, 3, -1, 5, 7, 9, 11, -1, 13, -1, -1, -1, -1, -1, -1], "loss_changes": [53077930.0, 2119573.2, 0.0, 434440.75, 1082926.6, 1018937.94, 58988.625, 0.0, 876533.75, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 8, 8], "right_children": [2, 4, -1, 6, 8, 10, 12, -1, 14, -1, -1, -1, -1, -1, -1], "split_conditions": [3.2148452, 0.4472843, 32.359818, 5.0, -0.44094184, 0.02337902, 0.7918346, 5.6527667, 1.1800077, -1.4948744, 2.5534558, -2.8160923, -1.3167223, -0.7499342, 2.8035245], "split_indices": [27, 26, 0, 8, 25, 27, 24, 0, 27, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [133.0, 129.0, 4.0, 97.0, 32.0, 67.0, 30.0, 4.0, 28.0, 61.0, 6.0, 24.0, 6.0, 18.0, 10.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [97.53091, -27.659613, 28.563488, -122.292435, 1033.0128, -93.31062, -9.6114855, 199.51814, 24.417238, -1.5620593, 1.9395508, 3.0731988, 0.14861414], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 202, "left_children": [1, 3, -1, 5, 7, 9, -1, 11, -1, -1, -1, -1, -1], "loss_changes": [47741436.0, 13476037.0, 0.0, 2900532.0, 12428252.0, 2198412.5, 0.0, 154651.12, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 7, 7], "right_children": [2, 4, -1, 6, 8, 10, -1, 12, -1, -1, -1, -1, -1], "split_conditions": [3.5387294, 1.7035879, 28.563488, 1.0485128, -0.33291245, 0.0526399, -9.6114855, 2.0, 24.417238, -1.5620593, 1.9395508, 3.0731988, 0.14861414], "split_indices": [27, 21, 0, 25, 17, 27, 0, 8, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [137.0, 132.0, 5.0, 122.0, 10.0, 119.0, 3.0, 7.0, 3.0, 98.0, 21.0, 4.0, 3.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [67.28986, -88.82186, 2401.5002, -169.46234, 165.56273, -0.5216906, 43.74889, -126.10363, -261.7206, 5.2531686, 445.65836, -1.5003054, 3.1813672, -2.196209, -3.448954, 4.442913, -0.87447137, 6.3374753, 2.2075925], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 203, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, 15, 17, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [52322396.0, 2797406.0, 43807068.0, 378978.0, 1479380.0, 0.0, 0.0, 813023.4, 36249.5, 934265.0, 367622.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9, 10, 10], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, 16, 18, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, 0.4472843, 0.49706092, 5.0, 0.20579594, -0.5216906, 43.74889, -0.14510837, 2.0, 0.49679533, 2.0008802, -1.5003054, 3.1813672, -2.196209, -3.448954, 4.442913, -0.87447137, 6.3374753, 2.2075925], "split_indices": [29, 26, 21, 8, 20, 0, 0, 18, 7, 26, 21, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [142.0, 134.0, 8.0, 102.0, 32.0, 4.0, 4.0, 71.0, 31.0, 21.0, 11.0, 68.0, 3.0, 23.0, 8.0, 3.0, 18.0, 5.0, 6.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "19", "size_leaf_vector": "1"}}, {"base_weights": [91.47424, -69.24314, 36.656002, -176.81804, 162.52509, -225.23555, -129.73051, 78.06366, 522.2734, -2.3208575, -0.98687124, -0.82062125, -1.9474158, 1.2367325, -3.1770642, 7.1670885, 1.4863565], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 204, "left_children": [1, 3, -1, 5, 7, 9, 11, 13, 15, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [80099140.0, 3369686.5, 0.0, 180491.5, 1265718.1, 15205.75, 135841.06, 689105.25, 474572.5, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8], "right_children": [2, 4, -1, 6, 8, 10, 12, 14, 16, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, -0.18306975, 36.656002, -0.40619367, 1.9500875, 5.0, 5.0, 1.562716, 2.0008802, -2.3208575, -0.98687124, -0.82062125, -1.9474158, 1.2367325, -3.1770642, 7.1670885, 1.4863565], "split_indices": [29, 29, 0, 25, 27, 0, 8, 20, 21, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [138.0, 133.0, 5.0, 91.0, 42.0, 43.0, 48.0, 35.0, 7.0, 40.0, 3.0, 29.0, 19.0, 32.0, 3.0, 4.0, 3.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "17", "size_leaf_vector": "1"}}, {"base_weights": [73.26906, -77.90338, 2615.1577, -129.0257, 284.52954, 54.506565, -1.7625285, -174.05833, -32.185482, 91.64094, 8.884823, -2.1532753, -1.3257794, -0.8072674, 2.4822922, -1.506291, 2.4298308], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 205, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, 15, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [54769404.0, 2538032.0, 64281556.0, 513332.5, 1898905.9, 0.0, 0.0, 110078.25, 550877.6, 549928.9, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, 16, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2473981, -0.009964261, -0.011381618, -0.28713, 7.0, 54.506565, -1.7625285, -0.30184492, 0.43023095, 2.0, 8.884823, -2.1532753, -1.3257794, -0.8072674, 2.4822922, -1.506291, 2.4298308], "split_indices": [25, 20, 17, 20, 13, 0, 0, 18, 22, 11, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [141.0, 134.0, 7.0, 118.0, 16.0, 3.0, 4.0, 80.0, 38.0, 13.0, 3.0, 38.0, 42.0, 33.0, 5.0, 5.0, 8.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "17", "size_leaf_vector": "1"}}, {"base_weights": [15.755168, -106.53821, 27.851292, -137.46667, 345.15976, -114.42309, -5.869323, 4.1482353, 1.5434915, -1.695247, 0.28958812], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 206, "left_children": [1, 3, -1, 5, 7, 9, -1, -1, -1, -1, -1], "loss_changes": [48050316.0, 1947391.5, 0.0, 1258505.5, 55548.75, 976694.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5], "right_children": [2, 4, -1, 6, 8, 10, -1, -1, -1, -1, -1], "split_conditions": [3.2148452, 1.722193, 27.851292, 0.36177117, 2.0, -0.20714763, -5.869323, 4.1482353, 1.5434915, -1.695247, 0.28958812], "split_indices": [27, 26, 0, 29, 8, 29, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [140.0, 135.0, 5.0, 127.0, 8.0, 122.0, 5.0, 5.0, 3.0, 88.0, 34.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-64.22004, -117.88118, 586.1256, -100.12523, -6.077474, 9.295325, 2.0224886, -171.67862, 36.695156, -2.438986, -1.3835428, -0.48900858, 1.6041899], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 207, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, 11, -1, -1, -1, -1], "loss_changes": [4570925.0, 982843.4, 1130149.0, 1151738.8, 0.0, 0.0, 0.0, 151532.0, 442056.75, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 8, 8], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, 12, -1, -1, -1, -1], "split_conditions": [1.8196839, 0.45794836, 2.0008802, -0.2967835, -6.077474, 9.295325, 2.0224886, -0.9018909, 9.0, -2.438986, -1.3835428, -0.48900858, 1.6041899], "split_indices": [27, 29, 21, 27, 0, 0, 0, 26, 11, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [128.0, 119.0, 9.0, 116.0, 3.0, 4.0, 5.0, 76.0, 40.0, 22.0, 54.0, 24.0, 16.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [65.92796, -69.791176, 32.89596, -104.26761, 455.0988, -172.75665, 8.371743, 6.365487, 0.6912417, -2.4124472, -1.4456177, 0.6497377, -1.6642463], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 208, "left_children": [1, 3, -1, 5, 7, 9, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [64981900.0, 2635192.5, 0.0, 1042928.6, 586244.1, 127291.75, 525282.25, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, -1, 6, 8, 10, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, 1.9500875, 32.89596, -0.2967835, 2.5527406, -0.6003495, 5.0, 6.365487, 0.6912417, -2.4124472, -1.4456177, 0.6497377, -1.6642463], "split_indices": [29, 27, 0, 27, 21, 27, 8, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [147.0, 142.0, 5.0, 134.0, 8.0, 83.0, 51.0, 5.0, 3.0, 22.0, 61.0, 39.0, 12.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [37.156063, -60.204823, 33.381622, -136.96146, 269.79868, 2.2292593, -149.02338, 506.1983, -232.96712, -1.6408896, 0.3491906, 3.2584276, 9.099341, -5.537008, 1.3433998], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 209, "left_children": [1, 3, -1, 5, 7, -1, 9, 11, 13, -1, -1, -1, -1, -1, -1], "loss_changes": [44874356.0, 3481812.5, 0.0, 515057.5, 3208149.8, 0.0, 305032.5, 1014069.0, 1134695.9, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 6, 6, 7, 7, 8, 8], "right_children": [2, 4, -1, 6, 8, -1, 10, 12, 14, -1, -1, -1, -1, -1, -1], "split_conditions": [2.830827, -0.031931423, 33.381622, -0.95644623, 5.0, 2.2292593, -0.0155352615, 0.8306982, 1.1987189, -1.6408896, 0.3491906, 3.2584276, 9.099341, -5.537008, 1.3433998], "split_indices": [29, 20, 0, 23, 8, 0, 29, 29, 20, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [138.0, 135.0, 3.0, 110.0, 25.0, 3.0, 107.0, 17.0, 8.0, 99.0, 8.0, 13.0, 4.0, 4.0, 4.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [64.75206, -33.370068, 33.356804, -115.94478, 1002.14087, -86.041145, -9.989945, -2.2933664, 25.093906, -1.3096336, 3.0068078, -2.19595, 2.155566], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 210, "left_children": [1, 3, -1, 5, 7, 9, -1, 11, -1, -1, -1, -1, -1], "loss_changes": [44081852.0, 11574045.0, 0.0, 3214737.5, 15145337.0, 2158794.8, 0.0, 378709.62, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 7, 7], "right_children": [2, 4, -1, 6, 8, 10, -1, 12, -1, -1, -1, -1, -1], "split_conditions": [4.2449884, 1.7035879, 33.356804, 1.1818613, -0.31147707, -0.101361185, -9.989945, 3.0, 25.093906, -1.3096336, 3.0068078, -2.19595, 2.155566], "split_indices": [19, 21, 0, 19, 17, 19, 0, 14, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [136.0, 133.0, 3.0, 124.0, 9.0, 121.0, 3.0, 6.0, 3.0, 109.0, 12.0, 3.0, 3.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [-28.013382, -85.14491, 1085.9583, -115.97608, 499.58322, -5.2229295, 24.227451, -97.26445, -6.8105154, 7.1295776, 1.6128787, -1.6100199, 0.7487145], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 211, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, -1, -1, -1, -1, -1], "loss_changes": [9129658.0, 2509689.2, 16314796.0, 1308233.8, 390206.38, 0.0, 0.0, 1405446.1, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, -1, -1, -1, -1, -1], "split_conditions": [2.8455322, 1.9500875, 1.0, 1.4532615, 7.0, -5.2229295, 24.227451, -0.18306975, -6.8105154, 7.1295776, 1.6128787, -1.6100199, 0.7487145], "split_indices": [25, 27, 4, 18, 11, 0, 0, 29, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [141.0, 135.0, 6.0, 129.0, 6.0, 3.0, 3.0, 126.0, 3.0, 3.0, 3.0, 92.0, 34.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [73.65975, -24.385231, 32.907543, -108.5331, 826.1295, -77.34722, -10.013582, 21.647896, 139.36626, -1.3340281, 1.7331823, 4.705006, -2.1962132], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 212, "left_children": [1, 3, -1, 5, 7, 9, -1, -1, 11, -1, -1, -1, -1], "loss_changes": [42662270.0, 9536699.0, 0.0, 3291507.5, 10730180.0, 1681159.1, 0.0, 0.0, 1173215.1, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 8, 8], "right_children": [2, 4, -1, 6, 8, 10, -1, -1, 12, -1, -1, -1, -1], "split_conditions": [2.830827, 1.7035879, 32.907543, 1.0485128, 1.5831932, 0.0526399, -10.013582, 21.647896, 1.0, -1.3340281, 1.7331823, 4.705006, -2.1962132], "split_indices": [29, 21, 0, 25, 27, 27, 0, 0, 16, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [134.0, 131.0, 3.0, 120.0, 11.0, 117.0, 3.0, 3.0, 8.0, 96.0, 21.0, 4.0, 4.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [75.74022, -33.01142, 30.621574, -112.5616, 740.54504, -83.36416, -9.822176, 72.155304, 22.26358, -1.3700559, 2.5915933, 2.4083307, -1.4466907], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 213, "left_children": [1, 3, -1, 5, 7, 9, -1, 11, -1, -1, -1, -1, -1], "loss_changes": [46219836.0, 8575338.0, 0.0, 3117370.2, 12749454.0, 2295429.5, 0.0, 400585.22, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 7, 7], "right_children": [2, 4, -1, 6, 8, 10, -1, 12, -1, -1, -1, -1, -1], "split_conditions": [3.5387294, 1.7035879, 30.621574, 1.1764319, 1.0, -0.06937444, -9.822176, 3.0, 22.26358, -1.3700559, 2.5915933, 2.4083307, -1.4466907], "split_indices": [27, 21, 0, 18, 4, 20, 0, 8, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [141.0, 137.0, 4.0, 125.0, 12.0, 122.0, 3.0, 9.0, 3.0, 106.0, 16.0, 5.0, 4.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [76.26112, -65.74696, 2783.8215, -125.824844, 201.22157, -2.661121, 51.378246, 2.6947303, -139.42734, 286.09848, -3.1588185, -1.6464496, -0.055876993, 2.016185, 5.329867], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 214, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, 11, 13, -1, -1, -1, -1, -1], "loss_changes": [54012668.0, 2174524.2, 51624600.0, 629032.6, 1187624.0, 0.0, 0.0, 0.0, 360200.5, 351322.88, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 8, 8, 9, 9], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, 12, 14, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, -0.031931423, 3.0, -0.9199369, 1.4532615, -2.661121, 51.378246, 2.6947303, -0.19738343, 0.59401447, -3.1588185, -1.6464496, -0.055876993, 2.016185, 5.329867], "split_indices": [29, 20, 13, 23, 18, 0, 0, 0, 18, 24, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [139.0, 133.0, 6.0, 109.0, 24.0, 3.0, 3.0, 3.0, 106.0, 21.0, 3.0, 89.0, 17.0, 17.0, 4.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [49.851784, -42.270042, 32.386475, -90.15844, 15.964778, -141.73369, 123.29844, -1.520987, 1.7093936, 2.5292232, -6.852444], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 215, "left_children": [1, 3, -1, 5, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [41850810.0, 11052089.0, 0.0, 1514713.6, 0.0, 382493.0, 3003045.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6], "right_children": [2, 4, -1, 6, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [2.830827, 2.8455322, 32.386475, -0.0155352615, 15.964778, 0.092575856, 1.4532615, -1.520987, 1.7093936, 2.5292232, -6.852444], "split_indices": [29, 25, 0, 29, 0, 20, 18, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [141.0, 138.0, 3.0, 135.0, 3.0, 109.0, 26.0, 106.0, 3.0, 23.0, 3.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [111.6791, -67.21796, 3366.9998, -126.903366, 261.42343, 3.4430873, 55.479153, 2.6859274, -140.6315, 168.18472, 5.0748262, -1.6233801, 0.4292277, 2.2801604, -0.6828823], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 216, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, 11, 13, -1, -1, -1, -1, -1], "loss_changes": [78263864.0, 2543894.5, 44234856.0, 629566.5, 373426.5, 0.0, 0.0, 0.0, 429318.25, 241962.6, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 8, 8, 9, 9], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, 12, 14, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, 0.092575856, 4.0, -0.9199369, 2.2059762, 3.4430873, 55.479153, 2.6859274, 0.21398789, -0.34237808, 5.0748262, -1.6233801, 0.4292277, 2.2801604, -0.6828823], "split_indices": [29, 20, 13, 23, 27, 0, 0, 0, 27, 23, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [133.0, 127.0, 6.0, 108.0, 19.0, 3.0, 3.0, 3.0, 105.0, 15.0, 4.0, 94.0, 11.0, 12.0, 3.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-9.928851, -61.5066, 1015.662, -109.17869, 639.2101, -5.368445, 23.14278, -90.014435, -6.951149, 2.579671, 10.512469, -1.6266904, 0.2657812], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 217, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, -1, -1, -1, -1, -1], "loss_changes": [7732536.0, 4712977.5, 15355353.0, 1408354.0, 1142457.8, 0.0, 0.0, 1088623.1, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, -1, -1, -1, -1, -1], "split_conditions": [2.8455322, 1.9500875, 1.0, 1.4532615, -0.37959817, -5.368445, 23.14278, -0.33963278, -6.951149, 2.579671, 10.512469, -1.6266904, 0.2657812], "split_indices": [25, 27, 4, 18, 23, 0, 0, 27, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [144.0, 138.0, 6.0, 130.0, 8.0, 3.0, 3.0, 127.0, 3.0, 5.0, 3.0, 78.0, 49.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [51.29094, -80.11685, 2615.8162, -140.36751, 159.4399, -4.6469226, 50.423954, -150.52718, 1.6647061, 228.21336, -3.1022933, -2.283088, -1.252842, -1.1203455, 3.1888208], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 218, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, -1, 13, -1, -1, -1, -1, -1], "loss_changes": [48400624.0, 1999761.0, 54669300.0, 367967.75, 975212.44, 0.0, 0.0, 181449.5, 0.0, 806992.4, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 9, 9], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, -1, 14, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, -0.0155352615, 0.49706092, 0.092575856, 1.4532615, -4.6469226, 50.423954, -0.9018909, 1.6647061, -0.14510837, -3.1022933, -2.283088, -1.252842, -1.1203455, 3.1888208], "split_indices": [29, 29, 21, 20, 18, 0, 0, 26, 0, 18, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [142.0, 136.0, 6.0, 109.0, 27.0, 3.0, 3.0, 106.0, 3.0, 24.0, 3.0, 24.0, 82.0, 5.0, 19.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [41.92585, -76.380905, 25.982677, -92.93818, 4.6838264, -74.46483, -4.5305653, -1.1412196, 1.9539006], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 219, "left_children": [1, 3, -1, 5, -1, 7, -1, -1, -1], "loss_changes": [41027100.0, 1216066.8, 0.0, 811089.6, 0.0, 1327894.1, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5], "right_children": [2, 4, -1, 6, -1, 8, -1, -1, -1], "split_conditions": [3.2148452, 2.1555429, 25.982677, 1.4532615, 4.6838264, -0.0155352615, -4.5305653, -1.1412196, 1.9539006], "split_indices": [27, 27, 0, 18, 0, 29, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [134.0, 129.0, 5.0, 126.0, 3.0, 121.0, 5.0, 106.0, 15.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [7.633125, -112.84679, 25.766157, -88.44676, -6.95378, -130.84343, 283.61267, -1.468926, 0.9614492, 4.755858, 0.51109177], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 220, "left_children": [1, 3, -1, 5, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [41455948.0, 1757781.1, 0.0, 1993079.1, 0.0, 422141.38, 555887.94, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6], "right_children": [2, 4, -1, 6, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [3.5387294, 1.2432504, 25.766157, 0.092575856, -6.95378, -0.0155352615, 0.26642248, -1.468926, 0.9614492, 4.755858, 0.51109177], "split_indices": [27, 29, 0, 20, 0, 29, 25, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [132.0, 127.0, 5.0, 123.0, 4.0, 111.0, 12.0, 104.0, 7.0, 6.0, 6.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [17.437027, -96.321014, 25.551437, -70.1645, -9.288417, -112.288376, 289.88425, -1.6142317, -0.12437434, 0.6471089, 4.0777936], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 221, "left_children": [1, 3, -1, 5, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [40373508.0, 2852691.0, 0.0, 2019364.1, 0.0, 576901.4, 345220.75, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6], "right_children": [2, 4, -1, 6, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [3.5387294, 1.2440649, 25.551437, 1.4375046, -9.288417, -0.2967835, 0.16828896, -1.6142317, -0.12437434, 0.6471089, 4.0777936], "split_indices": [27, 29, 0, 26, 0, 27, 29, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [138.0, 133.0, 5.0, 130.0, 3.0, 117.0, 13.0, 78.0, 39.0, 5.0, 8.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [75.357765, -55.619064, 2292.5566, -108.47967, 296.93268, -4.7777433, 40.5033, -148.28857, 26.191713, 444.13626, 100.36951, -2.25045, -1.2522669, -0.31618422, 3.8098214, 1.8661114, 5.847011, 2.030543, -0.22369209], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 222, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, 15, 17, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [41655170.0, 2566701.8, 40892412.0, 641869.4, 476194.38, 0.0, 0.0, 134636.88, 586374.5, 252800.75, 117990.85, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9, 10, 10], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, 16, 18, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, 0.8531112, 0.49706092, -0.18306975, 2.2768104, -4.7777433, 40.5033, -0.6035461, 11.0, -0.88131464, 1.0, -2.25045, -1.2522669, -0.31618422, 3.8098214, 1.8661114, 5.847011, 2.030543, -0.22369209], "split_indices": [29, 27, 21, 29, 21, 0, 0, 27, 11, 22, 16, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [142.0, 135.0, 7.0, 118.0, 17.0, 3.0, 4.0, 91.0, 27.0, 9.0, 8.0, 19.0, 72.0, 24.0, 3.0, 4.0, 5.0, 4.0, 4.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "19", "size_leaf_vector": "1"}}, {"base_weights": [82.69617, -63.3312, 2573.0903, -104.145134, 302.6802, -2.1828468, 54.190613, -153.158, -8.254423, 492.65585, 42.31084, -1.9564734, -1.156501, -0.64992464, 2.4808607, 2.2134097, 6.111566, 1.372405, -0.6317153], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 223, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, 15, 17, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [52531064.0, 2078058.9, 64736796.0, 581504.75, 671595.6, 0.0, 0.0, 108024.5, 641508.5, 121850.875, 78770.94, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9, 10, 10], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, 16, 18, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, 1.1800077, 4.0, -0.2967835, 2.2768104, -2.1828468, 54.190613, -0.6172026, 0.4568698, 0.3882633, 0.68735766, -1.9564734, -1.156501, -0.64992464, 2.4808607, 2.2134097, 6.111566, 1.372405, -0.6317153], "split_indices": [29, 27, 13, 27, 21, 0, 0, 26, 22, 20, 20, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [143.0, 136.0, 7.0, 123.0, 13.0, 4.0, 3.0, 81.0, 42.0, 7.0, 6.0, 36.0, 45.0, 35.0, 7.0, 3.0, 4.0, 3.0, 3.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "19", "size_leaf_vector": "1"}}, {"base_weights": [-76.15452, -97.3016, 6.5657296, -159.5128, 39.508717, -2.2359061, -137.71663, 122.61843, -4.9342093, -1.1935475, -2.1186247, -0.26347664, 3.527963], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 224, "left_children": [1, 3, -1, 5, 7, -1, 9, 11, -1, -1, -1, -1, -1], "loss_changes": [2202146.0, 1155317.1, 0.0, 99060.25, 1965005.6, 0.0, 72705.0, 1312298.5, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 6, 6, 7, 7], "right_children": [2, 4, -1, 6, 8, -1, 10, 12, -1, -1, -1, -1, -1], "split_conditions": [3.5387294, -0.19738343, 6.5657296, -0.6003495, 1.4532615, -2.2359061, -0.19460368, 0.09129919, -4.9342093, -1.1935475, -2.1186247, -0.26347664, 3.527963], "split_indices": [27, 18, 0, 27, 18, 0, 29, 29, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [137.0, 134.0, 3.0, 92.0, 42.0, 21.0, 71.0, 37.0, 5.0, 59.0, 12.0, 23.0, 14.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [58.00903, -41.387093, 26.10847, -138.21144, 526.4439, -105.76547, -9.796746, 93.32886, 21.272682, -1.5096321, 1.138904, 1.8557556, -1.46709], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 225, "left_children": [1, 3, -1, 5, 7, 9, -1, 11, -1, -1, -1, -1, -1], "loss_changes": [33856028.0, 7165125.0, 0.0, 2926807.5, 12974724.0, 1089415.2, 0.0, 381512.72, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 7, 7], "right_children": [2, 4, -1, 6, 8, 10, -1, 12, -1, -1, -1, -1, -1], "split_conditions": [3.5387294, 0.86266834, 26.10847, 0.53812104, 7.0, 0.4472843, -9.796746, 1.4532615, 21.272682, -1.5096321, 1.138904, 1.8557556, -1.46709], "split_indices": [27, 21, 0, 25, 13, 26, 0, 18, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [132.0, 128.0, 4.0, 110.0, 18.0, 107.0, 3.0, 15.0, 3.0, 89.0, 18.0, 11.0, 4.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [68.26222, -71.396645, 32.45779, -128.29271, 188.62543, -148.17772, 22.899775, 363.31647, 25.40985, -1.3692964, -2.7804081, -1.0162964, 2.0589395, 1.1258062, 4.34375, -2.3055625, 1.1618752], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 226, "left_children": [1, 3, -1, 5, 7, 9, 11, 13, 15, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [63247172.0, 2051003.0, 0.0, 343136.25, 703536.8, 107957.5, 339969.56, 164846.25, 352080.44, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8], "right_children": [2, 4, -1, 6, 8, 10, 12, 14, 16, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, -0.0155352615, 32.45779, 0.0526399, 2.0, -0.21593031, 6.0, -0.99255466, -0.820852, -1.3692964, -2.7804081, -1.0162964, 2.0589395, 1.1258062, 4.34375, -2.3055625, 1.1618752], "split_indices": [29, 29, 0, 27, 8, 20, 13, 22, 28, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [141.0, 136.0, 5.0, 112.0, 24.0, 99.0, 13.0, 11.0, 13.0, 93.0, 6.0, 8.0, 5.0, 3.0, 8.0, 3.0, 10.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "17", "size_leaf_vector": "1"}}, {"base_weights": [62.93179, -47.649456, 29.697102, -126.48988, 751.2873, -94.86479, -10.277824, 71.379585, 20.93233, -1.2252973, 4.4195247, -0.85118705, 1.6383184], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 227, "left_children": [1, 3, -1, 5, 7, 9, -1, 11, -1, -1, -1, -1, -1], "loss_changes": [43856984.0, 8409452.0, 0.0, 3351305.5, 10799158.0, 1806540.6, 0.0, 144170.61, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 7, 7], "right_children": [2, 4, -1, 6, 8, 10, -1, 12, -1, -1, -1, -1, -1], "split_conditions": [3.5387294, 1.7035879, 29.697102, 1.0485128, 1.2432504, 1.1800077, -10.277824, 1.3137271, 20.93233, -1.2252973, 4.4195247, -0.85118705, 1.6383184], "split_indices": [27, 21, 0, 25, 29, 27, 0, 26, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [135.0, 131.0, 4.0, 120.0, 11.0, 117.0, 3.0, 8.0, 3.0, 112.0, 5.0, 3.0, 5.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [25.978281, -57.49718, 30.454943, -34.777184, -8.496577, -85.76057, 437.4752, -1.3441775, 0.59857863, 2.1478245, 9.404863], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 228, "left_children": [1, 3, -1, 5, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [37479620.0, 2580056.5, 0.0, 3456420.0, 0.0, 922067.94, 1366121.2, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6], "right_children": [2, 4, -1, 6, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [2.1903315, 1.6165377, 30.454943, 1.1800077, -8.496577, -0.18306975, -0.34237808, -1.3441775, 0.59857863, 2.1478245, 9.404863], "split_indices": [29, 18, 0, 27, 0, 29, 23, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [147.0, 144.0, 3.0, 141.0, 3.0, 128.0, 13.0, 96.0, 32.0, 10.0, 3.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [29.18139, -57.170895, 30.22653, -100.92342, 11.355615, -82.30543, -6.900614, -1.2015625, 2.209348], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 229, "left_children": [1, 3, -1, 5, -1, 7, -1, -1, -1], "loss_changes": [36879130.0, 7368224.5, 0.0, 1423883.4, 0.0, 1541618.8, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5], "right_children": [2, 4, -1, 6, -1, 8, -1, -1, -1], "split_conditions": [2.830827, 2.8455322, 30.22653, 1.562716, 11.355615, 0.1516057, -6.900614, -1.2015625, 2.209348], "split_indices": [29, 25, 0, 20, 0, 29, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [141.0, 138.0, 3.0, 134.0, 4.0, 131.0, 3.0, 117.0, 14.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [67.05219, -60.559746, 2228.0862, -105.67614, 241.06865, 39.87308, -5.27938, 134.75568, -121.9072, -2.749469, 362.60822, -1.0108923, 3.958979, -0.765584, -1.9024947, 5.2086115, 1.0732471], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 230, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, -1, 15, -1, -1, -1, -1, -1, -1], "loss_changes": [39570790.0, 1876202.4, 40893064.0, 480819.5, 1228600.0, 0.0, 0.0, 532763.0, 332523.0, 0.0, 550026.6, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 10, 10], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, -1, 16, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, 0.022172607, -0.9297316, -0.6772184, -0.9617159, 39.87308, -5.27938, -0.30596283, 4.0, -2.749469, -0.4547159, -1.0108923, 3.958979, -0.765584, -1.9024947, 5.2086115, 1.0732471], "split_indices": [29, 29, 22, 17, 22, 0, 0, 18, 8, 0, 28, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [142.0, 135.0, 7.0, 118.0, 17.0, 4.0, 3.0, 7.0, 111.0, 3.0, 14.0, 4.0, 3.0, 68.0, 43.0, 8.0, 6.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "17", "size_leaf_vector": "1"}}, {"base_weights": [67.69092, -65.753944, 2160.9988, -108.120445, 345.7295, -5.239785, 38.768005, -136.25684, 10.345001, 4.9946485, 0.30586982, -1.0872798, -1.9043984, 0.9892312, -1.1468887], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 231, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, -1, -1, -1, -1, -1, -1], "loss_changes": [37294680.0, 2233928.8, 38886816.0, 384739.25, 566052.6, 0.0, 0.0, 117982.375, 266074.94, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, 0.33892554, 0.49706092, -0.11083611, 2.5527406, -5.239785, 38.768005, 5.0, 5.0, 4.9946485, 0.30586982, -1.0872798, -1.9043984, 0.9892312, -1.1468887], "split_indices": [29, 20, 21, 29, 21, 0, 0, 8, 14, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [132.0, 125.0, 7.0, 114.0, 11.0, 3.0, 4.0, 92.0, 22.0, 7.0, 4.0, 63.0, 29.0, 13.0, 9.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [51.22012, -62.930077, 2180.1567, -118.34092, 176.74155, -5.2859483, 43.43894, -156.6562, -9.88066, 255.1375, -2.9859657, -2.0247724, -1.3421938, -0.8472462, 0.88744575, 1.5779805, 4.411651], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 232, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, 15, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [33430948.0, 1760643.0, 43323730.0, 443093.88, 1007793.5, 0.0, 0.0, 54008.125, 221582.05, 328697.25, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, 16, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, -0.0155352615, 4.0, -0.19542712, 1.1764319, -5.2859483, 43.43894, -0.9018909, 6.0, 4.0, -2.9859657, -2.0247724, -1.3421938, -0.8472462, 0.88744575, 1.5779805, 4.411651], "split_indices": [29, 29, 13, 25, 18, 0, 0, 26, 13, 13, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [136.0, 130.0, 6.0, 106.0, 24.0, 3.0, 3.0, 78.0, 28.0, 21.0, 3.0, 23.0, 55.0, 16.0, 12.0, 15.0, 6.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "17", "size_leaf_vector": "1"}}, {"base_weights": [85.85811, 2.9971166, 29.43788, -38.61926, 14.172296, -97.55911, 409.52084, -0.78891957, -6.238255, 2.2091732, 9.20077], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 233, "left_children": [1, 3, -1, 5, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [33618030.0, 8235748.0, 0.0, 3632131.8, 0.0, 1139406.5, 1337308.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6], "right_children": [2, 4, -1, 6, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [2.830827, 2.8455322, 29.43788, 1.1800077, 14.172296, 0.45794836, -0.34237808, -0.78891957, -6.238255, 2.2091732, 9.20077], "split_indices": [29, 25, 0, 27, 0, 29, 23, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [141.0, 138.0, 3.0, 135.0, 3.0, 120.0, 15.0, 117.0, 3.0, 12.0, 3.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [13.978955, -82.75739, 26.811262, -103.939804, 328.49066, -87.17603, -5.1228447, 1.0536586, 4.694678, -1.4175068, 0.56227267], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 234, "left_children": [1, 3, -1, 5, 7, 9, -1, -1, -1, -1, -1], "loss_changes": [36859576.0, 1236270.1, 0.0, 858873.75, 170665.06, 1009730.56, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5], "right_children": [2, 4, -1, 6, 8, 10, -1, -1, -1, -1, -1], "split_conditions": [3.2148452, 1.8196839, 26.811262, 0.8306982, -0.4521468, -0.18306975, -5.1228447, 1.0536586, 4.694678, -1.4175068, 0.56227267], "split_indices": [27, 27, 0, 29, 17, 29, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [141.0, 137.0, 4.0, 131.0, 6.0, 127.0, 4.0, 3.0, 3.0, 92.0, 35.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [73.07446, -9.076733, 29.095848, -62.944675, 775.99854, -125.11053, 98.36886, 22.403072, -8.4349, -1.0962278, -3.3655832, -1.2133976, 1.6316571], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 235, "left_children": [1, 3, -1, 5, 7, 9, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [33115926.0, 5927137.0, 0.0, 1326003.0, 23232700.0, 274171.62, 546550.4, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, -1, 6, 8, 10, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [2.830827, 0.8306982, 29.095848, 0.21210697, 1.1198277, -0.019387605, -0.36327416, 22.403072, -8.4349, -1.0962278, -3.3655832, -1.2133976, 1.6316571], "split_indices": [29, 29, 0, 26, 18, 20, 27, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [141.0, 138.0, 3.0, 130.0, 8.0, 94.0, 36.0, 4.0, 4.0, 89.0, 5.0, 8.0, 28.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [58.782322, -20.760374, 28.877632, -89.370056, 735.66785, -60.166668, -10.088119, 22.235054, -7.322995, -0.9212144, 7.507161, 1.0335189, -0.79902416], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 236, "left_children": [1, 3, -1, 5, 7, 9, -1, -1, 11, -1, -1, -1, -1], "loss_changes": [32913852.0, 7487139.0, 0.0, 3483504.5, 13281898.0, 3411685.0, 0.0, 0.0, 80550.195, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 8, 8], "right_children": [2, 4, -1, 6, 8, 10, -1, -1, 12, -1, -1, -1, -1], "split_conditions": [2.830827, 1.7035879, 28.877632, 1.1764319, 2.2768104, 1.455611, -10.088119, 22.235054, 1.0, -0.9212144, 7.507161, 1.0335189, -0.79902416], "split_indices": [29, 21, 0, 18, 21, 27, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [145.0, 142.0, 3.0, 131.0, 11.0, 128.0, 3.0, 3.0, 8.0, 124.0, 4.0, 3.0, 5.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [81.34367, -53.961132, 2354.4568, -93.914444, 363.37817, -3.648039, 51.649433, -132.16943, 30.353516, 4.8478394, 0.96433216, -1.8310364, -0.9940442, 1.2653359, -0.6170615], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 237, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, -1, -1, -1, -1, -1, -1], "loss_changes": [43801244.0, 2285101.5, 63024240.0, 593501.75, 342096.5, 0.0, 0.0, 140312.25, 273443.6, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, 0.33892554, 4.0, -0.18520685, 8.0, -3.648039, 51.649433, -0.3022182, 3.0, 4.8478394, 0.96433216, -1.8310364, -0.9940442, 1.2653359, -0.6170615], "split_indices": [29, 20, 13, 18, 11, 0, 0, 18, 8, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [141.0, 134.0, 7.0, 123.0, 11.0, 4.0, 3.0, 94.0, 29.0, 7.0, 4.0, 35.0, 59.0, 14.0, 15.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [67.752235, -49.514416, 2111.4622, -123.298904, 108.99059, 42.38072, -0.12097969, -108.46434, -4.3735285, 5.069153, 57.802437, -1.4722191, 0.028411526, -1.3628347, 1.6349081], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 238, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, -1, -1, 13, -1, -1, -1, -1], "loss_changes": [35334628.0, 1650769.4, 36179550.0, 399758.12, 887249.06, 0.0, 0.0, 401628.25, 0.0, 0.0, 863301.5, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 10, 10], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, -1, -1, 14, -1, -1, -1, -1], "split_conditions": [1.2440649, 0.19972922, -0.011381618, -0.019387605, -0.44094184, 42.38072, -0.12097969, -0.259465, -4.3735285, 5.069153, 0.0526399, -1.4722191, 0.028411526, -1.3628347, 1.6349081], "split_indices": [29, 26, 17, 20, 25, 0, 0, 29, 0, 0, 27, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [146.0, 139.0, 7.0, 95.0, 44.0, 3.0, 4.0, 92.0, 3.0, 4.0, 40.0, 68.0, 24.0, 14.0, 26.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [5.23757, -65.19639, 18.777534, -89.6972, 4.683368, -137.12022, 40.22743, -2.0524049, -1.1345375, -0.6605456, 1.0603744], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 239, "left_children": [1, 3, -1, 5, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [18191358.0, 1780545.5, 0.0, 794187.4, 0.0, 128226.5, 251812.92, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6], "right_children": [2, 4, -1, 6, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [1.2440649, 1.8196839, 18.777534, -0.2197353, 4.683368, -0.6035461, -0.8956327, -2.0524049, -1.1345375, -0.6605456, 1.0603744], "split_indices": [29, 27, 0, 27, 0, 27, 22, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [136.0, 132.0, 4.0, 127.0, 5.0, 93.0, 34.0, 22.0, 71.0, 13.0, 21.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [9.587452, -86.14924, 26.691698, -56.599865, -7.3171043, -85.456375, 273.82806, -1.3377784, 0.08676571, 4.648233, 0.9827031], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 240, "left_children": [1, 3, -1, 5, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [36640816.0, 2610061.0, 0.0, 1301071.1, 0.0, 565204.94, 323103.06, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6], "right_children": [2, 4, -1, 6, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [3.5387294, 2.0373242, 26.691698, 1.1800077, -7.3171043, -0.33963278, 0.94270897, -1.3377784, 0.08676571, 4.648233, 0.9827031], "split_indices": [27, 25, 0, 27, 0, 27, 21, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [142.0, 138.0, 4.0, 133.0, 5.0, 123.0, 10.0, 81.0, 42.0, 4.0, 6.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [74.22701, -61.126812, 2298.2993, -128.29634, 152.78409, 50.794468, -3.862586, -105.82649, -302.51086, 469.3574, 45.36526, -1.2997543, 0.63977, -3.568854, -1.1810902, 1.8866187, 7.028624, -2.4839017, 1.3122864], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 241, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, 15, 17, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [41984816.0, 1916213.9, 61691664.0, 363317.5, 1066845.8, 0.0, 0.0, 381474.3, 68095.5, 391658.12, 663154.94, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9, 10, 10], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, 16, 18, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, 0.4472843, 4.1228113, -0.21593031, -0.13213359, 50.794468, -3.862586, -0.2337211, -0.37846264, 1.0, -0.23221819, -1.2997543, 0.63977, -3.568854, -1.1810902, 1.8866187, 7.028624, -2.4839017, 1.3122864], "split_indices": [29, 26, 25, 20, 24, 0, 0, 27, 17, 1, 25, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [138.0, 131.0, 7.0, 100.0, 31.0, 3.0, 4.0, 90.0, 10.0, 7.0, 24.0, 79.0, 11.0, 7.0, 3.0, 4.0, 3.0, 5.0, 19.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "19", "size_leaf_vector": "1"}}, {"base_weights": [13.399931, -66.81344, 27.974894, -104.374405, 161.88803, 2.274358, -114.8271, -0.45725486, 201.22133, 0.73447967, -1.2388892, 2.8919168, 0.34222004], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 242, "left_children": [1, 3, -1, 5, 7, -1, 9, -1, 11, -1, -1, -1, -1], "loss_changes": [31898790.0, 1210935.6, 0.0, 442302.25, 172539.06, 0.0, 208719.62, 0.0, 239817.75, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 6, 6, 8, 8], "right_children": [2, 4, -1, 6, 8, -1, 10, -1, 12, -1, -1, -1, -1], "split_conditions": [2.830827, 0.2040027, 27.974894, -0.95644623, -0.5258309, 2.274358, -0.8636659, -0.45725486, -0.4547159, 0.73447967, -1.2388892, 2.8919168, 0.34222004], "split_indices": [29, 25, 0, 23, 17, 0, 28, 0, 28, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [141.0, 138.0, 3.0, 119.0, 19.0, 3.0, 116.0, 3.0, 16.0, 5.0, 111.0, 10.0, 6.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [-29.838884, -106.92291, 559.7855, -73.87439, -10.633606, 21.662851, 22.409798, -95.62034, 5.752015, -1.2593106, 130.87672, 2.7828636, -1.0801066, 0.004102417, 2.0905457], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 243, "left_children": [1, 3, 5, 7, -1, -1, 9, 11, -1, -1, 13, -1, -1, -1, -1], "loss_changes": [6286544.0, 3777610.5, 13763937.0, 1734610.5, 0.0, 0.0, 225652.94, 567448.6, 0.0, 0.0, 81489.984, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6, 7, 7, 10, 10], "right_children": [2, 4, 6, 8, -1, -1, 10, 12, -1, -1, 14, -1, -1, -1, -1], "split_conditions": [1.448768, 0.698111, 2.0008802, 1.1800077, -10.633606, 21.662851, 1.3137271, -0.9199369, 5.752015, -1.2593106, 1.0, 2.7828636, -1.0801066, 0.004102417, 2.0905457], "split_indices": [21, 25, 21, 27, 0, 0, 26, 23, 0, 0, 2, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [136.0, 121.0, 15.0, 118.0, 3.0, 3.0, 12.0, 115.0, 3.0, 5.0, 7.0, 3.0, 112.0, 3.0, 4.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [82.32559, -47.876545, 2269.2085, -121.66824, 102.40836, -3.905006, 50.265675, -104.30112, -3.9950252, 62.717106, 4.935395, -1.4497043, -0.033050247, -0.7468012, 2.036753], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 244, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, -1, 13, -1, -1, -1, -1, -1], "loss_changes": [40541492.0, 1509582.0, 60633532.0, 397372.62, 667592.8, 0.0, 0.0, 356853.44, 0.0, 828648.56, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 9, 9], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, -1, 14, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, 0.19972922, 4.0, -0.019387605, 11.0, -3.905006, 50.265675, -0.25530338, -3.9950252, -0.0020397583, 4.935395, -1.4497043, -0.033050247, -0.7468012, 2.036753], "split_indices": [29, 26, 13, 20, 11, 0, 0, 29, 0, 29, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [141.0, 134.0, 7.0, 90.0, 44.0, 4.0, 3.0, 86.0, 4.0, 41.0, 3.0, 61.0, 25.0, 21.0, 20.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-20.438272, -57.894043, 8.444342, -36.992813, -7.4881206, -74.655014, 498.56186, -1.1555914, 0.40625662, 1.5831032, 7.3908105], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 245, "left_children": [1, 3, -1, 5, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [4681217.5, 1965084.1, 0.0, 2760150.0, 0.0, 603565.06, 619439.25, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6], "right_children": [2, 4, -1, 6, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [2.8455322, 0.98363477, 8.444342, 0.23101035, -7.4881206, -0.20714763, 6.0, -1.1555914, 0.40625662, 1.5831032, 7.3908105], "split_indices": [25, 18, 0, 29, 0, 29, 13, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [142.0, 137.0, 5.0, 134.0, 3.0, 126.0, 8.0, 93.0, 33.0, 4.0, 4.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [66.69449, -26.992496, 32.822468, -65.785324, 8.373972, -93.73411, 252.70433, -0.7123133, -7.45359, 0.8930763, 4.308988], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 246, "left_children": [1, 3, -1, 5, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [42565936.0, 4682444.0, 0.0, 1207557.2, 0.0, 1750420.2, 281745.88, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6], "right_children": [2, 4, -1, 6, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [3.5387294, 2.8455322, 32.822468, 1.1800077, 8.373972, 0.87322295, 0.18499485, -0.7123133, -7.45359, 0.8930763, 4.308988], "split_indices": [27, 25, 0, 27, 0, 27, 29, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [140.0, 137.0, 3.0, 132.0, 5.0, 122.0, 10.0, 119.0, 3.0, 6.0, 4.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [53.819553, -55.99561, 24.058924, -110.263855, 153.0979, 2.2357814, -122.51589, 263.38702, -141.74968, -1.3554838, 0.083213165, 1.0388992, 4.1248517, 0.7625788, -3.788467], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 247, "left_children": [1, 3, -1, 5, 7, -1, 9, 11, 13, -1, -1, -1, -1, -1, -1], "loss_changes": [34746252.0, 1480657.5, 0.0, 448677.12, 915346.3, 0.0, 171654.25, 432709.62, 442431.9, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 6, 6, 7, 7, 8, 8], "right_children": [2, 4, -1, 6, 8, -1, 10, 12, 14, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, -0.0155352615, 24.058924, -0.95644623, 5.0, 2.2357814, 0.0526399, 0.18499485, 0.22625422, -1.3554838, 0.083213165, 1.0388992, 4.1248517, 0.7625788, -3.788467], "split_indices": [29, 29, 0, 23, 8, 0, 27, 29, 29, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [133.0, 128.0, 5.0, 102.0, 26.0, 3.0, 99.0, 19.0, 7.0, 90.0, 9.0, 10.0, 9.0, 4.0, 3.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [48.659286, -62.560635, 1961.133, -88.152534, 190.2886, -4.9261894, 45.38065, -75.762695, -4.47077, 0.41023475, 278.43887, -1.1499063, 0.5535166, 1.2767702, 3.4334059], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 248, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, -1, -1, 13, -1, -1, -1, -1], "loss_changes": [30965132.0, 909747.4, 52821150.0, 526400.2, 159596.44, 0.0, 0.0, 639002.44, 0.0, 0.0, 34393.812, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 10, 10], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, -1, -1, 14, -1, -1, -1, -1], "split_conditions": [1.2440649, 1.4375046, 0.49706092, 0.36177117, 0.16828896, -4.9261894, 45.38065, -0.20714763, -4.47077, 0.41023475, 0.68735766, -1.1499063, 0.5535166, 1.2767702, 3.4334059], "split_indices": [29, 26, 21, 29, 29, 0, 0, 29, 0, 0, 20, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [144.0, 137.0, 7.0, 125.0, 12.0, 4.0, 3.0, 122.0, 3.0, 5.0, 7.0, 94.0, 28.0, 3.0, 4.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-13.608841, -62.905586, 601.58777, -116.3518, 60.752567, 20.77274, -538.4006, -100.16017, -3.909874, -99.564125, 136.05536, -11.024409, 1.60215, -1.5935419, -0.42577037, 0.15585123, -1.3239439, 0.59073496, 6.023986], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 249, "left_children": [1, 3, 5, 7, 9, -1, 11, 13, -1, 15, 17, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [4495886.0, 912515.44, 19623472.0, 387682.12, 520586.28, 0.0, 2935053.5, 306783.94, 0.0, 55000.375, 1005447.3, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 6, 6, 7, 7, 9, 9, 10, 10], "right_children": [2, 4, 6, 8, 10, -1, 12, 14, -1, 16, 18, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [0.8306982, 0.19972922, 1.1198277, -0.019387605, -0.20332481, 20.77274, -0.46722066, -0.48221627, -3.909874, -0.9687642, 7.0, -11.024409, 1.60215, -1.5935419, -0.42577037, 0.15585123, -1.3239439, 0.59073496, 6.023986], "split_indices": [29, 26, 18, 20, 20, 0, 23, 27, 0, 24, 13, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [146.0, 136.0, 10.0, 95.0, 41.0, 4.0, 6.0, 91.0, 4.0, 13.0, 28.0, 3.0, 3.0, 44.0, 47.0, 3.0, 10.0, 25.0, 3.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "19", "size_leaf_vector": "1"}}, {"base_weights": [63.87291, -32.69586, 26.391613, -70.634834, 9.602366, -52.48563, -6.291884, -1.2135208, 0.6338314], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 250, "left_children": [1, 3, -1, 5, -1, 7, -1, -1, -1], "loss_changes": [34406108.0, 5115630.0, 0.0, 1284758.0, 0.0, 1021090.25, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5], "right_children": [2, 4, -1, 6, -1, 8, -1, -1, -1], "split_conditions": [3.5387294, 2.8455322, 26.391613, 0.8360413, 9.602366, -0.2967835, -6.291884, -1.2135208, 0.6338314], "split_indices": [27, 25, 0, 18, 0, 27, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [137.0, 133.0, 4.0, 129.0, 4.0, 126.0, 3.0, 79.0, 47.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [69.712234, -44.678604, 2314.9014, -109.82793, 106.84671, -4.4184647, 44.929485, -1.8444114, -84.14353, 207.71268, -88.552864, -0.957433, 0.95061916, 1.2087946, 5.0593786, -2.7389822, 0.31499636], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 251, "left_children": [1, 3, 5, 7, 9, -1, -1, -1, 11, 13, 15, -1, -1, -1, -1, -1, -1], "loss_changes": [37087040.0, 1374039.6, 44015892.0, 170344.0, 846190.5, 0.0, 0.0, 0.0, 162928.56, 663863.4, 342419.6, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 8, 8, 9, 9, 10, 10], "right_children": [2, 4, 6, 8, 10, -1, -1, -1, 12, 14, 16, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, -0.18306975, 4.0, -0.6003495, 4.0, -4.4184647, 44.929485, -1.8444114, 2.0, 1.0, 2.0, -0.957433, 0.95061916, 1.2087946, 5.0593786, -2.7389822, 0.31499636], "split_indices": [29, 29, 11, 27, 8, 0, 0, 0, 4, 4, 11, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [143.0, 137.0, 6.0, 96.0, 41.0, 3.0, 3.0, 23.0, 73.0, 27.0, 14.0, 69.0, 4.0, 22.0, 5.0, 5.0, 9.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "17", "size_leaf_vector": "1"}}, {"base_weights": [51.632187, -28.439146, 27.211388, -66.154915, 12.1383705, -44.72765, -7.352636, -0.9153348, 4.1534815], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 252, "left_children": [1, 3, -1, 5, -1, 7, -1, -1, -1], "loss_changes": [29361538.0, 6358542.5, 0.0, 1845204.5, 0.0, 2794367.8, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5], "right_children": [2, 4, -1, 6, -1, 8, -1, -1, -1], "split_conditions": [4.2314267, 2.8455322, 27.211388, 1.1764319, 12.1383705, 0.25780264, -7.352636, -0.9153348, 4.1534815], "split_indices": [18, 25, 0, 18, 0, 20, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [136.0, 133.0, 3.0, 130.0, 3.0, 127.0, 3.0, 116.0, 11.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [47.89228, -27.057497, 26.591866, -62.63506, 9.460438, -42.09979, -7.297491, -0.9181682, 1.9966501], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 253, "left_children": [1, 3, -1, 5, -1, 7, -1, -1, -1], "loss_changes": [28059592.0, 4906047.5, 0.0, 1832315.2, 0.0, 1616959.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5], "right_children": [2, 4, -1, 6, -1, 8, -1, -1, -1], "split_conditions": [2.830827, 2.8455322, 26.591866, 1.4594278, 9.460438, -0.0020397583, -7.297491, -0.9181682, 1.9966501], "split_indices": [29, 25, 0, 19, 0, 29, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [142.0, 139.0, 3.0, 135.0, 4.0, 132.0, 3.0, 110.0, 22.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [72.23382, -35.180035, 2133.482, -100.56794, 169.97252, -6.971003, 44.307186, -59.1697, -179.30539, 82.74132, 3.6232016, -0.91127545, 2.9155898, -1.3780591, -2.6926348, 3.6748326, 0.074137665], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 254, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, 15, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [31293594.0, 1828043.8, 48606650.0, 325104.7, 523674.2, 0.0, 0.0, 799937.94, 93515.75, 512011.97, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, 16, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, 0.4472843, 0.49706092, 5.0, 0.18499485, -6.971003, 44.307186, 0.02337902, 2.0, 0.57106185, 3.6232016, -0.91127545, 2.9155898, -1.3780591, -2.6926348, 3.6748326, 0.074137665], "split_indices": [29, 26, 21, 8, 29, 0, 0, 27, 7, 26, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [140.0, 134.0, 6.0, 102.0, 32.0, 3.0, 3.0, 68.0, 34.0, 23.0, 9.0, 63.0, 5.0, 25.0, 9.0, 4.0, 19.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "17", "size_leaf_vector": "1"}}, {"base_weights": [-18.165968, -99.04039, 353.26123, -64.01403, -10.112104, 36.270664, 19.291212, -92.258965, 6.863329, 175.90744, -268.11823, -1.1591836, 0.681099, 1.0912085, 3.3226535, -3.7074575, -0.98436147], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 255, "left_children": [1, 3, 5, 7, -1, 9, -1, 11, -1, 13, 15, -1, -1, -1, -1, -1, -1], "loss_changes": [4030143.2, 3449660.5, 11918619.0, 2330965.8, 0.0, 939736.3, 0.0, 402499.88, 0.0, 120337.97, 85356.59, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 5, 5, 7, 7, 9, 9, 10, 10], "right_children": [2, 4, 6, 8, -1, 10, -1, 12, -1, 14, 16, -1, -1, -1, -1, -1, -1], "split_conditions": [0.86266834, 1.0485128, 7.0, 0.8306982, -10.112104, 0.8763525, 19.291212, -0.11083611, 6.863329, 0.59401447, -0.69065803, -1.1591836, 0.681099, 1.0912085, 3.3226535, -3.7074575, -0.98436147], "split_indices": [21, 25, 13, 29, 0, 20, 0, 29, 0, 24, 23, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [132.0, 109.0, 23.0, 106.0, 3.0, 20.0, 3.0, 103.0, 3.0, 14.0, 6.0, 90.0, 13.0, 11.0, 3.0, 3.0, 3.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "17", "size_leaf_vector": "1"}}, {"base_weights": [60.515793, -20.797369, 26.271048, -59.09412, 11.715761, -91.02355, 487.57263, -0.68429744, -7.1781454, 1.1250552, 7.4072156], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 256, "left_children": [1, 3, -1, 5, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [27185570.0, 5868453.5, 0.0, 2208731.0, 0.0, 1621869.9, 581214.4, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6], "right_children": [2, 4, -1, 6, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [4.2314267, 2.8455322, 26.271048, 2.1555429, 11.715761, 1.4532615, -0.37959817, -0.68429744, -7.1781454, 1.1250552, 7.4072156], "split_indices": [18, 25, 0, 27, 0, 18, 23, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [129.0, 126.0, 3.0, 123.0, 3.0, 117.0, 6.0, 114.0, 3.0, 3.0, 3.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [62.82671, -50.7026, 31.382786, -79.8159, 208.69308, 2.6670625, -90.70235, 275.81265, -0.2803396, -1.1129154, 0.009647631, 1.4012132, 4.7821712], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 257, "left_children": [1, 3, -1, 5, 7, -1, 9, 11, -1, -1, -1, -1, -1], "loss_changes": [49040976.0, 1043698.0, 0.0, 488177.7, 230203.06, 0.0, 226600.62, 235039.62, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 6, 6, 7, 7], "right_children": [2, 4, -1, 6, 8, -1, 10, 12, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, 1.1800077, 31.382786, -0.9199369, 0.8763525, 2.6670625, -0.11083611, -0.31147707, -0.2803396, -1.1129154, 0.009647631, 1.4012132, 4.7821712], "split_indices": [29, 27, 0, 23, 20, 0, 29, 17, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [139.0, 135.0, 4.0, 122.0, 13.0, 3.0, 119.0, 10.0, 3.0, 97.0, 22.0, 7.0, 3.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [45.560646, -29.371933, 26.193695, -63.781006, 11.392522, -86.214386, 5.367098, -0.6572772, -7.20209], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 258, "left_children": [1, 3, -1, 5, -1, 7, -1, -1, -1], "loss_changes": [27270756.0, 5621711.0, 0.0, 1864817.2, 0.0, 1654068.8, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5], "right_children": [2, 4, -1, 6, -1, 8, -1, -1, -1], "split_conditions": [4.2314267, 2.8455322, 26.193695, 2.2059762, 11.392522, 1.4532615, 5.367098, -0.6572772, -7.20209], "split_indices": [18, 25, 0, 27, 0, 18, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [140.0, 137.0, 3.0, 134.0, 3.0, 130.0, 4.0, 127.0, 3.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [36.89236, -35.000484, 25.619171, -3.1380844, -11.254781, -70.315674, 22.648651, -0.9689552, 2.1019409], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 259, "left_children": [1, 3, -1, 5, -1, 7, -1, -1, -1], "loss_changes": [26230418.0, 4895433.5, 0.0, 21184578.0, 0.0, 1026903.06, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5], "right_children": [2, 4, -1, 6, -1, 8, -1, -1, -1], "split_conditions": [2.830827, 1.7508882, 25.619171, 1.2432504, -11.254781, 1.4375046, 22.648651, -0.9689552, 2.1019409], "split_indices": [29, 29, 0, 29, 0, 26, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [143.0, 140.0, 3.0, 137.0, 3.0, 134.0, 3.0, 123.0, 11.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [41.732777, 26.677883, -34.136684, -83.41567, 609.89386, -52.527416, -10.432388, 17.249456, -114.39204, -0.7285455, 4.580056, -0.016300293, -1.9853103], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 260, "left_children": [1, -1, 3, 5, 7, 9, -1, -1, 11, -1, -1, -1, -1], "loss_changes": [28383046.0, 0.0, 4462289.5, 3799236.2, 8273645.0, 1351294.0, 0.0, 0.0, 66070.15, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4, 5, 5, 8, 8], "right_children": [2, -1, 4, 6, 8, 10, -1, -1, 12, -1, -1, -1, -1], "split_conditions": [-0.8811961, 26.677883, 1.7035879, 1.2473981, 2.0008802, 0.92753893, -10.432388, 17.249456, -1.0239222, -0.7285455, 4.580056, -0.016300293, -1.9853103], "split_indices": [28, 0, 21, 25, 21, 27, 0, 0, 22, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [141.0, 3.0, 138.0, 129.0, 9.0, 126.0, 3.0, 3.0, 6.0, 122.0, 4.0, 3.0, 3.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [58.59916, -57.108047, 1626.3673, -81.63394, 345.498, 78.590034, 39.283604, -104.83992, 19.092525, 0.13539569, 6.740465, 2.8549118, -1.4793363, -0.9379346, -3.602898, 4.1846423, -0.5438381], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 261, "left_children": [1, 3, 5, 7, 9, 11, -1, 13, 15, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [26396336.0, 1371079.1, 35320600.0, 303542.2, 863320.56, 370323.5, 0.0, 262452.62, 756403.3, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 7, 7, 8, 8], "right_children": [2, 4, 6, 8, 10, 12, -1, 14, 16, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2473981, 1.9500875, 3.0, 0.4472843, 1.0, 1.4532615, 39.283604, 1.7035879, 0.49679533, 0.13539569, 6.740465, 2.8549118, -1.4793363, -0.9379346, -3.602898, 4.1846423, -0.5438381], "split_indices": [25, 27, 13, 26, 4, 18, 0, 21, 26, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [144.0, 135.0, 9.0, 128.0, 7.0, 6.0, 3.0, 104.0, 24.0, 4.0, 3.0, 3.0, 3.0, 101.0, 3.0, 3.0, 21.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "17", "size_leaf_vector": "1"}}, {"base_weights": [1.0990708, -88.96441, 29.729443, -59.28148, -7.0240912, -107.7039, 87.83522, -0.9466683, -2.5992289, -0.05602761, 2.6673524], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 262, "left_children": [1, 3, -1, 5, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [36398160.0, 2361850.5, 0.0, 914178.2, 0.0, 165869.38, 536434.5, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6], "right_children": [2, 4, -1, 6, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [3.5387294, 1.6165377, 29.729443, -0.20115834, -7.0240912, 0.8186169, 0.1796442, -0.9466683, -2.5992289, -0.05602761, 2.6673524], "split_indices": [27, 18, 0, 18, 0, 26, 29, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [134.0, 131.0, 3.0, 126.0, 5.0, 95.0, 31.0, 89.0, 6.0, 21.0, 10.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [44.75744, -26.219976, 25.532915, -102.37398, 472.15985, -72.55772, -9.786975, 38.871723, 20.872473, -1.0045676, 5.9477167, 2.0783978, -0.56817377], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 263, "left_children": [1, 3, -1, 5, 7, 9, -1, 11, -1, -1, -1, -1, -1], "loss_changes": [25884344.0, 5437714.0, 0.0, 3168841.5, 13214818.0, 2312460.2, 0.0, 270518.44, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 7, 7], "right_children": [2, 4, -1, 6, 8, 10, -1, 12, -1, -1, -1, -1, -1], "split_conditions": [4.2314267, 0.8281771, 25.532915, 0.98363477, -0.2927211, 0.23448703, -9.786975, -0.14068931, 20.872473, -1.0045676, 5.9477167, 2.0783978, -0.56817377], "split_indices": [18, 21, 0, 18, 17, 20, 0, 24, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [144.0, 141.0, 3.0, 123.0, 18.0, 120.0, 3.0, 15.0, 3.0, 116.0, 4.0, 5.0, 10.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [31.776236, -47.287838, 25.341415, -84.617256, 11.217212, -57.354847, -7.219717, -0.99136966, 1.5650762], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 264, "left_children": [1, 3, -1, 5, -1, 7, -1, -1, -1], "loss_changes": [25840218.0, 5636894.5, 0.0, 2113115.2, 0.0, 1087785.5, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5], "right_children": [2, 4, -1, 6, -1, 8, -1, -1, -1], "split_conditions": [2.1903315, 2.8455322, 25.341415, 0.45794836, 11.217212, -0.07156372, -7.219717, -0.99136966, 1.5650762], "split_indices": [29, 25, 0, 29, 0, 29, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [129.0, 126.0, 3.0, 123.0, 3.0, 119.0, 4.0, 100.0, 19.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [36.379547, -50.08029, 24.49999, -6.024793, -776.23047, -70.6147, 20.946283, -13.004704, -2.015723, -0.8932807, 1.3994154], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 265, "left_children": [1, 3, -1, 5, 7, 9, -1, -1, -1, -1, -1], "loss_changes": [30173024.0, 4473972.0, 0.0, 18193282.0, 2147781.5, 524723.8, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5], "right_children": [2, 4, -1, 6, 8, 10, -1, -1, -1, -1, -1], "split_conditions": [3.5387294, 1.4532615, 24.49999, 0.6820698, 2.3330073, 0.2040027, 20.946283, -13.004704, -2.015723, -0.8932807, 1.3994154], "split_indices": [27, 18, 0, 18, 20, 25, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [143.0, 139.0, 4.0, 132.0, 7.0, 129.0, 3.0, 3.0, 4.0, 119.0, 10.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [60.868492, -53.731434, 2015.7961, -83.95958, 197.7148, -1.7326486, 42.481983, -70.551865, -4.6517606, 19.482279, 314.36066, -0.8720709, 1.4454119, -0.5538665, 0.89505637, 1.1737697, 3.93273], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 266, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, -1, 13, 15, -1, -1, -1, -1, -1, -1], "loss_changes": [32369486.0, 1057891.8, 39831390.0, 595810.06, 305693.5, 0.0, 0.0, 442490.25, 0.0, 41658.85, 93687.75, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 9, 9, 10, 10], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, -1, 14, 16, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, 1.3137271, 4.0, 0.36177117, 0.1796442, -1.7326486, 42.481983, -0.0155352615, -4.6517606, -0.4427688, 0.68735766, -0.8720709, 1.4454119, -0.5538665, 0.89505637, 1.1737697, 3.93273], "split_indices": [29, 26, 11, 29, 29, 0, 0, 29, 0, 17, 20, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [143.0, 136.0, 7.0, 122.0, 14.0, 4.0, 3.0, 119.0, 3.0, 6.0, 8.0, 111.0, 8.0, 3.0, 3.0, 3.0, 5.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "17", "size_leaf_vector": "1"}}, {"base_weights": [9.776079, -70.2486, 28.28227, -44.623074, -554.40533, -72.55646, 276.8493, -7.800812, -1.9010321, -1.283939, -0.2537688, 0.49738884, 3.5578585], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 267, "left_children": [1, 3, -1, 5, 7, 9, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [32677524.0, 1724555.8, 0.0, 1232341.5, 427106.25, 326024.75, 179463.5, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, -1, 6, 8, 10, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [3.2148452, 1.4532615, 28.28227, 0.33892554, 2.0, -0.47092557, -1.067449, -7.800812, -1.9010321, -1.283939, -0.2537688, 0.49738884, 3.5578585], "split_indices": [27, 18, 0, 20, 11, 27, 22, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [143.0, 140.0, 3.0, 134.0, 6.0, 124.0, 10.0, 3.0, 3.0, 56.0, 68.0, 3.0, 7.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [62.382557, -12.373461, 24.763824, 35.143593, -852.35077, -39.738728, 18.253107, -13.029115, -1.886774, -0.94202703, 1.2678996], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 268, "left_children": [1, 3, -1, 5, 7, 9, -1, -1, -1, -1, -1], "loss_changes": [24032206.0, 5218758.5, 0.0, 16695145.0, 1847197.0, 1100318.5, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5], "right_children": [2, 4, -1, 6, 8, 10, -1, -1, -1, -1, -1], "split_conditions": [3.0304446, 1.4532615, 24.763824, 0.8360413, 2.3330073, -0.20332481, 18.253107, -13.029115, -1.886774, -0.94202703, 1.2678996], "split_indices": [21, 18, 0, 18, 20, 20, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [132.0, 129.0, 3.0, 123.0, 6.0, 119.0, 4.0, 3.0, 3.0, 90.0, 29.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-13.913756, 33.56232, -692.8693, -34.43812, 21.672106, -12.931395, -1.7719427, -91.55158, 151.37335, -1.7390869, -0.6360263, -0.65586984, 2.332538], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 269, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, 11, -1, -1, -1, -1], "loss_changes": [4439398.5, 18791332.0, 2556615.5, 1351007.0, 0.0, 0.0, 0.0, 212189.25, 548259.4, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 8, 8], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, 12, -1, -1, -1, -1], "split_conditions": [1.4532615, 1.1965036, 2.3330073, -0.19738343, 21.672106, -12.931395, -1.7719427, -0.6744743, -0.21507357, -1.7390869, -0.6360263, -0.65586984, 2.332538], "split_indices": [18, 29, 20, 18, 0, 0, 0, 22, 27, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [136.0, 128.0, 8.0, 125.0, 3.0, 3.0, 5.0, 96.0, 29.0, 23.0, 73.0, 8.0, 21.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [-41.104504, -4.077178, -9.95574, -59.305523, 17.50417, -87.805, 66.65912, -1.2848592, -0.58172995, -0.18911655, 3.2654004], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 270, "left_children": [1, 3, -1, 5, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [4731596.0, 12700355.0, 0.0, 461775.8, 0.0, 117874.375, 539923.25, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6], "right_children": [2, 4, -1, 6, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [1.7508882, 1.1965036, -9.95574, -0.0155352615, 17.50417, -0.29037982, 0.59401447, -1.2848592, -0.58172995, -0.18911655, 3.2654004], "split_indices": [29, 29, 0, 29, 0, 29, 24, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [133.0, 129.0, 4.0, 126.0, 3.0, 103.0, 23.0, 42.0, 61.0, 18.0, 5.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-9.27912, -95.407906, 23.86937, -61.655945, -9.876094, -81.292915, 309.01913, -0.6875283, -4.559573, 1.6210269, 3.786558], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 271, "left_children": [1, 3, -1, 5, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [29740300.0, 4124787.0, 0.0, 1007754.7, 0.0, 574682.8, 10180.25, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6], "right_children": [2, 4, -1, 6, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [3.5387294, 1.2440649, 23.86937, 1.6850597, -9.876094, 0.36177117, 2.0, -0.6875283, -4.559573, 1.6210269, 3.786558], "split_indices": [27, 29, 0, 26, 0, 29, 7, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [142.0, 138.0, 4.0, 134.0, 4.0, 128.0, 6.0, 125.0, 3.0, 3.0, 3.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [64.98206, -43.220543, 2018.8176, -69.910904, 177.19527, -6.9633727, 42.29293, -55.49182, -4.5253763, 12.628972, 299.02887, -0.7883653, 1.8816344, -0.62462515, 0.8458822, 1.2887554, 3.7532573], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 272, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, -1, 13, 15, -1, -1, -1, -1, -1, -1], "loss_changes": [28204996.0, 759515.3, 44957852.0, 603789.44, 276888.28, 0.0, 0.0, 646198.1, 0.0, 43110.496, 55436.625, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 9, 9, 10, 10], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, -1, 14, 16, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, 1.4375046, 3.0, 0.25419647, 0.1796442, -6.9633727, 42.29293, 0.022172607, -4.5253763, -0.4427688, 0.8593422, -0.7883653, 1.8816344, -0.62462515, 0.8458822, 1.2887554, 3.7532573], "split_indices": [29, 26, 13, 29, 29, 0, 0, 29, 0, 17, 27, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [132.0, 126.0, 6.0, 113.0, 13.0, 3.0, 3.0, 110.0, 3.0, 6.0, 7.0, 101.0, 9.0, 3.0, 3.0, 3.0, 4.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "17", "size_leaf_vector": "1"}}, {"base_weights": [-11.971081, -64.09478, 590.8999, -31.19334, -10.679906, 19.091871, -142.09372, -65.54979, 415.07788, -2.4557216, 0.22752771, -1.0430306, 0.10686534, 1.5441788, 5.9270234], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 273, "left_children": [1, 3, 5, 7, -1, -1, 9, 11, 13, -1, -1, -1, -1, -1, -1], "loss_changes": [4342890.5, 4161337.2, 10900716.0, 1928377.8, 0.0, 0.0, 142074.19, 343835.25, 325097.75, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 6, 6, 7, 7, 8, 8], "right_children": [2, 4, 6, 8, -1, -1, 10, 12, 14, -1, -1, -1, -1, -1, -1], "split_conditions": [1.7035879, 1.2473981, 2.2768104, 0.1796442, -10.679906, 19.091871, -0.37846264, -0.39264774, 6.0, -2.4557216, 0.22752771, -1.0430306, 0.10686534, 1.5441788, 5.9270234], "split_indices": [21, 25, 21, 29, 0, 0, 17, 21, 13, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [136.0, 126.0, 10.0, 123.0, 3.0, 3.0, 7.0, 115.0, 8.0, 4.0, 3.0, 76.0, 39.0, 4.0, 4.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [44.604942, -65.50493, 1703.5425, -78.8504, 3.945834, -2.3544807, 41.861675, -124.10466, -37.583244, -1.0764759, -3.399095, -0.5554683, 2.1062546], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 274, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, 11, -1, -1, -1, -1], "loss_changes": [26415572.0, 866134.8, 44310104.0, 243699.0, 0.0, 0.0, 0.0, 187110.31, 328253.1, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 8, 8], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, 12, -1, -1, -1, -1], "split_conditions": [1.2473981, 2.2059762, 5.0, -0.39440298, 3.945834, -2.3544807, 41.861675, -0.22266608, 1.0883759, -1.0764759, -3.399095, -0.5554683, 2.1062546], "split_indices": [25, 27, 11, 26, 0, 0, 0, 20, 20, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [143.0, 135.0, 8.0, 132.0, 3.0, 5.0, 3.0, 62.0, 70.0, 59.0, 3.0, 66.0, 4.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [50.91168, 25.564884, -24.826061, 23.865627, -788.6294, -47.1127, 17.509838, -12.884994, -2.3098752, -1.0394635, 0.6361441], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 275, "left_children": [1, -1, 3, 5, 7, 9, -1, -1, -1, -1, -1], "loss_changes": [25871996.0, 0.0, 4965284.0, 15528746.0, 1932208.5, 774365.7, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4, 5, 5], "right_children": [2, -1, 4, 6, 8, 10, -1, -1, -1, -1, -1], "split_conditions": [-0.8811961, 25.564884, 1.4532615, 0.8360413, 2.9190056, -0.2967835, 17.509838, -12.884994, -2.3098752, -1.0394635, 0.6361441], "split_indices": [28, 0, 18, 18, 18, 27, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [135.0, 3.0, 132.0, 125.0, 7.0, 121.0, 4.0, 3.0, 4.0, 80.0, 41.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-15.044178, -46.741783, 8.4378, -65.65571, 261.71124, -30.634632, -131.81561, 0.5147341, 3.7753928, -1.2356557, 0.12410626, -0.9984296, -3.6121373], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 276, "left_children": [1, 3, -1, 5, 7, 9, 11, -1, -1, -1, -1, -1, -1], "loss_changes": [3823311.2, 804761.0, 0.0, 292520.56, 175335.38, 341564.78, 286605.75, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6], "right_children": [2, 4, -1, 6, 8, 10, 12, -1, -1, -1, -1, -1, -1], "split_conditions": [2.0373242, 1.9500875, 8.4378, 5.0, -0.89633024, -0.49849156, 0.5174845, 0.5147341, 3.7753928, -1.2356557, 0.12410626, -0.9984296, -3.6121373], "split_indices": [25, 27, 0, 8, 22, 27, 27, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [138.0, 134.0, 4.0, 127.0, 7.0, 84.0, 43.0, 3.0, 4.0, 26.0, 58.0, 39.0, 4.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [-2.599948, 25.668953, -75.99624, -41.428787, -8.410812, -63.814125, 321.9027, -1.1181891, -0.17022985, 5.5169573, -0.45789206], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 277, "left_children": [1, -1, 3, 5, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [27163400.0, 0.0, 3667652.5, 1118511.5, 0.0, 285597.06, 701256.6, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 5, 5, 6, 6], "right_children": [2, -1, 4, 6, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [-0.8811961, 25.668953, 1.4135019, 1.8196839, -8.410812, -0.4363957, 2.0008802, -1.1181891, -0.17022985, 5.5169573, -0.45789206], "split_indices": [28, 0, 29, 27, 0, 27, 21, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [142.0, 3.0, 139.0, 134.0, 5.0, 127.0, 7.0, 62.0, 65.0, 4.0, 3.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [48.211388, -36.417477, 23.235683, 10.4256115, -894.48047, -51.883583, 19.705837, -12.793257, -2.8599014, -0.91911435, 0.9828563], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 278, "left_children": [1, 3, -1, 5, 7, 9, -1, -1, -1, -1, -1], "loss_changes": [26850812.0, 5435647.0, 0.0, 15857962.0, 1273191.5, 766412.7, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5], "right_children": [2, 4, -1, 6, 8, 10, -1, -1, -1, -1, -1], "split_conditions": [3.5387294, 1.1764319, 23.235683, 0.6820698, 2.9190056, 0.0526399, 19.705837, -12.793257, -2.8599014, -0.91911435, 0.9828563], "split_indices": [27, 18, 0, 18, 18, 27, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [138.0, 134.0, 4.0, 128.0, 6.0, 125.0, 3.0, 3.0, 3.0, 99.0, 26.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [34.87371, 24.070578, -30.805674, 12.656279, -688.98145, -48.54542, 20.597742, -12.697306, -1.8696835, -0.9317491, 0.6665209], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 279, "left_children": [1, -1, 3, 5, 7, 9, -1, -1, -1, -1, -1], "loss_changes": [23133322.0, 0.0, 4156600.0, 17264526.0, 2386348.0, 695134.56, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4, 5, 5], "right_children": [2, -1, 4, 6, 8, 10, -1, -1, -1, -1, -1], "split_conditions": [-1.222348, 24.070578, 1.1764319, 0.8360413, 2.3330073, -0.20714763, 20.597742, -12.697306, -1.8696835, -0.9317491, 0.6665209], "split_indices": [22, 0, 18, 18, 20, 29, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [147.0, 3.0, 144.0, 136.0, 8.0, 133.0, 3.0, 3.0, 5.0, 96.0, 37.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [54.464474, 23.89005, -11.680083, 33.542683, -683.71014, -28.110567, 20.443258, -12.602077, -1.854101, -0.75042593, 1.1942735], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 280, "left_children": [1, -1, 3, 5, 7, 9, -1, -1, -1, -1, -1], "loss_changes": [22418630.0, 0.0, 4338528.5, 16669825.0, 2351619.5, 916034.9, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4, 5, 5], "right_children": [2, -1, 4, 6, 8, 10, -1, -1, -1, -1, -1], "split_conditions": [-1.222348, 23.89005, 1.1764319, 0.8360413, 2.3330073, -0.20332481, 20.443258, -12.602077, -1.854101, -0.75042593, 1.1942735], "split_indices": [22, 0, 18, 18, 20, 20, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [144.0, 3.0, 141.0, 133.0, 8.0, 130.0, 3.0, 3.0, 5.0, 99.0, 31.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [48.646904, 24.084265, -15.458941, 27.273502, -678.4793, -31.369223, 20.289934, -12.507561, -1.8386482, -0.5437654, 2.6625137], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 281, "left_children": [1, -1, 3, 5, 7, 9, -1, -1, -1, -1, -1], "loss_changes": [22880096.0, 0.0, 4211776.0, 16497931.0, 2317394.5, 952557.5, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 4, 4, 5, 5], "right_children": [2, -1, 4, 6, 8, 10, -1, -1, -1, -1, -1], "split_conditions": [-1.1819931, 24.084265, 1.4532615, 1.2220678, 2.3330073, 0.33892554, 20.289934, -12.507561, -1.8386482, -0.5437654, 2.6625137], "split_indices": [22, 0, 18, 29, 20, 20, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [150.0, 3.0, 147.0, 139.0, 8.0, 136.0, 3.0, 3.0, 5.0, 127.0, 9.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [5.8987417, -66.35803, 23.166656, -31.353409, -11.436379, -50.057472, 5.4693384, 3.2720942, -0.623189], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 282, "left_children": [1, 3, -1, 5, -1, 7, -1, -1, -1], "loss_changes": [22026838.0, 4790877.0, 0.0, 1379368.8, 0.0, 584716.3, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5], "right_children": [2, 4, -1, 6, -1, 8, -1, -1, -1], "split_conditions": [4.2314267, 2.0373242, 23.166656, 0.8306982, -11.436379, -0.94914436, 5.4693384, 3.2720942, -0.623189], "split_indices": [18, 25, 0, 29, 0, 23, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [130.0, 127.0, 3.0, 124.0, 3.0, 121.0, 3.0, 3.0, 118.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [45.01144, -35.219597, 22.02267, 0.84283775, -9.730314, -50.641384, 16.481522, -0.9452979, 0.734745], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 283, "left_children": [1, 3, -1, 5, -1, 7, -1, -1, -1], "loss_changes": [24135740.0, 4566587.5, 0.0, 11193792.0, 0.0, 704195.4, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5], "right_children": [2, 4, -1, 6, -1, 8, -1, -1, -1], "split_conditions": [3.5387294, 1.7508882, 22.02267, 1.1965036, -9.730314, -0.11261157, 16.481522, -0.9452979, 0.734745], "split_indices": [27, 29, 0, 29, 0, 25, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [138.0, 134.0, 4.0, 130.0, 4.0, 127.0, 3.0, 94.0, 33.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [-20.099728, 24.117182, -752.7059, -33.693047, 18.969452, -12.308199, -2.1965342, -58.797504, 356.4871, -1.037761, 0.33688375, 0.73446584, 5.1160207], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 284, "left_children": [1, 3, 5, 7, -1, -1, -1, 9, 11, -1, -1, -1, -1], "loss_changes": [4553327.0, 14463827.0, 1768380.5, 1294314.6, 0.0, 0.0, 0.0, 515169.0, 313596.62, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 7, 7, 8, 8], "right_children": [2, 4, 6, 8, -1, -1, -1, 10, 12, -1, -1, -1, -1], "split_conditions": [1.4594278, 1.103419, 2.3330073, 0.8139121, 18.969452, -12.308199, -2.1965342, -0.2967835, 6.0, -1.037761, 0.33688375, 0.73446584, 5.1160207], "split_indices": [19, 25, 20, 20, 0, 0, 0, 27, 13, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [139.0, 132.0, 7.0, 129.0, 3.0, 3.0, 4.0, 122.0, 7.0, 82.0, 40.0, 3.0, 4.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "13", "size_leaf_vector": "1"}}, {"base_weights": [8.873477, -60.717907, 23.553894, -39.790924, -5.9684124, -57.071053, 5.230959, -0.8207, 0.9065819], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 285, "left_children": [1, 3, -1, 5, -1, 7, -1, -1, -1], "loss_changes": [22678270.0, 1490812.2, 0.0, 1304012.1, 0.0, 480153.2, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5], "right_children": [2, 4, -1, 6, -1, 8, -1, -1, -1], "split_conditions": [1.7642304, 1.4532615, 23.553894, 0.6820698, -5.9684124, -0.0155352615, 5.230959, -0.8207, 0.9065819], "split_indices": [29, 18, 0, 18, 0, 29, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [137.0, 134.0, 3.0, 130.0, 4.0, 127.0, 3.0, 109.0, 18.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [41.237915, -21.531345, 23.377237, 8.74835, -11.062426, -47.51943, 19.900253, -0.64027923, 1.7513468], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 286, "left_children": [1, 3, -1, 5, -1, 7, -1, -1, -1], "loss_changes": [21672876.0, 4837962.5, 0.0, 16148176.0, 0.0, 529474.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5], "right_children": [2, 4, -1, 6, -1, 8, -1, -1, -1], "split_conditions": [2.830827, 1.7508882, 23.377237, 1.2432504, -11.062426, 1.4375046, 19.900253, -0.64027923, 1.7513468], "split_indices": [29, 29, 0, 29, 0, 26, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [149.0, 146.0, 3.0, 143.0, 3.0, 140.0, 3.0, 131.0, 9.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [56.123795, -50.408764, 1920.0496, -73.33297, 133.30351, 41.248695, -7.647579, -61.302574, -3.1369078, -19.99712, 250.79112, -0.87474376, 0.55604917, -0.82184225, 0.47164267, 3.1675222, 1.0561696], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 287, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, -1, 13, 15, -1, -1, -1, -1, -1, -1], "loss_changes": [25712346.0, 527778.94, 44591484.0, 298808.5, 257191.14, 0.0, 0.0, 329193.28, 0.0, 33115.67, 43110.125, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 9, 9, 10, 10], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, -1, 14, 16, -1, -1, -1, -1, -1, -1], "split_conditions": [1.2440649, 1.4375046, -0.9297316, 0.19307804, 0.1796442, 41.248695, -7.647579, -0.19738343, -3.1369078, -0.4427688, -0.83306056, -0.87474376, 0.55604917, -0.82184225, 0.47164267, 3.1675222, 1.0561696], "split_indices": [29, 26, 22, 18, 29, 0, 0, 18, 0, 17, 28, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [128.0, 122.0, 6.0, 109.0, 13.0, 3.0, 3.0, 105.0, 4.0, 6.0, 7.0, 86.0, 19.0, 3.0, 3.0, 4.0, 3.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "17", "size_leaf_vector": "1"}}, {"base_weights": [2.336459, -75.87354, 21.60485, -42.12479, -9.651453, -97.37628, 44.151566, -1.5041195, -0.75421333, -2.6501427, 0.680038], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 288, "left_children": [1, 3, -1, 5, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [24132144.0, 4100872.5, 0.0, 643068.8, 0.0, 84113.56, 408840.06, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6], "right_children": [2, 4, -1, 6, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [3.5387294, 2.0373242, 21.60485, -0.2967835, -9.651453, -0.47624645, 1.0, -1.5041195, -0.75421333, -2.6501427, 0.680038], "split_indices": [27, 25, 0, 27, 0, 28, 3, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [141.0, 137.0, 4.0, 133.0, 4.0, 81.0, 52.0, 22.0, 59.0, 3.0, 49.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [-22.097113, 15.407852, -8.435401, -40.990158, 18.304167, -69.03145, 266.08292, -0.94323844, 0.20639828, 3.1879895, 0.94105065], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 289, "left_children": [1, 3, -1, 5, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [4233802.0, 13587110.0, 0.0, 1129131.9, 0.0, 271846.44, 69684.06, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6], "right_children": [2, 4, -1, 6, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [1.7508882, 1.2220678, -8.435401, 0.1796442, 18.304167, -0.12909646, 4.0, -0.94323844, 0.20639828, 3.1879895, 0.94105065], "split_indices": [29, 29, 0, 29, 0, 25, 8, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [136.0, 131.0, 5.0, 128.0, 3.0, 118.0, 10.0, 92.0, 26.0, 7.0, 3.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [90.613045, -17.51564, 22.38101, -40.15209, 4.192522, 192.52896, -61.72528, -0.875203, 3.9383187, -0.526916, -2.7888615], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 290, "left_children": [1, 3, -1, 5, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [29065058.0, 1203220.6, 0.0, 589133.25, 0.0, 598246.25, 193218.6, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6], "right_children": [2, 4, -1, 6, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [1.4135019, 0.5643499, 22.38101, -0.75929564, 4.192522, -0.19222529, 0.3882633, -0.875203, 3.9383187, -0.526916, -2.7888615], "split_indices": [29, 29, 0, 23, 0, 29, 20, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [124.0, 119.0, 5.0, 114.0, 5.0, 9.0, 105.0, 4.0, 5.0, 102.0, 3.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [30.498188, -30.753191, 23.045124, 1.2249619, -9.51989, -53.43381, 19.413301, -0.10984123, -1.3900055], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 291, "left_children": [1, 3, -1, 5, -1, 7, -1, -1, -1], "loss_changes": [21242648.0, 4390714.5, 0.0, 15480268.0, 0.0, 514242.16, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5], "right_children": [2, 4, -1, 6, -1, 8, -1, -1, -1], "split_conditions": [3.0304446, 1.7508882, 23.045124, 1.2432504, -9.51989, 5.0, 19.413301, -0.10984123, -1.3900055], "split_indices": [21, 29, 0, 29, 0, 8, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [151.0, 148.0, 3.0, 144.0, 4.0, 141.0, 3.0, 95.0, 46.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [46.004406, -18.579784, 22.94975, 15.992062, -9.552381, -45.892864, 20.696928, -0.75685155, 1.2621285], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 292, "left_children": [1, 3, -1, 5, -1, 7, -1, -1, -1], "loss_changes": [20813326.0, 4548851.0, 0.0, 17379848.0, 0.0, 691494.6, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5], "right_children": [2, 4, -1, 6, -1, 8, -1, -1, -1], "split_conditions": [2.9190056, 1.1198277, 22.94975, 0.5547066, -9.552381, -0.031931423, 20.696928, -0.75685155, 1.2621285], "split_indices": [18, 18, 0, 18, 0, 20, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [142.0, 139.0, 3.0, 135.0, 4.0, 132.0, 3.0, 113.0, 19.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [38.101856, 22.961884, -26.226854, 6.9218464, -9.292759, -50.200348, 19.188097, -0.082573585, -1.4187647], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 293, "left_children": [1, -1, 3, 5, -1, 7, -1, -1, -1], "loss_changes": [20977860.0, 0.0, 4227346.0, 15058451.0, 0.0, 514066.34, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 5, 5], "right_children": [2, -1, 4, 6, -1, 8, -1, -1, -1], "split_conditions": [-1.222348, 22.961884, 1.7508882, 1.2432504, -9.292759, 5.0, 19.188097, -0.082573585, -1.4187647], "split_indices": [22, 0, 29, 29, 0, 8, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [143.0, 3.0, 140.0, 136.0, 4.0, 133.0, 3.0, 92.0, 41.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [57.59843, -44.553913, 23.123922, -69.09533, 153.10489, -91.17744, -3.4661956, 271.3708, -2.39944, -1.4146379, -0.70432264, 0.66544896, -0.8258769, 0.9540382, 3.8341806], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 294, "left_children": [1, 3, -1, 5, 7, 9, 11, 13, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [31889136.0, 655730.4, 0.0, 172132.81, 762381.0, 82859.94, 177218.25, 199971.94, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7], "right_children": [2, 4, -1, 6, 8, 10, 12, 14, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.4135019, 0.1796442, 23.123922, -0.2197353, 5.0, -0.47624645, -0.019995142, 0.25780264, -2.39944, -1.4146379, -0.70432264, 0.66544896, -0.8258769, 0.9540382, 3.8341806], "split_indices": [29, 29, 0, 27, 8, 28, 25, 20, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [137.0, 132.0, 5.0, 118.0, 14.0, 88.0, 30.0, 11.0, 3.0, 24.0, 64.0, 16.0, 14.0, 5.0, 6.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [65.25876, -35.418427, 1718.2875, -77.17666, 47.02697, -6.9097276, 43.00316, -85.70398, 0.8149764, 77.744194, -2.872356, -0.71544963, -2.312181, 1.2672925, -1.4627099], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 295, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, -1, 13, -1, -1, -1, -1, -1], "loss_changes": [23193724.0, 458078.62, 52737984.0, 126054.94, 484352.7, 0.0, 0.0, 156495.44, 0.0, 479417.56, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 9, 9], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, -1, 14, -1, -1, -1, -1, -1], "split_conditions": [1.2432504, -0.20714763, 4.0, 2.0, 0.8360413, -6.9097276, 43.00316, -0.21593031, 0.8149764, 0.8215076, -2.872356, -0.71544963, -2.312181, 1.2672925, -1.4627099], "split_indices": [29, 29, 13, 4, 18, 0, 0, 20, 0, 24, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [138.0, 131.0, 7.0, 87.0, 44.0, 4.0, 3.0, 83.0, 4.0, 41.0, 3.0, 77.0, 6.0, 34.0, 7.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "15", "size_leaf_vector": "1"}}, {"base_weights": [-12.372605, -89.31443, 21.290646, -54.573883, -10.094262, -89.413284, 40.117325, -0.8098845, -2.6923099, 2.2446706, -0.2715106], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 296, "left_children": [1, 3, -1, 5, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [23751504.0, 4387966.0, 0.0, 448954.97, 0.0, 128138.875, 464947.9, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 3, 3, 5, 5, 6, 6], "right_children": [2, 4, -1, 6, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [3.5387294, 2.0373242, 21.290646, -0.19738343, -10.094262, -0.20831245, -0.09158805, -0.8098845, -2.6923099, 2.2446706, -0.2715106], "split_indices": [27, 25, 0, 18, 0, 18, 25, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [142.0, 138.0, 4.0, 134.0, 4.0, 98.0, 36.0, 95.0, 3.0, 9.0, 27.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "11", "size_leaf_vector": "1"}}, {"base_weights": [31.492098, 22.58675, -28.108728, 6.997938, -8.718517, -36.402885, 15.460027, -0.7515584, 0.5765312], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 297, "left_children": [1, -1, 3, 5, -1, 7, -1, -1, -1], "loss_changes": [20373232.0, 0.0, 4449338.5, 9741570.0, 0.0, 521917.38, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 5, 5], "right_children": [2, -1, 4, 6, -1, 8, -1, -1, -1], "split_conditions": [-1.222348, 22.58675, 1.7508882, 1.54191, -8.718517, 0.19972922, 15.460027, -0.7515584, 0.5765312], "split_indices": [22, 0, 29, 25, 0, 26, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [152.0, 3.0, 149.0, 144.0, 5.0, 141.0, 3.0, 100.0, 41.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "9", "size_leaf_vector": "1"}}, {"base_weights": [74.39799, -37.82285, 1972.865, -89.100586, 44.15928, 39.827312, -0.29581025, -144.2889, -63.26844, -4.05745, 361.606, -1.5198299, -0.639506, -0.8234831, -0.018042687, -0.4221705, 2.6371932, 0.3864417, 5.941413], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 298, "left_children": [1, 3, 5, 7, 9, -1, -1, 11, 13, 15, 17, -1, -1, -1, -1, -1, -1, -1, -1], "loss_changes": [30540616.0, 575663.75, 32315400.0, 110603.44, 812733.75, 0.0, 0.0, 6330.3125, 69034.0, 491369.22, 502676.8, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 7, 7, 8, 8, 9, 9, 10, 10], "right_children": [2, 4, 6, 8, 10, -1, -1, 12, 14, 16, 18, -1, -1, -1, -1, -1, -1, -1, -1], "split_conditions": [1.0485128, -0.2967835, 0.4490717, -0.6003495, 0.4568698, 39.827312, -0.29581025, 0.71666294, -0.20020312, 0.35387513, -0.3928138, -1.5198299, -0.639506, -0.8234831, -0.018042687, -0.4221705, 2.6371932, 0.3864417, 5.941413], "split_indices": [25, 27, 24, 27, 22, 0, 0, 24, 25, 23, 21, 0, 0, 0, 0, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [142.0, 135.0, 7.0, 83.0, 52.0, 3.0, 4.0, 25.0, 58.0, 46.0, 6.0, 22.0, 3.0, 44.0, 14.0, 41.0, 5.0, 3.0, 3.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "19", "size_leaf_vector": "1"}}, {"base_weights": [-7.9605403, 22.318882, -75.7752, -36.97496, -8.84401, -79.073944, 47.101517, -1.592956, -0.58161736, 0.9536109, -0.5966911], "categories": [], "categories_nodes": [], "categories_segments": [], "categories_sizes": [], "default_left": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "id": 299, "left_children": [1, -1, 3, 5, -1, 7, 9, -1, -1, -1, -1], "loss_changes": [20674676.0, 0.0, 4108690.8, 453248.5, 0.0, 133310.31, 227259.97, 0.0, 0.0, 0.0, 0.0], "parents": [2147483647, 0, 0, 2, 2, 3, 3, 5, 5, 6, 6], "right_children": [2, -1, 4, 6, -1, 8, 10, -1, -1, -1, -1], "split_conditions": [-1.222348, 22.318882, 1.2440649, -0.15932864, -8.84401, -0.6862769, -0.28870195, -1.592956, -0.58161736, 0.9536109, -0.5966911], "split_indices": [22, 0, 29, 25, 0, 23, 17, 0, 0, 0, 0], "split_type": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "sum_hessian": [134.0, 3.0, 131.0, 126.0, 5.0, 84.0, 42.0, 16.0, 68.0, 29.0, 13.0], "tree_param": {"num_deleted": "0", "num_feature": "30", "num_nodes": "11", "size_leaf_vector": "1"}}]}, "name": "gbtree"}, "learner_model_param": {"base_score": "1.4536682E3", "boost_from_average": "1", "num_class": "0", "num_feature": "30", "num_target": "1"}, "objective": {"name": "reg:<PERSON><PERSON><PERSON><PERSON>", "reg_loss_param": {"scale_pos_weight": "1"}}}, "version": [2, 1, 4]}