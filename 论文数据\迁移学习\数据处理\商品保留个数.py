import pandas as pd

file_path = r"C:\Users\<USER>\Desktop\Copper.xlsx"
data = pd.read_excel(file_path,sheet_name='Sheet1') # 导入原始数据
df = pd.DataFrame(data)
# 假设df是你的DataFrame，并且'Commodity(s)'是其中的一列

def truncate_commodities(commodities_str):
    """
    截断Commodity(s)字符串，只保留前四个词。
    
    :param commodities_str: 以逗号分隔的商品字符串
    :return: 处理后的商品字符串
    """
    if isinstance(commodities_str, str):  # 确保我们处理的是字符串
        # 分割字符串并去除可能存在的多余空格
        commodities = [item.strip() for item in commodities_str.split(',')]
        # 只保留前四个元素，并用逗号连接回字符串
        truncated = ', '.join(commodities[:3])
        return truncated
    else:
        # 如果不是字符串（例如缺失值），返回原样
        return commodities_str

# 应用函数到整个列
df['Commodity(s)_truncated'] = df['Commodity(s)'].apply(truncate_commodities)

# 查看结果
print(df[['Commodity(s)', 'Commodity(s)_truncated']].head())

df.to_excel(r"C:\Users\<USER>\Desktop\Copper11.xlsx", index=False)

