import pandas as pd


file_path = r"C:\Users\<USER>\Desktop\Copper11_optimized_by_mine_type.xlsx"
# file_path = r"C:\Users\<USER>\Desktop\Copper11_optimized.xlsx"
data = pd.read_excel(file_path,sheet_name='Sheet1') # 导入原始数据
df = pd.DataFrame(data)


# # 使用fillna填充缺失值
# df['Average Depth of Geologic Deposit1'] = df['Average Depth of Geologic Deposit1'].fillna(df['Average Depth of Geologic Deposit2'])
# df['Geologic Ore Body Type1'] = df['Geologic Ore Body Type1'].fillna(df['Geologic Ore Body Type2'])
# df['Ore Minerals1'] = df['Ore Minerals1'].fillna(df['Ore Minerals2'])
# df['Geologic Ore Body Type'] = df['Geologic Ore Body Type'].fillna(df['Predicted Geologic Ore Body Type'])
# df['Ore Minerals'] = df['Ore Minerals'].fillna(df['Predicted Ore Minerals'])
# df['Production Forms'] = df['Production Forms'].fillna(df['Predicted Production Forms'])
# df['Mining Method'] = df['Mining Method'].fillna(df['Predicted Mining Method'])
# df['MillHead Grade - percent(%)'] = df['MillHead Grade - percent(%)'].fillna(df['MillHead Grade1 - percent(%)'])
# df['Production Capacity(tonnes)'] = df['Production Capacity(tonnes)'].fillna(df['Commodity Production(tonnes)'])
df['Estimated Mine Life'] = df['Estimated Mine Life'].fillna(df['Predicted Estimated Mine Life'])
# 或者使用where方法
# df['Geologic Ore Body Type1'] = df['Geologic Ore Body Type1'].where(df['Geologic Ore Body Type1'].notnull(), df['Geologic Ore Body Type2'])

# # 检查结果
# print(df[['Average Depth of Geologic Deposit1', 'Average Depth of Geologic Deposit2']].head())

df.to_excel(r"C:\Users\<USER>\Desktop\Copper11_optimized_by_mine_type.xlsx", index=False)
# df.to_excel(r"C:\Users\<USER>\Desktop\Copper11_optimized.xlsx", index=False)