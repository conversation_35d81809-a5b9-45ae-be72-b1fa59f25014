import matplotlib.pyplot as plt
import seaborn as sns
import pandas as pd
import numpy as np

# 设置 Matplotlib 的全局字体为支持中文的字体
plt.rcParams['font.sans-serif'] = ['SimHei']  # 使用黑体
plt.rcParams['axes.unicode_minus'] = False  # 正常显示负号

# # 假设这是你的Excel文件路径
# file_path = r"C:\Users\<USER>\Desktop\CobaltCAC.xlsx"

# # 读取Excel数据
# data = pd.read_excel(file_path, sheet_name='Sheet3')
# df = pd.DataFrame(data)


# # 创建图形
# fig, ax1 = plt.subplots(figsize=(10, 6))

# # 绘制散点图
# sns.stripplot(x=df['钴矿可供成本（美元/吨）'], jitter=True, color="blue", size=5)

# # 创建第二个坐标轴用于密度分布图
# ax2 = ax1.twinx()

# # 绘制密度分布图
# sns.kdeplot(df['钴矿可供成本（美元/吨）'], color="green", shade=True, alpha=0.5, ax=ax2)

# # 设置Y轴从0开始
# ax1.set_ylim(0, )


# # 添加标题和标签
# ax1.set_title('钴矿可供成本分布', fontsize=14)
# ax2.set_ylabel('密度', fontsize=12)

# # 显示图形
# plt.show()





# # #####4.2
# file_path = r"C:\Users\<USER>\Desktop\CobaltCAC.xlsx"

# # 读取Excel数据
# data = pd.read_excel(file_path, sheet_name='Sheet3')
# df = pd.DataFrame(data)

# # 过滤出价格在400000以下的数据
# filtered_df = df[df['钴矿可供成本（美元/吨）'] < 20000]

# # 创建图表
# fig, ax = plt.subplots(figsize=(8, 6))  # 调整图表大小以增加空间感

# # 绘制累积可供曲线，横轴为累积储量，纵轴为平均开采成本
# # 确保数据按累积储量排序
# filtered_df = filtered_df.sort_values(by='累积储量(万吨）')

# # 绘制曲线
# ax.fill_between(filtered_df['累积储量(万吨）'], 
#                 filtered_df['钴矿可供成本（美元/吨）'], 
#                 color='skyblue', alpha=0.7, label='累积储量(万吨）')

# # 添加线条以突出曲线
# ax.plot(filtered_df['累积储量(万吨）'], 
#         filtered_df['钴矿可供成本（美元/吨）'], 
#         color='darkblue', linewidth=2)

# # 设置X轴和Y轴范围
# ax.set_ylim(0, 20000)  # 确保Y轴显示范围不超过100000
# ax.set_xlim(0, 3046)  # 动态调整X轴范围以适应数据


# # 设置其他图表属性
# ax.set_xlabel('钴矿累积储量(万吨）', fontsize=12, fontweight='bold')
# ax.set_ylabel('钴矿可供成本（美元/吨）', fontsize=12, fontweight='bold')
# ax.set_title('钴矿累积可供曲线 ', fontsize=14, fontweight='bold')

# # 添加网格线
# ax.grid(True, linestyle='--', alpha=0.7)

# # 添加图例
# ax.legend(fontsize=12, loc='upper left')

# # 显示图表
# plt.tight_layout()  # 自动调整子图参数，使其填充整个图表区域
# plt.show()




# ##### 可供性预警

# # 假设这是你的Excel文件路径
# file_path = r"C:\Users\<USER>\Desktop\CobaltCAC.xlsx"

# # 读取Excel数据
# data = pd.read_excel(file_path, sheet_name='Sheet3')
# df = pd.DataFrame(data)
# # 过滤出价格在40000以下的数据
# filtered_df = df[df['钴矿可供成本（美元/吨）'] < 20000000]

# # 创建图表
# fig, ax = plt.subplots(figsize=(8, 6))  # 调整图表大小以增加空间感

# # 确保数据按累积储量和资源量排序
# filtered_df = filtered_df.sort_values(by='累积储量(万吨）')

# # 绘制整个曲线的背景填充（可选，如果需要背景色）
# ax.fill_between(filtered_df['累积储量(万吨）'], 
#                 filtered_df['钴矿可供成本（美元/吨）'], 
#                 facecolor='skyblue', alpha=0.2)  # 轻微的背景色

# # 使用不同的颜色填充特定的x区间
# # 区间1: x <= 3052.5294
# mask1 = filtered_df['累积储量(万吨）'] <= 3061.2831
# ax.fill_between(filtered_df.loc[mask1, '累积储量(万吨）'], 
#                 filtered_df.loc[mask1, '钴矿可供成本（美元/吨）'], 
#                 color='skyblue', alpha=0.7, label='可供储量')  # 保持原来的颜色

# # # 区间2: 3052.5294 < x <= 3057.5839
# # mask2 = (filtered_df['累积储量(万吨）'] > 3052.5294) & (filtered_df['累积储量(万吨）'] <= 3057.5839)
# # ax.fill_between(filtered_df.loc[mask2, '累积储量(万吨）'], 
# #                 filtered_df.loc[mask2, '钴矿可供成本（美元/吨）'], 
# #                 color='red', alpha=0.9, label='预警资源')  # 橙色填充

# # 区间3: x > 3057.5839
# mask3 = filtered_df['累积储量(万吨）'] > 3061.2831
# ax.fill_between(filtered_df.loc[mask3, '累积储量(万吨）'], 
#                 filtered_df.loc[mask3, '钴矿可供成本（美元/吨）'], 
#                 color='red', alpha=0.7,label='不可供储量')      # 红色填充

# # 添加线条以突出曲线
# ax.plot(filtered_df['累积储量(万吨）'], 
#         filtered_df['钴矿可供成本（美元/吨）'], 
#         color='darkblue', linewidth=2)

# # 设置X轴和Y轴范围
# ax.set_ylim(0, 350000)  # 确保Y轴显示范围不超过100000
# ax.set_xlim(0, 3100)    # 动态调整X轴范围以适应数据

# # 在Y轴的指定位置添加竖直线
# # ax.axhline(y=24755, color='green', linestyle='--', alpha=0.7)
# ax.axhline(y=41389, color='red', linestyle='--', alpha=0.7)

# # # 在x轴的指定位置添加竖直线
# # ax.axvline(x=3052.5294, color='red', linestyle='--', alpha=0.7, label='分割线1')
# # ax.axvline(x=3057.5839, color='red', linestyle='--', alpha=0.7, label='分割线2')

# # 设置其他图表属性
# ax.set_xlabel('钴矿累积储量(万吨）', fontsize=12, fontweight='bold')
# ax.set_ylabel('钴矿可供成本（美元/吨）', fontsize=12, fontweight='bold')
# ax.set_title('钴矿累积可供曲线', fontsize=14, fontweight='bold')

# # 添加网格线
# ax.grid(True, linestyle='--', alpha=0.7)

# # 添加图例
# ax.legend(fontsize=12, loc='upper left')

# # 显示图表
# plt.tight_layout()  # 自动调整子图参数，使其填充整个图表区域
# plt.show()








# ######单个国家的累积可供曲线
# import pandas as pd
# import matplotlib.pyplot as plt

# file_path = r"C:\Users\<USER>\Desktop\CobaltCAC.xlsx"

# # 读取Excel数据
# data = pd.read_excel(file_path, sheet_name='Sheet3')
# df = pd.DataFrame(data)

# # 假设df是你的数据框，包含'全球区域'、'钴矿可供成本（美元/吨）'和'钴矿储量（万吨）'三列

# # 过滤出价格在40000美元以下的数据
# filtered_df = df[df['钴矿可供成本（美元/吨）'] < 350000]

# # 按国家/地区分组
# grouped = filtered_df.groupby('国家/地区')

# # 定义颜色映射
# colors = plt.cm.tab20.colors  # 使用tab20颜色映射，确保颜色不重合

# # 国家/地区名称映射字典
# country_mapping = {
#     "Australia": "澳大利亚",
#     "Guinea": "几内亚",
#     "Canada": "加拿大",
#     "Dem. Rep. Congo": "刚果民主共和国",
#     "Indonesia": "印度尼西亚",
#     "Tonga": "汤加",
#     "New Caledonia": "新喀里多尼亚",
#     "USA": "美国",
#     "Finland": "芬兰",
#     "Nauru": "瑙鲁",
#     "China": "中国",
#     "Cuba": "古巴",
#     "Russia": "俄罗斯",
#     "Côte d'Ivoire": "科特迪瓦",
#     "Solomon Islands": "所罗门群岛",
#     "Mexico": "墨西哥",
#     "Philippines": "菲律宾",
#     "Chile": "智利",
#     "Sweden": "瑞典",
#     "Vietnam": "越南",
#     "Tanzania": "坦桑尼亚",
#     "Madagascar": "马达加斯加",
#     "South Africa": "南非",
#     "Norway": "挪威",
#     "Papua New Guinea": "巴布亚新几内亚",
#     "Brazil": "巴西",
#     "Cameroon": "喀麦隆",
#     "Turkey": "土耳其",
#     "India": "印度",
#     "Namibia": "纳米比亚",
#     "Uganda": "乌干达",
#     "Morocco": "摩洛哥",
#     "Botswana": "博茨瓦纳",
#     "United Kingdom": "英国",
#     "Argentina": "阿根廷",
#     "Spain": "西班牙",
#     "Albania":"阿尔巴尼亚",
#     "Burundi":"布隆迪",
#     "Malaysia":"马来西亚",
#     "Türkiye":"土耳其",
#     "Zambia":"赞比亚"
# }

# # 遍历每个国家/地区的数据，单独绘制每个国家/地区的累积可供曲线
# for i, (region, group) in enumerate(grouped):
#     # 将国家/地区名称映射为中文
#     region_cn = country_mapping.get(region, region)
    
#     # 创建新图表
#     fig, ax = plt.subplots(figsize=(8, 6))  # 调整图表大小以增加空间感
    
#     # 按可供价格从小到大排序
#     group = group.sort_values(by='钴矿可供成本（美元/吨）')
    
#     # 计算累积储量和资源量
#     group['累积储量（万吨）'] = group['钴矿储量（万吨）'].cumsum()
    
#     # 确保曲线从0开始
#     # 在数据前面添加一个点，累积储量为0，可供成本为第一个点的可供成本
#     first_cost = group['钴矿可供成本（美元/吨）'].iloc[0]
#     extended_group = pd.DataFrame({
#         '累积储量（万吨）': [0] + group['累积储量（万吨）'].tolist(),
#         '钴矿可供成本（美元/吨）': [first_cost] + group['钴矿可供成本（美元/吨）'].tolist()
#     })
    
#     # 绘制填充区域和曲线
#     ax.fill_between(extended_group['累积储量（万吨）'], 
#                     extended_group['钴矿可供成本（美元/吨）'], 
#                     color=colors[i % len(colors)], alpha=0.5, label=region_cn)
#     ax.plot(extended_group['累积储量（万吨）'], 
#             extended_group['钴矿可供成本（美元/吨）'], 
#             color=colors[i % len(colors)], linewidth=2)
    
#     # 动态设置Y轴范围为0到该国家数据中钴矿可供成本的最大值
#     max_cost = group['钴矿可供成本（美元/吨）'].max()
#     ax.set_ylim(0, max_cost)
    
#     # 动态调整X轴范围以适应数据
#     ax.set_xlim(0, extended_group['累积储量（万吨）'].max())
    
#     # 设置其他图表属性
#     ax.set_xlabel('钴矿累积储量（万吨）', fontsize=12, fontweight='bold')
#     ax.set_ylabel('钴矿可供成本（美元/吨）', fontsize=12, fontweight='bold')
#     ax.set_title(f'{region_cn} - 钴矿累积可供曲线', fontsize=14, fontweight='bold')
    
#     # 添加网格线
#     ax.grid(True, linestyle='--', alpha=0.7)
    
#     # 添加图例
#     ax.legend(fontsize=10, title='国家/地区', loc='upper left')
    
#     # 显示图表
#     plt.tight_layout()  # 自动调整子图参数，使其填充整个图表区域
#     plt.show()

import pandas as pd
import matplotlib.pyplot as plt

file_path = r"C:\Users\<USER>\Desktop\CobaltCAC.xlsx"

# 读取Excel数据
data = pd.read_excel(file_path, sheet_name='Sheet3')
df = pd.DataFrame(data)

# 过滤出价格在40000美元以下的数据
filtered_df = df[df['钴矿可供成本（美元/吨）'] < 350000]

# 按国家/地区分组
grouped = filtered_df.groupby('国家/地区')

# 定义颜色映射
colors = plt.cm.tab20.colors  # 使用tab20颜色映射，确保颜色不重合

# 国家/地区名称映射字典
country_mapping = {
    "Australia": "澳大利亚",
    "Guinea": "几内亚",
    "Canada": "加拿大",
    "Dem. Rep. Congo": "刚果民主共和国",
    "Indonesia": "印度尼西亚",
    "Tonga": "汤加",
    "New Caledonia": "新喀里多尼亚",
    "USA": "美国",
    "Finland": "芬兰",
    "Nauru": "瑙鲁",
    "China": "中国",
    "Cuba": "古巴",
    "Russia": "俄罗斯",
    "Côte d'Ivoire": "科特迪瓦",
    "Solomon Islands": "所罗门群岛",
    "Mexico": "墨西哥",
    "Philippines": "菲律宾",
    "Chile": "智利",
    "Sweden": "瑞典",
    "Vietnam": "越南",
    "Tanzania": "坦桑尼亚",
    "Madagascar": "马达加斯加",
    "South Africa": "南非",
    "Norway": "挪威",
    "Papua New Guinea": "巴布亚新几内亚",
    "Brazil": "巴西",
    "Cameroon": "喀麦隆",
    "Turkey": "土耳其",
    "India": "印度",
    "Namibia": "纳米比亚",
    "Uganda": "乌干达",
    "Morocco": "摩洛哥",
    "Botswana": "博茨瓦纳",
    "United Kingdom": "英国",
    "Argentina": "阿根廷",
    "Spain": "西班牙",
    "Albania":"阿尔巴尼亚",
    "Burundi":"布隆迪",
    "Malaysia":"马来西亚",
    "Türkiye":"土耳其",
    "Zambia":"赞比亚"
}

# 创建ExcelWriter对象
output_file = r"C:\Users\<USER>\Desktop\国家钴矿累积可供曲线数据.xlsx"
with pd.ExcelWriter(output_file) as writer:
    # 遍历每个国家/地区的数据，单独绘制每个国家/地区的累积可供曲线
    for i, (region, group) in enumerate(grouped):
        # 将国家/地区名称映射为中文
        region_cn = country_mapping.get(region, region)
        
        # 创建新图表
        fig, ax = plt.subplots(figsize=(8, 6))  # 调整图表大小以增加空间感
        
        # 按可供价格从小到大排序
        group = group.sort_values(by='钴矿可供成本（美元/吨）')
        
        # 计算累积储量和资源量
        group['累积储量（万吨）'] = group['钴矿储量（万吨）'].cumsum()
        
        # 确保曲线从0开始
        # 在数据前面添加一个点，累积储量为0，可供成本为第一个点的可供成本
        first_cost = group['钴矿可供成本（美元/吨）'].iloc[0]
        extended_group = pd.DataFrame({
            '累积储量（万吨）': [0] + group['累积储量（万吨）'].tolist(),
            '钴矿可供成本（美元/吨）': [first_cost] + group['钴矿可供成本（美元/吨）'].tolist()
        })
        
        # 绘制填充区域和曲线
        ax.fill_between(extended_group['累积储量（万吨）'], 
                        extended_group['钴矿可供成本（美元/吨）'], 
                        color=colors[i % len(colors)], alpha=0.5, label=region_cn)
        ax.plot(extended_group['累积储量（万吨）'], 
                extended_group['钴矿可供成本（美元/吨）'], 
                color=colors[i % len(colors)], linewidth=2)
        
        # 动态设置Y轴范围为0到该国家数据中钴矿可供成本的最大值
        max_cost = group['钴矿可供成本（美元/吨）'].max()
        ax.set_ylim(0, max_cost)
        
        # 动态调整X轴范围以适应数据
        ax.set_xlim(0, extended_group['累积储量（万吨）'].max())
        
        # 设置其他图表属性
        ax.set_xlabel('钴矿累积储量（万吨）', fontsize=12, fontweight='bold')
        ax.set_ylabel('钴矿可供成本（美元/吨）', fontsize=12, fontweight='bold')
        ax.set_title(f'{region_cn} - 钴矿累积可供曲线', fontsize=14, fontweight='bold')
        
        # 添加网格线
        ax.grid(True, linestyle='--', alpha=0.7)
        
        # 添加图例
        ax.legend(fontsize=10, title='国家/地区', loc='upper left')
        
        # 显示图表
        plt.tight_layout()  # 自动调整子图参数，使其填充整个图表区域
        plt.show()
        
        # 将数据保存到Excel中
        # 创建一个包含原始数据和累积储量的DataFrame
        result_df = group[['钴矿可供成本（美元/吨）', '钴矿储量（万吨）', '累积储量（万吨）']].copy()
        # 添加扩展的初始点
        result_df = pd.concat([pd.DataFrame([[first_cost, 0, 0]], columns=result_df.columns), result_df], ignore_index=True)
        
        # 将国家/地区名称作为列
        result_df['国家/地区'] = region_cn
        
        # 将数据写入Excel，每个国家一个工作表
        result_df.to_excel(writer, sheet_name=region_cn[:31], index=False)  # Excel工作表名称最多31个字符


# # ###4.2.2发展阶段
# # 假设这是你的Excel文件路径

# file_path = r"C:\Users\<USER>\Desktop\时间演变特征\第三个周期CAC.xlsx"
# data = pd.read_excel(file_path, sheet_name='生产')
# # 读取Excel数据
# # file_path = r"C:\Users\<USER>\Desktop\CobaltCAC.xlsx"
# # data = pd.read_excel(file_path, sheet_name='Sheet3')
# df = pd.DataFrame(data)

# # 假设df是你的数据框，包含'发展阶段'、'钴矿可供成本（美元/吨）'和'钴矿储量（万吨）'三列

# # 定义发展阶段的顺序
# development_stages = ['生产', '预生产', '激励', '可行性研究', '资源地']

# # 定义颜色映射
# colors = {
#     '生产': 'red',
#     '预生产': 'blue',
#     '激励': 'green',
#     '可行性研究': 'orange',
#     '资源地': 'purple'
# }

# # 过滤出价格在40000美元以下的数据
# filtered_df = df[df['钴矿可供成本（美元/吨）'] < 40000]

# # 按发展阶段分组，并计算每个发展阶段的累积储量和资源量
# grouped = filtered_df.groupby('发展阶段')

# # 创建ExcelWriter对象
# excel_writer = pd.ExcelWriter('累积储量数据.xlsx', engine='openpyxl')

# # 初始化全局累积储量
# global_cumulative储备 = 0

# # 按指定顺序遍历每个发展阶段的数据，绘制累积可供曲线并保存到Excel
# for i, stage in enumerate(development_stages):
#     group = grouped.get_group(stage)  # 获取当前发展阶段的数据
#     if not group.empty:
#         # 按可供价格从小到大排序
#         group = group.sort_values(by='钴矿可供成本（美元/吨）')
        
#         # 计算累积储量和资源量，从全局累积储量开始
#         if i == 0:
#             group['累积储量（万吨）'] = group['钴矿储量（万吨）'].cumsum()
#         else:
#             group['累积储量（万吨）'] = group['钴矿储量（万吨）'].cumsum() + global_cumulative储备
        
#         # 更新全局累积储量
#         global_cumulative储备 = group['累积储量（万吨）'].iloc[-1]
        
#         # 保存到Excel的不同工作表
#         group.to_excel(excel_writer, sheet_name=stage, index=False)

# # 保存并关闭Excel文件
# excel_writer.save()
# excel_writer.close()

# # 创建图表
# fig, ax = plt.subplots(figsize=(12, 8))  # 调整图表大小以增加空间感

# # 遍历每个发展阶段的数据，绘制累积可供曲线
# for i, stage in enumerate(development_stages):
#     group = grouped.get_group(stage)  # 获取当前发展阶段的数据
#     if not group.empty:
#         # 按可供价格从小到大排序
#         group = group.sort_values(by='钴矿可供成本（美元/吨）')
        
#         # 计算累积储量和资源量，从全局累积储量开始
#         if i == 0:
#             group['累积储量（万吨）'] = group['钴矿储量（万吨）'].cumsum()
#         else:
#             group['累积储量（万吨）'] = group['钴矿储量（万吨）'].cumsum() + global_cumulative储备
        
#         # 更新全局累积储量
#         global_cumulative储备 = group['累积储量（万吨）'].iloc[-1]
        
#         # 绘制填充区域和曲线
#         ax.fill_between(group['累积储量（万吨）'], 
#                         group['钴矿可供成本（美元/吨）'], 
#                         color=colors[stage], alpha=0.5, label=stage)
#         ax.plot(group['累积储量（万吨）'], 
#                 group['钴矿可供成本（美元/吨）'], 
#                 color=colors[stage], linewidth=2)

# # 设置X轴和Y轴范围
# ax.set_ylim(0, 40000)  # 确保Y轴显示范围不超过40000
# ax.set_xlim(0, global_cumulative储备 + 20)  # 动态调整X轴范围以适应数据

# # 设置其他图表属性
# ax.set_xlabel('钴矿累积储量（万吨）', fontsize=12, fontweight='bold')
# ax.set_ylabel('钴矿可供成本（美元/吨）', fontsize=12, fontweight='bold')
# ax.set_title('2060年钴矿累积可供曲线', fontsize=14, fontweight='bold')

# # 添加网格线
# ax.grid(True, linestyle='--', alpha=0.7)

# # 添加图例
# ax.legend(fontsize=10, title='发展阶段', loc='upper left')

# # 显示图表
# plt.tight_layout()  # 自动调整子图参数，使其填充整个图表区域
# plt.show()


# # 创建图形
# fig, axes = plt.subplots(nrows=1, ncols=len(development_stages), figsize=(20, 6), sharey=True)

# # 遍历每个发展阶段的数据，绘制散点图和箱线图
# for i, stage in enumerate(development_stages):
#     group = filtered_df[filtered_df['发展阶段'] == stage]  # 获取当前发展阶段的数据
#     if not group.empty:
#         # 绘制箱线图
#         sns.boxplot(x=[stage]*len(group), y=group['钴矿可供成本（美元/吨）'], ax=axes[i], palette={stage: colors[stage]}, width=0.5)
        
#         # 在箱线图上叠加散点图
#         sns.stripplot(x=[stage]*len(group), y=group['钴矿可供成本（美元/吨）'], data=group, jitter=True, color=colors[stage], size=5, ax=axes[i])
        
#         # 设置X轴和Y轴标签
#         axes[i].set_xlabel('')
#         axes[i].set_xticks([0])
#         axes[i].set_xticklabels([stage], rotation=45)
#         axes[i].set_title(stage, fontsize=12, fontweight='bold')
    
#     else:
#         # 如果没有数据，设置为空白子图
#         axes[i].set_xlabel('')
#         axes[i].set_xticks([])
#         axes[i].set_yticks([])
#         axes[i].set_title(stage, fontsize=12, fontweight='bold')

# # # 设置共享的Y轴标签
# # fig.text(0.04, 0.5, '', va='center', rotation='vertical', fontsize=12, fontweight='bold')

# # 设置主标题
# fig.suptitle('各发展阶段钴矿可供成本散点图带箱线图', fontsize=14, fontweight='bold')

# # 调整子图参数
# plt.tight_layout(rect=[0.05, 0.05, 1, 0.95])

# # 显示图表
# plt.show()



#######可供性预警（删除）

# # 假设这是你的Excel文件路径
# file_path = r"C:\Users\<USER>\Desktop\CobaltCAC.xlsx"

# # 读取Excel数据
# data = pd.read_excel(file_path, sheet_name='Sheet2')
# df = pd.DataFrame(data)

# # 定义发展阶段的顺序
# development_stages = ['生产', '预生产', '激励', '可行性研究', '资源地']

# # 定义颜色映射
# colors = {
#     '生产': 'pink',
#     '预生产': 'blue',
#     '激励': 'green',
#     '可行性研究': 'orange',
#     '资源地': 'purple'
# }

# # 过滤出价格在40000美元以下的数据
# filtered_df = df[df['钴矿可供成本（美元/吨）'] < 2000000]

# # 按发展阶段分组，并计算每个发展阶段的累积储量和资源量
# grouped = filtered_df.groupby('发展阶段')

# # 创建ExcelWriter对象
# excel_writer = pd.ExcelWriter('累积储量数据.xlsx', engine='openpyxl')

# # 初始化全局累积储量
# global_cumulative储备 = 0

# # 按指定顺序遍历每个发展阶段的数据，绘制累积可供曲线并保存到Excel
# for i, stage in enumerate(development_stages):
#     group = grouped.get_group(stage)  # 获取当前发展阶段的数据
#     if not group.empty:
#         # 按可供价格从小到大排序
#         group = group.sort_values(by='钴矿可供成本（美元/吨）')
        
#         # 计算累积储量和资源量，从全局累积储量开始
#         if i == 0:
#             group['累积储量（万吨）'] = group['钴矿储量（万吨）'].cumsum()
#         else:
#             group['累积储量（万吨）'] = group['钴矿储量（万吨）'].cumsum() + global_cumulative储备
        
#         # 更新全局累积储量
#         global_cumulative储备 = group['累积储量（万吨）'].iloc[-1]
        
#         # 保存到Excel的不同工作表
#         group.to_excel(excel_writer, sheet_name=stage, index=False)

# # 保存并关闭Excel文件
# excel_writer.save()
# excel_writer.close()

# # 创建图表
# fig, ax = plt.subplots(figsize=(12, 8))  # 调整图表大小以增加空间感

# # 遍历每个发展阶段的数据，绘制累积可供曲线
# for i, stage in enumerate(development_stages):
#     group = grouped.get_group(stage)  # 获取当前发展阶段的数据
#     if not group.empty:
#         # 按可供价格从小到大排序
#         group = group.sort_values(by='钴矿可供成本（美元/吨）')
        
#         # 计算累积储量和资源量，从全局累积储量开始
#         if i == 0:
#             group['累积储量（万吨）'] = group['钴矿储量（万吨）'].cumsum()
#         else:
#             group['累积储量（万吨）'] = group['钴矿储量（万吨）'].cumsum() + global_cumulative储备
        
#         # 更新全局累积储量
#         global_cumulative储备 = group['累积储量（万吨）'].iloc[-1]
        
#         # 绘制填充区域和曲线
#         ax.fill_between(group['累积储量（万吨）'], 
#                         group['钴矿可供成本（美元/吨）'], 
#                         color=colors[stage], alpha=0.5, label=stage)
#         ax.plot(group['累积储量（万吨）'], 
#                 group['钴矿可供成本（美元/吨）'], 
#                 color=colors[stage], linewidth=2)
        
#         # 查找与y=24755和31374的交点
#         idx_low = (np.abs(group['钴矿可供成本（美元/吨）'] - 24755 < 0)).argmin()-1
#         x_low = group['累积储量（万吨）'].iloc[idx_low]
#         print(x_low)
#         idx_high = (np.abs(group['钴矿可供成本（美元/吨）'] - 31374 < 0)).argmin()-1
#         x_high = group['累积储量（万吨）'].iloc[idx_high]
#         print(x_high)
#         # 在交点之后填充红色
#         ax.fill_between(group['累积储量（万吨）'][idx_low:], 
#                         group['钴矿可供成本（美元/吨）'][idx_low:], 
#                         color='red', alpha=0.9)
#         ax.fill_between(group['累积储量（万吨）'][idx_high:], 
#                         group['钴矿可供成本（美元/吨）'][idx_high:], 
#                         color='black', alpha=0.9)

# # 添加Y轴竖直线
# ax.axhline(y=24755, color='green', linestyle='--', alpha=0.7)
# ax.axhline(y=31374, color='red', linestyle='--', alpha=0.7)

# # 设置X轴和Y轴范围
# ax.set_ylim(0, 40000)  # 确保Y轴显示范围不超过40000
# max_x = global_cumulative储备 if not pd.isna(global_cumulative储备) else 0
# ax.set_xlim(0, max_x + 0.1)  # 动态调整X轴范围以适应数据

# # 设置其他图表属性
# ax.set_xlabel('钴矿累积储量（万吨）', fontsize=12, fontweight='bold')
# ax.set_ylabel('钴矿可供成本（美元/吨）', fontsize=12, fontweight='bold')
# ax.set_title('钴矿累积可供曲线', fontsize=14, fontweight='bold')

# # 添加网格线
# ax.grid(True, linestyle='--', alpha=0.7)

# # 添加图例
# ax.legend(fontsize=10, title='发展阶段', loc='upper left')

# # 显示图表
# plt.tight_layout()  # 自动调整子图参数，使其填充整个图表区域
# plt.show()





#######4.2.3全球区域
#####单图

# file_path = r"C:\Users\<USER>\Desktop\CobaltCAC.xlsx"

# # 读取Excel数据
# data = pd.read_excel(file_path, sheet_name='Sheet2')
# df = pd.DataFrame(data)

# # 假设df是你的数据框，包含'Global Region'、'钴矿可供成本（美元/吨）'和'钴矿储量（万吨）'三列

# # 过滤出价格在40000美元以下的数据
# filtered_df = df[df['钴矿可供成本（美元/吨）'] < 40000]

# # 按Global Region分组，并计算每个Global Region的累积储量和资源量
# grouped = filtered_df.groupby('全球区域')

# # 定义颜色映射
# colors = {
#     '亚太地区': 'red',
#     '非洲': 'green',
#     '欧洲': 'pink',
#     '美国和加拿大地区': 'purple',
#     '拉丁美洲和加勒比海地区': 'orange',
# }

# # 创建ExcelWriter对象
# excel_writer = pd.ExcelWriter('累积储量数据.xlsx', engine='openpyxl')

# # 遍历每个全球区域的数据，为每个国家绘制单独的图表并保存到Excel
# for region, group in grouped:
#     # 按可供价格从小到大排序
#     group = group.sort_values(by='钴矿可供成本（美元/吨）')
    
#     # 计算累积储量和资源量
#     group['累积储量（万吨）'] = group['钴矿储量（万吨）'].cumsum()
    
#     # 保存到Excel的不同工作表
#     group.to_excel(excel_writer, sheet_name=region, index=False)
    
#     # 创建图表
#     fig, ax = plt.subplots(figsize=(8, 6))  # 调整图表大小以增加空间感
    
#     # 绘制填充区域和曲线
#     ax.fill_between(group['累积储量（万吨）'], 
#                     group['钴矿可供成本（美元/吨）'], 
#                     color=colors[region], alpha=0.5, label=region)
#     ax.plot(group['累积储量（万吨）'], 
#             group['钴矿可供成本（美元/吨）'], 
#             color=colors[region], linewidth=2)
    
#     # 设置X轴和Y轴范围
#     ax.set_ylim(0, 40000)  # 确保Y轴显示范围不超过40000
#     ax.set_xlim(0, group['累积储量（万吨）'].max())  # 动态调整X轴范围以适应数据
    
#     # 设置其他图表属性
#     ax.set_xlabel('钴矿累积储量（万吨）', fontsize=12, fontweight='bold')
#     ax.set_ylabel('钴矿可供成本（美元/吨）', fontsize=12, fontweight='bold')
#     ax.set_title(f'钴矿累积可供曲线 - {region} (可供成本<40000美元/吨)', fontsize=14, fontweight='bold')
    
#     # 添加网格线
#     ax.grid(True, linestyle='--', alpha=0.7)
    
#     # 添加图例
#     ax.legend(fontsize=10, title='全球区域', loc='upper left')
    
#     # 显示图表
#     plt.tight_layout()  # 自动调整子图参数，使其填充整个图表区域
#     plt.show()

# # 保存并关闭Excel文件
# excel_writer.save()
# excel_writer.close()



# ######不同区域预警（删掉）
# # 假设这是你的Excel文件路径
# file_path = r"C:\Users\<USER>\Desktop\CobaltCAC.xlsx"

# # 读取Excel数据
# data = pd.read_excel(file_path, sheet_name='Sheet2')
# df = pd.DataFrame(data)

# # 过滤出价格在40000美元以下的数据
# filtered_df = df[df['钴矿可供成本（美元/吨）'] < 2000000]

# # 按Global Region分组，并计算每个Global Region的累积储量和资源量
# grouped = filtered_df.groupby('全球区域')

# # 定义颜色映射
# colors = {
#     '亚太地区': 'pink',
#     '非洲': 'green',
#     '欧洲': 'blue',
#     '美国和加拿大地区': 'purple',
#     '拉丁美洲和加勒比海地区': 'orange',
# }

# # 创建ExcelWriter对象
# excel_writer = pd.ExcelWriter('累积储量数据.xlsx', engine='openpyxl')

# # 定义Y轴的标记位置
# y_marks = [24755, 31374]

# # 遍历每个全球区域的数据，为每个区域绘制单独的图表并保存到Excel
# for region, group in grouped:
#     # 按可供价格从小到大排序
#     group = group.sort_values(by='钴矿可供成本（美元/吨）')
    
#     # 计算累积储量和资源量
#     group['累积储量（万吨）'] = group['钴矿储量（万吨）'].cumsum()
    
#     # 保存到Excel的不同工作表
#     group.to_excel(excel_writer, sheet_name=region, index=False)
    
#     # 创建图表
#     fig, ax = plt.subplots(figsize=(8, 6))  # 调整图表大小以增加空间感
    
#     # 绘制填充区域和曲线
#     ax.fill_between(group['累积储量（万吨）'], 
#                     group['钴矿可供成本（美元/吨）'], 
#                     color=colors[region], alpha=0.5, label='可供资源')
#     ax.plot(group['累积储量（万吨）'], 
#             group['钴矿可供成本（美元/吨）'], 
#             color=colors[region], linewidth=2)
    
#     # 在Y轴的指定位置添加竖直线
#     # 添加Y轴竖直线
#     ax.axhline(y=24755, color='green', linestyle='--', alpha=0.7)
#     ax.axhline(y=31374, color='red', linestyle='--', alpha=0.7)
    
#     # # 查找与y=24755和31374的交点，并在交点之后的区域填充红色
#     # for y in y_marks:
#     #     idx = (np.abs(group['钴矿可供成本（美元/吨）'] - y)).argmin()
#     #     x_intersect = group['累积储量（万吨）'].iloc[idx]
        
#     #     # 在交点之后的区域填充红色
#     #     ax.fill_between(group['累积储量（万吨）'][idx:], 
#     #                     group['钴矿可供成本（美元/吨）'][idx:], 
#     #                     color='red', alpha=0.9)
        
#     #    查找与y=24755和31374的交点
#     idx_low = (np.abs(group['钴矿可供成本（美元/吨）'] - 24755 < 0)).argmin()-1
#     x_low = group['累积储量（万吨）'].iloc[idx_low]
#     print(x_low)   
#     idx_high = (np.abs(group['钴矿可供成本（美元/吨）'] - 31374 < 0)).argmin()-1
#     x_high = group['累积储量（万吨）'].iloc[idx_high]
#     print(x_high)       
#     # 在交点之后填充红色
#     ax.fill_between(group['累积储量（万吨）'][idx_low:],
#                         group['钴矿可供成本（美元/吨）'][idx_low:],
#                         color='red', alpha=0.9, label='预警资源')
#     ax.fill_between(group['累积储量（万吨）'][idx_high:], 
#                         group['钴矿可供成本（美元/吨）'][idx_high:], 
#                         color='black', alpha=0.9, label='不可供资源')
    
#     # 设置X轴和Y轴范围
#     ax.set_ylim(0, 40000)  # 确保Y轴显示范围不超过40000
#     ax.set_xlim(0, group['累积储量（万吨）'].max())  # 动态调整X轴范围以适应数据
    
#     # 设置其他图表属性
#     ax.set_xlabel('钴矿累积储量（万吨）', fontsize=12, fontweight='bold')
#     ax.set_ylabel('钴矿可供成本（美元/吨）', fontsize=12, fontweight='bold')
#     ax.set_title(f'钴矿累积可供曲线 - {region}', fontsize=14, fontweight='bold')
    
#     # 添加网格线
#     ax.grid(True, linestyle='--', alpha=0.7)
    
#     # 添加图例
#     ax.legend(fontsize=10, loc='upper left')
    
#     # 显示图表
#     plt.tight_layout()  # 自动调整子图参数，使其填充整个图表区域
#     plt.show()

# # 保存并关闭Excel文件
# excel_writer.save()
# excel_writer.close()






######全球区域
# # 假设df是你的数据框，包含'全球区域'、'钴矿可供成本（美元/吨）'和'钴矿储量（万吨）'三列
# # 假设这是你的Excel文件路径
# file_path = r"C:\Users\<USER>\Desktop\CobaltCAC.xlsx"

# # 读取Excel数据
# data = pd.read_excel(file_path, sheet_name='Sheet3')
# df = pd.DataFrame(data)

# # 定义全球区域的顺序
# Global_Region_stages = ['亚太地区', '非洲', '欧洲', '美国和加拿大地区', '拉丁美洲和加勒比海地区']

# # 按Global Region分组，并计算每个Global Region的累积储量和资源量

# # 定义颜色映射
# colors = {
#     '亚太地区': 'red',
#     '非洲': 'green',
#     '欧洲': 'pink',
#     '美国和加拿大地区': 'purple',
#     '拉丁美洲和加勒比海地区': 'orange',
# }

# # 过滤出价格在40000美元以下的数据
# filtered_df = df[df['钴矿可供成本（美元/吨）'] < 200000]

# # 按全球区域分组，并计算每个全球区域的累积储量和资源量
# grouped = filtered_df.groupby('全球区域')

# # 创建ExcelWriter对象
# excel_writer = pd.ExcelWriter('累积储量数据.xlsx', engine='openpyxl')

# # 初始化全局累积储量
# global_cumulative储备 = 0

# # 按指定顺序遍历每个全球区域的数据，绘制累积可供曲线并保存到Excel
# for i, stage in enumerate(Global_Region_stages):
#     group = grouped.get_group(stage)  # 获取当前全球区域的数据
#     if not group.empty:
#         # 按可供价格从小到大排序
#         group = group.sort_values(by='钴矿可供成本（美元/吨）')
        
#         # 计算累积储量和资源量，从全局累积储量开始
#         if i == 0:
#             group['累积储量（万吨）'] = group['钴矿储量（万吨）'].cumsum()
#         else:
#             group['累积储量（万吨）'] = group['钴矿储量（万吨）'].cumsum() + global_cumulative储备
        
#         # 更新全局累积储量
#         global_cumulative储备 = group['累积储量（万吨）'].iloc[-1]
        
#         # 保存到Excel的不同工作表
#         group.to_excel(excel_writer, sheet_name=stage, index=False)

# # 保存并关闭Excel文件
# excel_writer.save()
# excel_writer.close()

# # 创建图表
# fig, ax = plt.subplots(figsize=(12, 8))  # 调整图表大小以增加空间感

# # 遍历每个全球区域的数据，绘制累积可供曲线
# for i, stage in enumerate(Global_Region_stages):
#     group = grouped.get_group(stage)  # 获取当前全球区域的数据
#     if not group.empty:
#         # 按可供价格从小到大排序
#         group = group.sort_values(by='钴矿可供成本（美元/吨）')
        
#         # 计算累积储量和资源量，从全局累积储量开始
#         if i == 0:
#             group['累积储量（万吨）'] = group['钴矿储量（万吨）'].cumsum()
#         else:
#             group['累积储量（万吨）'] = group['钴矿储量（万吨）'].cumsum() + global_cumulative储备
        
#         # 更新全局累积储量
#         global_cumulative储备 = group['累积储量（万吨）'].iloc[-1]
        
#         # 绘制填充区域和曲线
#         ax.fill_between(group['累积储量（万吨）'], 
#                         group['钴矿可供成本（美元/吨）'], 
#                         color=colors[stage], alpha=0.5, label=stage)
#         ax.plot(group['累积储量（万吨）'], 
#                 group['钴矿可供成本（美元/吨）'], 
#                 color=colors[stage], linewidth=2)

# # 设置X轴和Y轴范围
# ax.set_ylim(0, 4000)  # 确保Y轴显示范围不超过40000
# ax.set_xlim(0, global_cumulative储备 + 10)  # 动态调整X轴范围以适应数据

# # 设置其他图表属性
# ax.set_xlabel('钴矿累积储量（万吨）', fontsize=12, fontweight='bold')
# ax.set_ylabel('钴矿可供成本（美元/吨）', fontsize=12, fontweight='bold')
# ax.set_title('钴矿累积可供曲线', fontsize=14, fontweight='bold')

# # 添加网格线
# ax.grid(True, linestyle='--', alpha=0.7)

# # 添加图例
# ax.legend(fontsize=10, title='全球区域', loc='upper left')

# # 显示图表
# plt.tight_layout()  # 自动调整子图参数，使其填充整个图表区域
# plt.show()







