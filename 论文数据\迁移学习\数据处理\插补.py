import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split, GridSearchCV
from sklearn.ensemble import RandomForestRegressor
from sklearn.metrics import mean_squared_error

# 读取数据
file_path = r"C:\Users\<USER>\Desktop\Copper1.0.xlsx"
data = pd.read_excel(file_path, sheet_name='Sheet1')

# 分离出无缺失值的数据
known_data = data[data['In-Situ Value'].notnull()]
unknown_data = data[data['In-Situ Value'].isnull()]

# 如果已知数据为空或样本数量不足，则直接保留原始数据
if known_data.empty or len(known_data) <= 2:
    print("Warning: Not enough samples with non-missing 'In-Situ Value'. Skipping modeling.")
else:
    # 准备训练数据
    X = known_data[['Primary Reserves & Resources']]
    y = known_data['In-Situ Value']

    # 确保有足够的数据进行划分
    if len(X) > 2:
        # 调整交叉验证的折叠数以适应样本数量
        cv_folds = min(5, len(X) - 1) if len(X) > 1 else 1
        
        # 定义随机森林回归模型
        rf_model = RandomForestRegressor(random_state=42)

        # 设置参数网格
        param_grid = {
            'n_estimators': [5, 25, 50],
            'max_depth': [None, 5, 10, 15],
            'min_samples_split': [2, 10, 20],  # 调整了最小样本分裂数
            'min_samples_leaf': [1, 5, 10]     # 调整了最小叶节点样本数
        }

        # 使用网格搜索寻找最佳参数
        grid_search = GridSearchCV(estimator=rf_model, param_grid=param_grid, cv=cv_folds, n_jobs=-1, verbose=2)
        grid_search.fit(X, y)

        # 输出最佳参数
        print(f'Best parameters found: {grid_search.best_params_}')

        # 使用最佳参数重新训练模型
        best_rf_model = grid_search.best_estimator_
        best_rf_model.fit(X, y)

        # 测试模型性能
        predictions = best_rf_model.predict(X)
        mse = mean_squared_error(y, predictions)
        print(f'MSE with best parameters: {mse}')

        # 使用模型预测未知的In-Situ Value
        if not unknown_data.empty:
            unknown_predictions = best_rf_model.predict(unknown_data[['Primary Reserves & Resources']])
            unknown_data['Predicted In-Situ Value'] = unknown_predictions

        # 合并已知和未知数据
        final_data = pd.concat([known_data, unknown_data], ignore_index=True)
    else:
        final_data = data  # 如果没有足够的样本，则不进行分割和建模

# 填充原始列中的缺失值
final_data['In-Situ Value'] = final_data['In-Situ Value'].fillna(final_data.get('Predicted In-Situ Value', np.nan))

# 删除临时添加的预测列
final_data.drop(columns=['Predicted In-Situ Value'], inplace=True, errors='ignore')

# 保存最终数据到新的Excel文件
final_data.to_excel(r"C:\Users\<USER>\Desktop\Copper11_optimized_total_value.xlsx", index=False)