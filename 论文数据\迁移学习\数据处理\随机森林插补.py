import pandas as pd
from sklearn.model_selection import train_test_split, GridSearchCV
from sklearn.ensemble import RandomForestClassifier
from sklearn.preprocessing import LabelEncoder
from sklearn.metrics import accuracy_score

# 读取数据
file_path = r"C:\Users\<USER>\Desktop\Cobalt1.0.xlsx"
data = pd.read_excel(file_path, sheet_name='Sheet1')
df = pd.DataFrame(data)

# 编码分类变量
le_commodities = LabelEncoder()
data['Commodity(s)_encoded'] = le_commodities.fit_transform(data['Production Forms1'])

# 分离出无缺失值的数据
known_data = data[data['Processing Method'].notnull()]
unknown_data = data[data['Processing Method'].isnull()]

# 对目标变量进行编码
le_geology = LabelEncoder()
known_data = known_data.copy()  # 显式创建一份副本
known_data.loc[:, 'Processing Method_encoded'] = le_geology.fit_transform(known_data['Processing Method'])

# 准备训练数据
X = known_data[['Commodity(s)_encoded']]
y = known_data['Processing Method_encoded']

# 分割数据集
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.1, random_state=42)

# 定义随机森林模型
rf_model = RandomForestClassifier(random_state=42)

# 设置参数网格
param_grid = {
    'n_estimators': [5, 25, 50],
    'max_depth': [None, 5, 10, 15],
    'min_samples_split': [2, 5, 10],
    'min_samples_leaf': [1, 2, 4]
}

# 使用网格搜索寻找最佳参数
grid_search = GridSearchCV(estimator=rf_model, param_grid=param_grid, cv=10, n_jobs=-1, verbose=2)
grid_search.fit(X_train, y_train)

# 输出最佳参数
print(f'Best parameters found: {grid_search.best_params_}')

# 使用最佳参数重新训练模型
best_rf_model = grid_search.best_estimator_
best_rf_model.fit(X_train, y_train)

# 测试模型准确率
predictions = best_rf_model.predict(X_test)
print(f'Accuracy with best parameters: {accuracy_score(y_test, predictions)}')

# 使用模型预测未知的地质类型
unknown_predictions = best_rf_model.predict(unknown_data[['Commodity(s)_encoded']])
unknown_data['Processing Method_encoded'] = unknown_predictions

# 将预测结果反编码回原始类别
unknown_data['Predicted Processing Method'] = le_geology.inverse_transform(unknown_predictions.astype(int))

# 合并数据
final_data = pd.concat([known_data, unknown_data], ignore_index=True)

# 保存最终数据到新的Excel文件
final_data.to_excel(r"C:\Users\<USER>\Desktop\Copper11_optimized.xlsx", index=False)



