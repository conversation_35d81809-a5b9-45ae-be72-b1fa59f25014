import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split, cross_val_score
import xgboost as xgb
from sklearn.ensemble import RandomForestRegressor
from sklearn.metrics import mean_squared_error, r2_score
from sklearn.neighbors import NearestNeighbors
import matplotlib.pyplot as plt

def preprocess_small_dataset(df_small):
    """预处理小数据集"""
    # 分离特征
    categorical_features = df_small.columns[1:18].tolist()  # B-R列
    numerical_features = df_small.columns[18:31].tolist()   # S-AF列
    
    # 分离有标签和无标签数据
    mask_labeled = df_small['Total Cost'].notna()
    
    # 准备训练数据（有标签的数据）
    X_train = df_small[mask_labeled][categorical_features + numerical_features]
    y_train = df_small[mask_labeled]['Total Cost']
    
    # 准备预测数据（缺失值数据）
    X_predict = df_small[~mask_labeled][categorical_features + numerical_features]
    
    # 标准化数值特征
    scaler = StandardScaler()
    X_train[numerical_features] = scaler.fit_transform(X_train[numerical_features])
    X_predict[numerical_features] = scaler.transform(X_predict[numerical_features])
    
    print(f"训练集大小: {X_train.shape}")
    print(f"预测集大小: {X_predict.shape}")
    
    return X_train, y_train, X_predict, mask_labeled, scaler

class KNNRegressor:
    """KNN回归器（符合sklearn API）"""
    def __init__(self, n_neighbors=5):
        self.n_neighbors = n_neighbors
        self.nn = NearestNeighbors(n_neighbors=n_neighbors)
        self.y_train = None
    
    def fit(self, X, y):
        self.nn.fit(X)
        self.y_train = y
        return self
    
    def predict(self, X):
        distances, indices = self.nn.kneighbors(X)
        predictions = []
        for i in range(len(X)):
            weights = 1 / (distances[i] + 1e-6)
            weights = weights / weights.sum()
            weighted_pred = np.average(self.y_train.values[indices[i]], weights=weights)
            predictions.append(weighted_pred)
        return np.array(predictions)
    
    def get_params(self, deep=True):
        """获取模型参数（sklearn API要求）"""
        return {'n_neighbors': self.n_neighbors}
    
    def set_params(self, **parameters):
        """设置模型参数（sklearn API要求）"""
        for parameter, value in parameters.items():
            setattr(self, parameter, value)
        return self

def train_models(X_train, y_train):
    """训练多个模型"""
    models = {}
    
    # 1. XGBoost
    xgb_model = xgb.XGBRegressor(
        n_estimators=200,
        learning_rate=0.01,
        max_depth=4,
        min_child_weight=3,
        subsample=0.8,
        colsample_bytree=0.8,
        random_state=42
    )
    models['XGBoost'] = xgb_model
    
    # 2. 随机森林
    rf_model = RandomForestRegressor(
        n_estimators=200,
        max_depth=6,
        min_samples_split=5,
        random_state=42
    )
    models['RandomForest'] = rf_model
    
    # 3. KNN回归
    knn_model = KNNRegressor(n_neighbors=5)
    models['KNN'] = knn_model
    
    # 训练和评估所有模型
    results = {}
    for name, model in models.items():
        print(f"\n训练 {name} 模型...")
        model.fit(X_train, y_train)
        
        # 使用5折交叉验证评估模型
        scores = cross_val_score(model, X_train, y_train, cv=5, 
                               scoring='neg_mean_squared_error')
        rmse_scores = np.sqrt(-scores)
        results[name] = rmse_scores.mean()
        print(f"{name} 交叉验证 RMSE: {rmse_scores.mean():.2f} (+/- {rmse_scores.std() * 2:.2f})")
    
    # 选择最佳模型
    best_model_name = min(results, key=results.get)
    print(f"\n最佳模型: {best_model_name} (RMSE: {results[best_model_name]:.2f})")
    
    return models, best_model_name

def evaluate_predictions(y_true, predictions_dict):
    """评估不同模型的预测效果"""
    print("\n" + "="*50)
    print("模型性能对比")
    print("="*50)
    
    results = {}
    for name, y_pred in predictions_dict.items():
        mse = mean_squared_error(y_true, y_pred)
        rmse = np.sqrt(mse)
        r2 = r2_score(y_true, y_pred)
        mae = np.mean(np.abs(y_true - y_pred))
        mape = np.mean(np.abs((y_true - y_pred) / y_true)) * 100
        
        results[name] = {
            'RMSE': rmse,
            'R2': r2,
            'MAE': mae,
            'MAPE': mape
        }
        
        print(f"\n{name} 模型:")
        print(f"RMSE: {rmse:.2f}")
        print(f"R2 Score: {r2:.4f}")
        print(f"MAE: {mae:.2f}")
        print(f"MAPE: {mape:.2f}%")
    
    return results

def plot_prediction_comparison(y_true, predictions_dict):
    """绘制预测值vs实际值的对比散点图"""
    plt.figure(figsize=(12, 8))
    
    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei']
    plt.rcParams['axes.unicode_minus'] = False
    
    # 绘制理想预测线
    min_val = y_true.min()
    max_val = y_true.max()
    plt.plot([min_val, max_val], [min_val, max_val], 
             'k--', label='理想预测', alpha=0.5)
    
    # 设置不同模型的颜色和标记
    model_settings = {
        'RandomForest': {'color': '#FF9999', 'marker': 'o', 'label': '随机森林'},
        'XGBoost': {'color': '#66B2FF', 'marker': 's', 'label': 'XGBoost'},
        'KNN': {'color': '#99FF99', 'marker': 'D', 'label': '实例迁移'}
    }
    
    # 绘制每个模型的预测散点
    for model_name, y_pred in predictions_dict.items():
        if model_name in model_settings:
            settings = model_settings[model_name]
            plt.scatter(y_true, 
                       y_pred,
                       alpha=0.6,
                       s=60,
                       label=settings['label'],
                       color=settings['color'],
                       marker=settings['marker'])
    
    plt.title('各模型预测效果比较', fontsize=14)
    plt.xlabel('实际值', fontsize=12)
    plt.ylabel('预测值', fontsize=12)
    plt.grid(True, linestyle='--', alpha=0.3)
    plt.legend(fontsize=10)
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图片
    plt.savefig('prediction_comparison.png', 
                dpi=300, 
                bbox_inches='tight',
                facecolor='white')
    plt.close()

def main():
    # 1. 读取数据
    print("读取数据...")
    df_small = pd.read_excel('small_dataset.xlsx')
    
    # 2. 预处理数据
    print("\n预处理数据...")
    X_train, y_train, X_predict, mask_labeled, scaler = preprocess_small_dataset(df_small)
    
    # 3. 训练和评估多个模型
    print("\n训练模型...")
    models, best_model_name = train_models(X_train, y_train)
    
    # 4. 使用所有模型进行预测
    predictions = {}
    for name, model in models.items():
        pred = model.predict(X_predict)
        predictions[name] = pred
        
        print(f"\n{name} 预测统计:")
        print(f"预测值数量: {len(pred)}")
        print(f"预测值范围: {pred.min():.2f} - {pred.max():.2f}")
        print(f"预测值平均值: {pred.mean():.2f}")
        print(f"预测值中位数: {np.median(pred):.2f}")
    
    # 5. 保存预测结果
    results = pd.DataFrame({
        'Property ID': df_small[~mask_labeled]['Property ID']
    })
    
    for name, pred in predictions.items():
        results[f'{name}_Prediction'] = pred
    
    # 添加最佳模型标记
    results['Best_Model'] = best_model_name
    results['Best_Prediction'] = results[f'{best_model_name}_Prediction']
    
    # 保存结果
    results.to_excel('small_dataset_predictions.xlsx', index=False)
    print("\n预测结果已保存到 small_dataset_predictions.xlsx")
    
    # 分割验证集用于绘图
    X_val, X_test, y_val, y_test = train_test_split(
        X_train, y_train, test_size=0.2, random_state=42
    )
    
    # 获取验证集上的预测结果
    val_predictions = {}
    for name, model in models.items():
        val_predictions[name] = model.predict(X_val)
    
    # 绘制预测对比图
    plot_prediction_comparison(y_val, val_predictions)

if __name__ == "__main__":
    main() 