import pandas as pd

file_path = r"C:\Users\<USER>\Desktop\Copper2319.xlsx"
# file_path = r"C:\Users\<USER>\Desktop\Cobalt351.xlsx"
data = pd.read_excel(file_path, sheet_name='Sheet2')
df = pd.DataFrame(data)

# # 输出每行的缺失值数量
# missing_values_per_row = df.isnull().sum(axis=1)
# print(missing_values_per_row)

# output_path = r"C:\Users\<USER>\Desktop\missing_values_per_row.xlsx"  # 定义输出文件的路径和名称
# missing_values_per_row.to_excel(output_path, index=True)  # 将结果写入Excel文件

# print(f"缺失值数量已保存到 '{output_path}'")

# # 计算每列的缺失值数量
# missing_values_count = len(df)-df.isnull().sum()

# # 计算每列的缺失比例
# # missing_values_percentage = missing_values_count / len(df)

# # 输出未排序的缺失比例
# # print(missing_values_percentage)

# # # 将缺失比例转换为百分比形式
# # missing_values_percentage = missing_values_percentage * 100

# # 将缺失比例和对应的列名合并为一个DataFrame
# missing_values_df = pd.DataFrame({
#     'Variable': df.columns,
#     'Missing Percentage': missing_values_count
# })

# # 将结果写入Excel文件
# output_path_percentage = r"C:\Users\<USER>\Desktop\missing_values_percentage.xlsx"
# missing_values_df.to_excel(output_path_percentage, index=False)

# print(f"缺失比例已保存到 '{output_path_percentage}'")

import pandas as pd
import seaborn as sns
import matplotlib.pyplot as plt
import missingno as msno

# 设置 Matplotlib 的全局字体为支持中文的字体
plt.rcParams['font.sans-serif'] = ['SimHei']  # 使用黑体
plt.rcParams['axes.unicode_minus'] = False  # 正常显示负号

# 读取数据
file_path = r"C:\Users\<USER>\Desktop\Copper2319.xlsx"
# file_path = r"C:\Users\<USER>\Desktop\Cobalt351.xlsx"
data = pd.read_excel(file_path, sheet_name='Sheet2')

# 使用Seaborn绘制热力图
plt.figure(figsize=(10, 8))
sns.heatmap(data.isnull(), cbar=False, cmap='gray', yticklabels=False)
plt.title('铜矿')
plt.show()


