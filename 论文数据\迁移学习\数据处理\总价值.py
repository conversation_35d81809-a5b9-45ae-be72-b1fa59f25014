import pandas as pd
import numpy as np
from sklearn.linear_model import LinearRegression
from sklearn.ensemble import RandomForestRegressor
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_squared_error
import warnings

# 忽略所有警告（可选，用于忽略拟合过程中可能出现的警告）
warnings.filterwarnings('ignore')

# 读取数据
# file_path = r"C:\Users\<USER>\Desktop\Cobalt1.0.xlsx"
file_path = r"C:\Users\<USER>\Desktop\Copper1.0.xlsx"
data = pd.read_excel(file_path, sheet_name='Sheet1')

# 检查是否有缺失值
print("Initial missing values in Production Capacity:", data['Production Capacity'].isnull().sum())

# 确保所有的数值都是正数，否则对数转换会失败
data['Production Capacity'] = data['Production Capacity'].replace(0, np.nan)
data['Mill Capacity1'] = data['Mill Capacity1'].replace(0, np.nan)

# 分离出非空数据用于训练模型
known_data = data[['Production Capacity', 'Mill Capacity1']].dropna()

# 计算两个变量的相关性
correlation = known_data.corr()
print("Correlation between Production Capacity and Mill Capacity1:")
print(correlation)

# 准备训练数据
X_known = known_data[['Mill Capacity1']]
y_known = known_data['Production Capacity']

# 划分训练集和测试集（可选）
X_train, X_val, y_train, y_val = train_test_split(X_known, y_known, test_size=0.2, random_state=42)

# 定义回归模型（这里选择随机森林回归模型）
rf_model = RandomForestRegressor(n_estimators=100, random_state=42)

# 训练模型
rf_model.fit(X_train, y_train)

# 在验证集上评估模型性能（可选）
predictions_val = rf_model.predict(X_val)
mse = mean_squared_error(y_val, predictions_val)
print(f"Validation MSE: {mse}")

# 使用模型预测未知的Production Capacity
unknown_data = data[data['Production Capacity'].isnull()]
if not unknown_data.empty:
    X_unknown = unknown_data[['Mill Capacity1']]
    predicted_values = rf_model.predict(X_unknown)

    # 将预测结果放回原数据框
    data.loc[data['Production Capacity'].isnull(), 'Production Capacity'] = predicted_values

# 再次检查是否有缺失值
print("\nRemaining missing values in Production Capacity:", data['Production Capacity'].isnull().sum())

# 检查填补后的数据范围
print("\nFilled Production Capacity summary statistics:")
print(data['Production Capacity'].describe())

# 保存更新后的数据到新的Excel文件
output_file_path = r"C:\Users\<USER>\Desktop\Copper1_filled_Grade.xlsx"
data.to_excel(output_file_path, index=False)

print(f"\nFilled data saved to {output_file_path}")

