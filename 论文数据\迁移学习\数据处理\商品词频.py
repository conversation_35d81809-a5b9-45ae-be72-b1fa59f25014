# import pandas as pd
# from collections import Counter

# # 假设 df 是你的 DataFrame，它已经包含了 'Cluster' 和 'Ore Minerals' 列
# file_path = r"C:\Users\<USER>\Desktop\Copper2.0.xlsx"
# data = pd.read_excel(file_path, sheet_name='Sheet1')  # 导入原始数据
# data = data[data['Ore Minerals'].notnull()]
# df = pd.DataFrame(data)

# # 定义一个函数来处理 Commodity(s) 字符串并返回一个列表
# def split_commodities(commodities_str):
#     if isinstance(commodities_str, str):
#         # 清洗并分割字符串，去除多余空格
#         return [commodity.strip() for commodity in commodities_str.split(',')]
#     else:
#         return []

# # 应用上述函数到整个 DataFrame 并创建一个新的列保存结果
# df['Commodities_list'] = df['Ore Minerals'].apply(split_commodities)

# # 创建一个字典用于存储每个 Cluster 的商品词频DataFrame
# cluster_commodities_freq_dfs = {}

# # 遍历每个不同的 Cluster
# for cluster_id in df['Ore Minerals Cluster'].unique():
#     # 获取属于当前 Cluster 的所有商品列表
#     commodities_in_cluster = df[df['Ore Minerals Cluster'] == cluster_id]['Commodities_list'].explode().dropna()
    
#     # 计算词频
#     freq = Counter(commodities_in_cluster)
    
#     # 将词频转换为 DataFrame
#     freq_df = pd.DataFrame(list(freq.items()), columns=['Ore Minerals', f'Frequency{int(cluster_id)}'])
#     freq_df = freq_df.sort_values(by=f'Frequency{int(cluster_id)}', ascending=False).reset_index(drop=True)
    
#     # 保存到字典中
#     cluster_commodities_freq_dfs[int(cluster_id)] = freq_df

# # 合并所有集群的词频表到一个 DataFrame 中
# merged_df = None
# for cluster_id in sorted(cluster_commodities_freq_dfs.keys()):
#     freq_df = cluster_commodities_freq_dfs[cluster_id].copy()
#     if merged_df is None:
#         merged_df = freq_df.rename(columns={'Ore Minerals': f'Ore Minerals{cluster_id}'})
#     else:
#         freq_df_renamed = freq_df.rename(columns={
#             'Ore Minerals': f'Ore Minerals{cluster_id}',
#             f'Frequency{cluster_id}': f'Frequency{cluster_id}'
#         })
#         merged_df = pd.merge(merged_df, freq_df_renamed, left_index=True, right_index=True, how='outer')

# # 创建一个 ExcelWriter 对象，使用 openpyxl 引擎
# excel_file_path = r"C:\Users\<USER>\Desktop\Cluster_Production_Forms_Frequency.xlsx"
# with pd.ExcelWriter(excel_file_path, engine='openpyxl') as writer:
#     # 将所有的词频表写入同一个 Sheet
#     merged_df.to_excel(writer, sheet_name='All_Clusters_Frequencies', index=False)

# print(f"Word frequency results have been saved to {excel_file_path}")




import pandas as pd
from collections import Counter

# 假设 df 是你的 DataFrame，它已经包含了 'Cluster' 和 'Ore Minerals' 列
file_path = r"C:\Users\<USER>\Desktop\Copper2.0.xlsx"
data = pd.read_excel(file_path, sheet_name='Sheet1')  # 导入原始数据
data = data[data['Commodities'].notnull()]
df = pd.DataFrame(data)

# 定义一个函数来处理 Commodity(s) 字符串并返回一个列表，但只保留前四个
def split_commodities(commodities_str):
    if isinstance(commodities_str, str):
        # 清洗并分割字符串，去除多余空格，并限制为前四个
        commodities_list = [commodity.strip() for commodity in commodities_str.split(',')]
        return commodities_list[:4]  # 只保留前四个
    else:
        return []

# 应用上述函数到整个 DataFrame 并创建一个新的列保存结果
df['Commodities_list'] = df['Commodities'].apply(split_commodities)

# 创建一个字典用于存储每个 Cluster 的商品词频DataFrame
cluster_commodities_freq_dfs = {}

# 遍历每个不同的 Cluster
for cluster_id in df['Commodities Cluster'].unique():
    # 获取属于当前 Cluster 的所有商品列表
    commodities_in_cluster = df[df['Commodities Cluster'] == cluster_id]['Commodities_list'].explode().dropna()
    
    # 计算词频
    freq = Counter(commodities_in_cluster)
    
    # 将词频转换为 DataFrame
    freq_df = pd.DataFrame(list(freq.items()), columns=['Commodities', f'Frequency{int(cluster_id)}'])
    freq_df = freq_df.sort_values(by=f'Frequency{int(cluster_id)}', ascending=False).reset_index(drop=True)
    
    # 保存到字典中
    cluster_commodities_freq_dfs[int(cluster_id)] = freq_df

# 合并所有集群的词频表到一个 DataFrame 中
merged_df = None
for cluster_id in sorted(cluster_commodities_freq_dfs.keys()):
    freq_df = cluster_commodities_freq_dfs[cluster_id].copy()
    if merged_df is None:
        merged_df = freq_df.rename(columns={'Commodities': f'Commodities{cluster_id}'})
    else:
        freq_df_renamed = freq_df.rename(columns={
            'Commodities': f'Commodities{cluster_id}',
            f'Frequency{cluster_id}': f'Frequency{cluster_id}'
        })
        merged_df = pd.merge(merged_df, freq_df_renamed, left_index=True, right_index=True, how='outer')

# 创建一个 ExcelWriter 对象，使用 openpyxl 引擎
excel_file_path = r"C:\Users\<USER>\Desktop\Cluster_Production_Forms_Frequency.xlsx"
with pd.ExcelWriter(excel_file_path, engine='openpyxl') as writer:
    # 将所有的词频表写入同一个 Sheet
    merged_df.to_excel(writer, sheet_name='All_Clusters_Frequencies', index=False)

print(f"Word frequency results have been saved to {excel_file_path}")


