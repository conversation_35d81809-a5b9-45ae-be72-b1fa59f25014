import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.ensemble import RandomForestRegressor

file_path = r"C:\Users\<USER>\Desktop\Cobalt5.2.xlsx"
# file_path = r"C:\Users\<USER>\Desktop\Copper 5.2.xlsx"
data = pd.read_excel(file_path,sheet_name='Sheet1') # 导入原始数据
df = pd.DataFrame(data)

# 查看原始数据
print("原始数据:")
print(df)

# 分离数值型和分类型数据
numerical_cols = [
    'MillHead Grade - percent',
    'Mill Capacity - tonnes/day',
    'Stripping Ratio',
    'In.Situ.Value.Reserves.Resources',
    'Total In-Situ Value ($M)',
    'Initial Capital Cost',
    'Mining & Processing Costs per tonne',
    'Life of Mine Sustaining Cost',
    'Total Cost'
]

categorical_cols = ['Mining Method', 'Geologic Ore Body Type']

# 编码分类变量
df_encoded = pd.get_dummies(df, columns=categorical_cols, drop_first=True)

# 初始化一个字典来存储填充后的列
filled_data = {}

# 对每个数值型列进行处理
for col in numerical_cols:
    # 分离特征和目标变量
    X = df_encoded.drop(columns=[col])
    y = df_encoded[col]
    
    # 分割数据集为训练集和测试集
    train_df = df_encoded[df_encoded[col].notnull()]
    test_df = df_encoded[df_encoded[col].isnull()]
    
    if not test_df.empty:
        X_train = train_df.drop(columns=[col]).values
        y_train = train_df[col].values
        
        # 训练随机森林回归器
        rf_regressor = RandomForestRegressor(random_state=42)
        rf_regressor.fit(X_train, y_train)
        
        # 准备测试数据
        X_test = test_df.drop(columns=[col]).values
        
        # 预测缺失值
        predicted_values = rf_regressor.predict(X_test)
        
        # 填补缺失值
        filled_data[col] = predicted_values
    else:
        filled_data[col] = df_encoded[col].values

# 将填补后的数据合并回原DataFrame
for col in numerical_cols:
    df.loc[df[col].isnull(), col] = filled_data[col]

# 查看处理后的数据
print("\n处理后的数据:")
print(df)

# 绘制数据分布图
plt.figure(figsize=(18, 12))

for i, col in enumerate(numerical_cols, 1):
    plt.subplot(3, 3, i)
    sns.kdeplot(df[col], label='Original', shade=True)
    sns.kdeplot(filled_data[col], label='Filled', shade=True)
    plt.title(f'Distribution of {col}')
    plt.legend()

plt.tight_layout()
plt.show()

# 将填充后的数据保存到Excel文件

df.to_excel(r"C:\Users\<USER>\Desktop\Copper11.xlsx", index=False)




