import matplotlib.pyplot as plt
import seaborn as sns
import pandas as pd
import numpy as np

# 设置 Matplotlib 的全局字体为支持中文的字体
plt.rcParams['font.sans-serif'] = ['SimHei']  # 使用黑体
plt.rcParams['axes.unicode_minus'] = False  # 正常显示负号


# # file_path = r"C:\Users\<USER>\Desktop\时间演变特征\第三个周期CAC.xlsx"
# # data = pd.read_excel(file_path, sheet_name='生产')
# # 读取Excel数据
# file_path = r"C:\Users\<USER>\Desktop\CobaltCAC.xlsx"
# data = pd.read_excel(file_path, sheet_name='Sheet3')
# df = pd.DataFrame(data)

# # 假设df是你的数据框，包含'发展阶段'、'钴矿可供成本（美元/吨）'和'钴矿储量（万吨）'三列

# # 定义发展阶段的顺序
# development_stages = ['生产', '预生产', '激励', '可行性研究', '资源地']

# # 定义颜色映射
# colors = {
#     '生产': 'red',
#     '预生产': 'blue',
#     '激励': 'green',
#     '可行性研究': 'orange',
#     '资源地': 'purple'
# }

# # 过滤出价格在40000美元以下的数据
# filtered_df = df[df['钴矿可供成本（美元/吨）'] < 40000]

# # 按发展阶段分组，并计算每个发展阶段的累积储量和资源量
# grouped = filtered_df.groupby('发展阶段')

# # 创建ExcelWriter对象
# excel_writer = pd.ExcelWriter('累积储量数据.xlsx', engine='openpyxl')

# # 初始化全局累积储量
# global_cumulative储备 = 0

# # 按指定顺序遍历每个发展阶段的数据，绘制累积可供曲线并保存到Excel
# for i, stage in enumerate(development_stages):
#     group = grouped.get_group(stage)  # 获取当前发展阶段的数据
#     if not group.empty:
#         # 按可供价格从小到大排序
#         group = group.sort_values(by='钴矿可供成本（美元/吨）')
        
#         # 计算累积储量和资源量，从全局累积储量开始
#         if i == 0:
#             group['累积储量（万吨）'] = group['钴矿储量（万吨）'].cumsum()
#         else:
#             group['累积储量（万吨）'] = group['钴矿储量（万吨）'].cumsum() + global_cumulative储备
        
#         # 更新全局累积储量
#         global_cumulative储备 = group['累积储量（万吨）'].iloc[-1]
        
#         # 保存到Excel的不同工作表
#         group.to_excel(excel_writer, sheet_name=stage, index=False)

# # 保存并关闭Excel文件
# excel_writer.save()
# excel_writer.close()

# # 创建图表
# fig, ax = plt.subplots(figsize=(12, 8))  # 调整图表大小以增加空间感

# # 遍历每个发展阶段的数据，绘制累积可供曲线
# for i, stage in enumerate(development_stages):
#     group = grouped.get_group(stage)  # 获取当前发展阶段的数据
#     if not group.empty:
#         # 按可供价格从小到大排序
#         group = group.sort_values(by='钴矿可供成本（美元/吨）')
        
#         # 计算累积储量和资源量，从全局累积储量开始
#         if i == 0:
#             group['累积储量（万吨）'] = group['钴矿储量（万吨）'].cumsum()
#         else:
#             group['累积储量（万吨）'] = group['钴矿储量（万吨）'].cumsum() + global_cumulative储备
        
#         # 更新全局累积储量
#         global_cumulative储备 = group['累积储量（万吨）'].iloc[-1]
        
#         # 绘制填充区域和曲线
#         ax.fill_between(group['累积储量（万吨）'], 
#                         group['钴矿可供成本（美元/吨）'], 
#                         color=colors[stage], alpha=0.5, label=stage)
#         ax.plot(group['累积储量（万吨）'], 
#                 group['钴矿可供成本（美元/吨）'], 
#                 color=colors[stage], linewidth=2)

# # 设置X轴和Y轴范围
# ax.set_ylim(0, 40000)  # 确保Y轴显示范围不超过40000
# ax.set_xlim(0, global_cumulative储备 + 20)  # 动态调整X轴范围以适应数据

# # 设置其他图表属性
# ax.set_xlabel('钴矿累积储量（万吨）', fontsize=12, fontweight='bold')
# ax.set_ylabel('钴矿可供成本（美元/吨）', fontsize=12, fontweight='bold')
# ax.set_title('2060年钴矿累积可供曲线', fontsize=14, fontweight='bold')

# # 添加网格线
# ax.grid(True, linestyle='--', alpha=0.7)

# # 添加图例
# ax.legend(fontsize=10, title='发展阶段', loc='upper left')

# # 显示图表
# plt.tight_layout()  # 自动调整子图参数，使其填充整个图表区域
# plt.show()



# # # 读取Excel数据
# # # file_path = r"C:\Users\<USER>\Desktop\CobaltCAC.xlsx"
# # # data = pd.read_excel(file_path, sheet_name='Sheet3')
# # file_path = r"C:\Users\<USER>\Desktop\时间演变特征\第三个周期CAC.xlsx"
# # data = pd.read_excel(file_path, sheet_name='生产')
# # df = pd.DataFrame(data)

# # # 假设df是你的数据框，包含'发展阶段'、'钴矿可供成本（美元/吨）'和'钴矿储量（万吨）'三列

# # # 定义颜色映射
# # colors = {
# #     '生产': 'red',
# #     '预生产': 'blue',
# #     '激励': 'green',
# #     '可行性研究': 'orange',
# #     '资源地': 'purple'
# # }

# # # 过滤出价格在40000美元以下且储量小于40万吨的数据
# # filtered_df = df[(df['钴矿可供成本（美元/吨）'] < 40000)]

# g = sns.jointplot(data=filtered_df, x="钴矿储量（万吨）", y="钴矿可供成本（美元/吨）", hue="发展阶段", palette=colors)
# # 设置图片大小



# # 设置坐标轴标签
# g.ax_joint.set_xlabel('钴矿储量（万吨）', fontsize=12)
# g.ax_joint.set_ylabel('钴矿可供成本（美元/吨）', fontsize=12)

# # 获取当前图例
# handles, labels = g.ax_joint.get_legend_handles_labels()

# # 按照颜色映射的顺序重新排列图例
# order = ['生产', '预生产', '激励', '可行性研究', '资源地']
# new_handles = [handles[labels.index(stage)] for stage in order]
# new_labels = order

# # 设置新的图例
# g.ax_joint.legend(new_handles, new_labels, title='发展阶段', loc='upper right')

# # 调整布局并显示图表
# plt.tight_layout()
# plt.show()




#####全球区域
# 假设df是你的数据框，包含'全球区域'、'钴矿可供成本（美元/吨）'和'钴矿储量（万吨）'三列
# 假设这是你的Excel文件路径
# 读取Excel数据
# file_path = r"C:\Users\<USER>\Desktop\CobaltCAC.xlsx"
# data = pd.read_excel(file_path, sheet_name='Sheet3')
file_path = r"C:\Users\<USER>\Desktop\时间演变特征\第二个周期CAC.xlsx"
data = pd.read_excel(file_path, sheet_name='生产')
df = pd.DataFrame(data)

# 定义全球区域的顺序
Global_Region_stages = ['亚太地区', '非洲', '欧洲', '美国和加拿大地区', '拉丁美洲和加勒比海地区']

# 按Global Region分组，并计算每个Global Region的累积储量和资源量

# 定义颜色映射
colors = {
    '亚太地区': 'red',
    '非洲': 'green',
    '欧洲': 'pink',
    '美国和加拿大地区': 'purple',
    '拉丁美洲和加勒比海地区': 'orange',
}

# 过滤出价格在40000美元以下的数据
filtered_df = df[df['钴矿可供成本（美元/吨）'] < 40000]

# 按全球区域分组，并计算每个全球区域的累积储量和资源量
grouped = filtered_df.groupby('全球区域')

# 创建ExcelWriter对象
excel_writer = pd.ExcelWriter('累积储量数据.xlsx', engine='openpyxl')

# 初始化全局累积储量
global_cumulative储备 = 0

# 按指定顺序遍历每个全球区域的数据，绘制累积可供曲线并保存到Excel
for i, stage in enumerate(Global_Region_stages):
    group = grouped.get_group(stage)  # 获取当前全球区域的数据
    if not group.empty:
        # 按可供价格从小到大排序
        group = group.sort_values(by='钴矿可供成本（美元/吨）')
        
        # 计算累积储量和资源量，从全局累积储量开始
        if i == 0:
            group['累积储量（万吨）'] = group['钴矿储量（万吨）'].cumsum()
        else:
            group['累积储量（万吨）'] = group['钴矿储量（万吨）'].cumsum() + global_cumulative储备
        
        # 更新全局累积储量
        global_cumulative储备 = group['累积储量（万吨）'].iloc[-1]
        
        # 保存到Excel的不同工作表
        group.to_excel(excel_writer, sheet_name=stage, index=False)

# 保存并关闭Excel文件
excel_writer.save()
excel_writer.close()

# 创建图表
fig, ax = plt.subplots(figsize=(12, 8))  # 调整图表大小以增加空间感

# 遍历每个全球区域的数据，绘制累积可供曲线
for i, stage in enumerate(Global_Region_stages):
    group = grouped.get_group(stage)  # 获取当前全球区域的数据
    if not group.empty:
        # 按可供价格从小到大排序
        group = group.sort_values(by='钴矿可供成本（美元/吨）')
        
        # 计算累积储量和资源量，从全局累积储量开始
        if i == 0:
            group['累积储量（万吨）'] = group['钴矿储量（万吨）'].cumsum()
        else:
            group['累积储量（万吨）'] = group['钴矿储量（万吨）'].cumsum() + global_cumulative储备
        
        # 更新全局累积储量
        global_cumulative储备 = group['累积储量（万吨）'].iloc[-1]
        
        # 绘制填充区域和曲线
        ax.fill_between(group['累积储量（万吨）'], 
                        group['钴矿可供成本（美元/吨）'], 
                        color=colors[stage], alpha=0.5, label=stage)
        ax.plot(group['累积储量（万吨）'], 
                group['钴矿可供成本（美元/吨）'], 
                color=colors[stage], linewidth=2)

# 设置X轴和Y轴范围
ax.set_ylim(0, 40000)  # 确保Y轴显示范围不超过40000
ax.set_xlim(0, global_cumulative储备 + 10)  # 动态调整X轴范围以适应数据

# 设置其他图表属性
ax.set_xlabel('钴矿累积储量（万吨）', fontsize=12, fontweight='bold')
ax.set_ylabel('钴矿可供成本（美元/吨）', fontsize=12, fontweight='bold')
ax.set_title('2060年钴矿累积可供曲线', fontsize=14, fontweight='bold')

# 添加网格线
ax.grid(True, linestyle='--', alpha=0.7)

# 添加图例
ax.legend(fontsize=10, title='全球区域', loc='upper left')

# 显示图表
plt.tight_layout()  # 自动调整子图参数，使其填充整个图表区域
plt.show()





# # 读取Excel数据
# # file_path = r"C:\Users\<USER>\Desktop\CobaltCAC.xlsx"
# # data = pd.read_excel(file_path, sheet_name='Sheet3')
# file_path = r"C:\Users\<USER>\Desktop\时间演变特征\第一个周期CAC.xlsx"
# data = pd.read_excel(file_path, sheet_name='生产')
# df = pd.DataFrame(data)

# 假设df是你的数据框，包含'发展阶段'、'钴矿可供成本（美元/吨）'和'钴矿储量（万吨）'三列


# 定义颜色映射
colors = {
    '亚太地区': 'red',
    '非洲': 'green',
    '欧洲': 'pink',
    '美国和加拿大地区': 'purple',
    '拉丁美洲和加勒比海地区': 'orange',
}


# 过滤出价格在40000美元以下且储量小于40万吨的数据
filtered_df = df[(df['钴矿可供成本（美元/吨）'] < 40000)]

g = sns.jointplot(data=filtered_df, x="钴矿储量（万吨）", y="钴矿可供成本（美元/吨）", hue="全球区域", palette=colors)
# 设置图片大小



# 设置坐标轴标签
g.ax_joint.set_xlabel('钴矿储量（万吨）', fontsize=12)
g.ax_joint.set_ylabel('钴矿可供成本（美元/吨）', fontsize=12)

# 获取当前图例
handles, labels = g.ax_joint.get_legend_handles_labels()

# 按照颜色映射的顺序重新排列图例
order = ['亚太地区', '非洲', '欧洲', '美国和加拿大地区', '拉丁美洲和加勒比海地区']
new_handles = [handles[labels.index(stage)] for stage in order]
new_labels = order

# 设置新的图例
g.ax_joint.legend(new_handles, new_labels, title='发展阶段', loc='upper right')

# 调整布局并显示图表
plt.tight_layout()
plt.show()

