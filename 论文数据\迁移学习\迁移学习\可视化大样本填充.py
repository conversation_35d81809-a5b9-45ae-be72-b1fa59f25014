import pandas as pd
import numpy as np
import xgboost as xgb
from sklearn.model_selection import cross_val_score
from sklearn.ensemble import RandomForestRegressor
from sklearn.preprocessing import StandardScaler
import matplotlib.pyplot as plt
import seaborn as sns

# 设置可视化样式
sns.set_theme(style="whitegrid")
plt.rcParams['figure.figsize'] = (12, 8)
plt.rcParams['font.sans-serif'] = ['SimHei']  # 使用黑体
plt.rcParams['axes.unicode_minus'] = False  # 正常显示负号


def engineer_features(df):
    """增强特征工程"""
    df = df.copy()

    # 成本相关特征
    if all(col in df.columns for col in ['初始投资成本', '矿山维护成本']):
        df['成本结构'] = df['初始投资成本'] / df['矿山维护成本']
        df['开采能力'] = df['初始投资成本'] * df['磨机产能']

    # 生产效率特征
    if all(col in df.columns for col in ['磨机产能', '矿产产量']):
        df['生产效率'] = df['矿产产量'] / df['磨机产能']

    # 矿石品位和回收率
    if all(col in df.columns for col in ['磨机原矿品位', '回收率']):
        df['回收能力'] = df['磨机原矿品位'] * df['回收率']

    # 经济效益指标
    if '矿产价值' in df.columns:
        df['经济价值'] = df['矿产价值'] / df['磨机产能']

    return df


def train_group_model(group_data, features):
    """改进的分组模型训练"""
    mask = group_data['总成本'].notna()
    X_train = group_data[mask][features]
    y_train = group_data[mask]['总成本']

    # 使用随机森林作为基础模型
    model = RandomForestRegressor(
        n_estimators=200,
        max_depth=4,
        min_samples_split=5,
        min_samples_leaf=3,
        random_state=42
    )

    if len(X_train) >= 5:
        cv_scores = cross_val_score(model, X_train, y_train, cv=min(5, len(X_train)))
        print(f"组内交叉验证分数: {cv_scores.mean():.4f} (+/- {cv_scores.std() * 2:.4f})")

    model.fit(X_train, y_train)

    # 可视化特征重要性
    plt.figure()
    feature_importances = pd.Series(model.feature_importances_, index=features)
    feature_importances = feature_importances.sort_values(ascending=True)  # 按重要性升序排列
    feature_importances.nlargest(10).plot(kind='barh')
    plt.title(
        f"特征重要性 - 组内模型 (能力: {group_data['供应能力分组'].iloc[0]}, 成本: {group_data['成本分组'].iloc[0]})")
    plt.xlabel('重要性得分')
    plt.ylabel('特征')
    plt.show()

    return model


def fill_large_dataset():
    """改进的大数据集填充策略"""
    print("读取数据...")
    df_large = pd.read_excel('large_dataset.xlsx')

    # 1. 特征工程
    print("\n进行特征工程...")
    df_enhanced = engineer_features(df_large)

    # # 可视化特征工程结果
    # plt.figure()
    # sns.pairplot(
    #     df_enhanced[['总成本'] + [col for col in df_enhanced.columns if '结构' in col or '效率' in col]])
    # plt.suptitle('特征工程后的相关性分析')
    # plt.show()

    # 2. 准备特征
    numerical_features = [
        '初始投资成本', '采矿与加工单位成本', '矿山维护成本',
        '磨机产能', '剥离率', '磨机原矿品位', '回收率',
        '矿产产量', '预计矿山寿命', '矿产价值'
    ]

    # 添加工程特征
    engineered_features = [
        '成本结构', '开采能力', '生产效率',
        '回收能力', '经济价值'
    ]

    features = [col for col in numerical_features + engineered_features
                if col in df_enhanced.columns]

    # 计算特征与总成本之间的相关性
    correlation_matrix = df_enhanced[numerical_features + ['总成本']].corr()

    # 可视化相关性矩阵
    plt.figure()
    sns.heatmap(correlation_matrix, annot=True, cmap='coolwarm', fmt=".2f")
    plt.title('特征与总成本之间的相关性矩阵')
    plt.show()

    # 3. 数据分组（基于多个特征）
    print("\n数据分组...")
    df_enhanced['供应能力分组'] = pd.qcut(df_enhanced['矿产产量'].rank(method='first'), q=2)
    df_enhanced['成本分组'] = pd.qcut(df_enhanced['矿山维护成本'].rank(method='first'), q=3)

    # 可视化分组结果
    plt.figure()
    sns.scatterplot(data=df_enhanced, x='矿山维护成本', y='矿产产量', hue='供应能力分组', style='成本分组')
    plt.title('数据分组分布')
    plt.show()

    # 4. 标准化特征
    scaler = StandardScaler()
    df_enhanced[features] = scaler.fit_transform(df_enhanced[features])

    # 5. 对每个组合训练模型
    group_models = {}
    group_stats = {}

    for size_group in df_enhanced['供应能力分组'].unique():
        for cost_group in df_enhanced['成本分组'].unique():
            mask = (df_enhanced['供应能力分组'] == size_group) & \
                   (df_enhanced['成本分组'] == cost_group)
            group_data = df_enhanced[mask]

            if len(group_data) > 0:
                print(f"\n处理组 (Size: {size_group}, Cost: {cost_group})")
                group_key = (size_group, cost_group)

                # 计算组统计信息
                group_stats[group_key] = {
                    'mean': group_data['总成本'].mean(),
                    'std': group_data['总成本'].std()
                }

                # 训练模型
                group_models[group_key] = train_group_model(group_data, features)

    # 6. 填充预测
    missing_mask = df_large['总成本'].isna()
    for idx in df_large[missing_mask].index:
        size_group = df_enhanced.loc[idx, '供应能力分组']
        cost_group = df_enhanced.loc[idx, '成本分组']
        group_key = (size_group, cost_group)

        if group_key in group_models:
            X_predict = df_enhanced.loc[[idx], features]
            prediction = group_models[group_key].predict(X_predict)[0]

            # 验证预测值
            stats = group_stats[group_key]
            prediction = np.clip(
                prediction,
                stats['mean'] - 2 * stats['std'],
                stats['mean'] + 2 * stats['std']
            )

            df_large.loc[idx, '总成本'] = prediction

    # 可视化填充结果
    plt.figure()
    sns.histplot(data=df_large[~missing_mask], x='总成本', kde=True, color='blue', label='原始数据')
    sns.histplot(data=df_large[missing_mask], x='总成本', kde=True, color='orange', label='填充数据')
    plt.title('总成本分布对比')
    plt.ylabel('数量')
    plt.legend()
    plt.show()

    # 7. 保存结果
    df_large.to_excel('large_dataset_filled2.xlsx', index=False)
    print("\n填充完成，结果已保存到 large_dataset_filled.xlsx")


if __name__ == "__main__":
    fill_large_dataset()