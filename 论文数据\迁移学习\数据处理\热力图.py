import pandas as pd
import seaborn as sns
import matplotlib.pyplot as plt
from dython import nominal

# 设置 Matplotlib 的全局字体为支持中文的字体
plt.rcParams['font.sans-serif'] = ['SimHei']  # 使用黑体
plt.rcParams['axes.unicode_minus'] = False  # 正常显示负号

# 读取数据
# file_path = r"C:\Users\<USER>\Desktop\Cobalt351.xlsx"
file_path = r"C:\Users\<USER>\Desktop\Copper2319.xlsx"
data = pd.read_excel(file_path, sheet_name='Sheet2')

# 选择需要分析的列
# columns_of_interest = ['Commodity(s)', 'Geologic Ore Body Type', 'Ore Minerals', 'Production Forms', 'Mining Method', 'Processing Method']
# columns_of_interest = ['Primary Reserves and Resources', 'Reserves & Resources: Ore Tonge', 
#                        'Grade.Reserves.Resources', 'Contained.Reserves.Resources', 
#                        'In.Situ.Value.Reserves.Resources', 'Total In-Situ Value ($M)']
# columns_of_interest = ['Ore Minerals','Mining Method','Primary Reserves and Resources','MillHead Grade - percent','Commodity Production - tonne','Mill Capacity - tonnes/day',
#                        'Stripping Ratio']
# columns_of_interest = ['Development Stage','Ore Minerals','Mining Method','Primary Reserves and Resources',
#                        'Commodity Production - tonne','Estimated Mine Life (Years)',
#                        'Initial Capital Cost','Mining & Processing Costs per tonne','Life of Mine Sustaining Cost']

# columns_of_interest = [ 'Commodities', 'Mine Type', 'Geologic Ore Body Type', 
#                        'Ore Minerals', 'Processing Method','Production Forms']
# columns_of_interest = ['Grade Reserves & Resources','Primary Reserves & Resources',
#                        'In-Situ Value','Total Value','Mill Capacity',
#                        'Estimated Mine Life','Stripping Ratio','MillHead Grade','Recovery Rate','Production Capacity',
#                        'Initial Capital Cost','Mining & Processing Costs','Mine Sustaining Cost']

# columns_of_interest = [ 'Commodities Cluster', 'Mine Type','Geologic Ore Body Type Cluster', 
#                        'Ore Minerals Cluster', 'Processing Method','Production Forms Cluster']


# columns_of_interest = ['矿产资源','矿山类型','地质矿床类型','矿石类型','选矿方法','生产形式']

columns_of_interest = ['品位','储量',
                       '矿产价值','全部矿产价值','磨机产能',
                       '预计矿山寿命','剥离率','磨机原矿品位','回收率','矿产产量',
                       '初始投资成本','矿山维护成本','采矿与加工单位成本']

df_selected = data[columns_of_interest]

# 计算关联矩阵（使用Cramer's V 或 Theil's U）
associations_matrix = nominal.associations(df_selected, nominal_columns='all', figsize=(10, 8))

# 绘制热力图
plt.figure(figsize=(10, 8))
sns.heatmap(associations_matrix['corr'], annot=True, cmap='OrRd', fmt='.2f', square=True, cbar_kws={"shrink": .5})
plt.title('铜矿')
plt.show()



# #####插补前后分布图
# import pandas as pd
# from sklearn.preprocessing import LabelEncoder

# # 读取数据
# file_path_original = r"C:\Users\<USER>\Desktop\Cobalt351.xlsx"
# file_path_new = r"C:\Users\<USER>\Desktop\Cobalt2.0.xlsx"

# original_data = pd.read_excel(file_path_original, sheet_name='Sheet2')
# new_data = pd.read_excel(file_path_new, sheet_name='Sheet3')

# columns_of_interest = ['地质矿床类型', '矿石类型', '选矿方法', '生产形式']

# # 初始化一个字典来存储LabelEncoders
# encoders = {}

# # 对每个感兴趣的列进行编码，跳过缺失值
# for column in columns_of_interest:
#     # 合并原始数据和新数据列以确保所有可能的类别都被包含
#     combined_series = pd.concat([original_data[column].dropna(), new_data[column].dropna()])
    
#     # 初始化LabelEncoder并拟合合并的数据
#     le = LabelEncoder()
#     le.fit(combined_series)
#     encoders[column] = le
    
#     # 创建临时列用于存储编码结果，初始值为NaN
#     original_data[f'{column}_encoded'] = pd.NA
#     new_data[f'{column}_encoded'] = pd.NA
    
#     # 找到非缺失值的索引
#     original_not_na = ~original_data[column].isna()
#     new_not_na = ~new_data[column].isna()
    
#     # 对非缺失值进行编码
#     original_data.loc[original_not_na, f'{column}_encoded'] = le.transform(original_data.loc[original_not_na, column])
#     new_data.loc[new_not_na, f'{column}_encoded'] = le.transform(new_data.loc[new_not_na, column])

# # 保存包含编码后数据的新数据集到新的Excel文件中
# output_file_path_original = r"C:\Users\<USER>\Desktop\Copper2319_encoded.xlsx"
# output_file_path_new = r"C:\Users\<USER>\Desktop\Copper2.0_encoded.xlsx"

# original_data.to_excel(output_file_path_original, index=False, sheet_name='Sheet2')
# new_data.to_excel(output_file_path_new, index=False, sheet_name='Sheet3')

# # 可视化编码后的数据密度分布对比（可选）
# import matplotlib.pyplot as plt
# import seaborn as sns

# plt.rcParams['font.sans-serif'] = ['SimHei']  # 使用黑体
# plt.rcParams['axes.unicode_minus'] = False  # 正常显示负号

# for column in columns_of_interest:
#     encoded_column = f'{column}_encoded'
    
#     # 创建图形
#     plt.figure(figsize=(6, 6))
    
#     # 绘制密度分布图
#     sns.kdeplot(original_data[encoded_column].dropna(), shade=True, color="blue", label="原始数据")
#     sns.kdeplot(new_data[encoded_column].dropna(), shade=True, color="red", label="插值后数据")

#     plt.xlabel(f'{column}')  # X轴标签设置为中文“编码”
#     plt.ylabel('密度')  # Y轴标签设置为中文“密度”
#     plt.legend()
#     plt.tight_layout()  # 自动调整子图参数,使之填充整个图像区域
#     plt.show()

# import pandas as pd
# import matplotlib.pyplot as plt
# import seaborn as sns

# # 设置 Matplotlib 的全局字体为支持中文的字体
# plt.rcParams['font.sans-serif'] = ['SimHei']  # 使用黑体
# plt.rcParams['axes.unicode_minus'] = False  # 正常显示负号

# # 读取数据
# file_path = r"C:\Users\<USER>\Desktop\Cobalt351.xlsx"
# original_data = pd.read_excel(file_path, sheet_name='Sheet2')
# file_path1 = r"C:\Users\<USER>\Desktop\Cobalt2.0.xlsx"
# data = pd.read_excel(file_path1, sheet_name='Sheet3')

# columns_of_interest = ['磨机产能',
#                        '预计矿山寿命','剥离率','磨机原矿品位','回收率','矿产产量',
#                        '初始投资成本','矿山维护成本','采矿与加工单位成本']

# # 对于每个感兴趣的列，创建并显示一个独立的密度图
# for column in columns_of_interest:
#     plt.figure(figsize=(6, 6))  # 调整图形大小
    
#     # 绘制原始数据和新数据的密度图
#     sns.kdeplot(original_data[column], fill=True, label='原数据')
#     sns.kdeplot(data[column], fill=True, color="red", label='插值后数据')

#     plt.ylabel('密度')  # Y轴标签设置为中文“密度”
#     plt.legend()
    
#     # 显示图像
#     plt.show()