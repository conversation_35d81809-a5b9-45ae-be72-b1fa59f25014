import pandas as pd
import numpy as np
import xgboost as xgb
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.metrics import mean_squared_error, r2_score
from sklearn.model_selection import cross_val_score
from sklearn.preprocessing import StandardScaler

def train_base_model(X_train, y_train):
    """训练基础模型，用于迁移学习的知识提取"""
    # 1. XGBoost - 更快的训练速度，更好的特征重要性
    model = xgb.XGBRegressor(
        n_estimators=500,        # 增加树的数量以获取更多知识
        learning_rate=0.005,     # 更小的学习率以获得更稳定的特征
        max_depth=4,             # 控制树的深度避免过拟合
        min_child_weight=5,      # 增加正则化
        subsample=0.7,           # 减少样本使用以增加多样性
        colsample_bytree=0.7,    # 特征采样
        gamma=0.2,               # 增加分裂阈值
        reg_alpha=0.5,           # L1正则化
        reg_lambda=2,            # L2正则化
        random_state=42,
        early_stopping=True,     # 使用早停
        eval_metric='rmse'
    )
    
    # 训练并获取特征重要性
    model.fit(
        X_train, 
        y_train,
        eval_set=[(X_train, y_train)],
        verbose=False
    )
    
    # 获取特征重要性
    feature_names = [f'feature_{i}' for i in range(X_train.shape[1])]
    importance = pd.DataFrame({
        'feature': feature_names,
        'importance': model.feature_importances_
    })
    importance = importance.sort_values('importance', ascending=False)
    
    print("\n特征重要性:")
    print(importance)
    
    return model, importance

def train_ensemble_model(X_train, y_train, base_predictions):
    """训练集成模型，结合基础模型的知识"""
    # 将基础模型的预测作为新特征
    X_enhanced = np.hstack([X_train, base_predictions.reshape(-1, 1)])
    
    # 使用梯度提升作为集成模型
    ensemble = GradientBoostingRegressor(
        n_estimators=200,
        learning_rate=0.01,
        max_depth=3,
        min_samples_split=5,
        min_samples_leaf=3,
        subsample=0.8,
        random_state=42
    )
    
    # 训练集成模型
    ensemble.fit(X_enhanced, y_train)
    return ensemble

def evaluate_models(models, X_val, y_val, base_model=None):
    """评估模型性能"""
    results = {}
    
    for name, model in models.items():
        if name == 'Ensemble':
            # 为集成模型准备特征
            base_preds = base_model.predict(X_val).reshape(-1, 1)
            X_val_enhanced = np.hstack([X_val, base_preds])
            y_pred = model.predict(X_val_enhanced)
        else:
            y_pred = model.predict(X_val)
        
        mse = mean_squared_error(y_val, y_pred)
        rmse = np.sqrt(mse)
        r2 = r2_score(y_val, y_pred)
        
        results[name] = {
            'RMSE': rmse,
            'R2': r2
        }
        
        print(f"\n{name} 模型评估:")
        print(f"RMSE: {rmse:.2f}")
        print(f"R2: {r2:.4f}")
    
    return results

def main():
    """主函数，包含数据预处理和模型训练"""
    print("读取数据...")
    df = pd.read_excel('small_dataset.xlsx')
    
    # 准备特征
    numerical_features = [
        'Initial Capital Cost', 'Mining & Processing Costs', 'Mine Sustaining Cost',
        'Mill Capacity', 'Stripping Ratio', 'MillHead Grade', 'Recovery Rate',
        'Production Capacity', 'Estimated Mine Life'
    ]
    
    # 只使用有标签的数据
    mask = df['Total Cost'].notna()
    df_train = df[mask].copy()
    
    # 处理缺失值
    for col in numerical_features:
        if df_train[col].isna().any():
            print(f"处理 {col} 的缺失值...")
            df_train[col] = df_train[col].fillna(df_train[col].median())
    
    # 处理无穷大值
    df_train = df_train.replace([np.inf, -np.inf], np.nan)
    for col in numerical_features:
        if df_train[col].isna().any():
            print(f"处理 {col} 的异常值...")
            df_train[col] = df_train[col].fillna(df_train[col].median())
    
    # 标准化数值特征
    print("\n标准化特征...")
    scaler = StandardScaler()
    X = scaler.fit_transform(df_train[numerical_features])
    y = df_train['Total Cost'].values
    
    # 打印数据信息
    print(f"\n训练数据大小: {X.shape}")
    print(f"特征数量: {len(numerical_features)}")
    print(f"样本数量: {len(y)}")
    
    # 检查数据
    print("\n数据检查:")
    print(f"X中的NaN数量: {np.isnan(X).sum()}")
    print(f"y中的NaN数量: {np.isnan(y).sum()}")
    print(f"X中的无穷大值数量: {np.isinf(X).sum()}")
    print(f"y中的无穷大值数量: {np.isinf(y).sum()}")
    
    # 训练基础模型
    print("\n训练基础模型...")
    base_model, feature_importance = train_base_model(X, y)
    
    # 获取基础模型预测
    base_predictions = base_model.predict(X)
    
    # 训练集成模型
    print("\n训练集成模型...")
    ensemble_model = train_ensemble_model(X, y, base_predictions)
    
    # 评估模型
    models = {
        'Base': base_model,
        'Ensemble': ensemble_model
    }
    
    results = evaluate_models(models, X, y, base_model)
    
    # 保存特征重要性
    feature_importance.to_excel('feature_importance.xlsx', index=False)
    print("\n特征重要性已保存到 feature_importance.xlsx")
    
    return base_model, ensemble_model, feature_importance

if __name__ == "__main__":
    base_model, ensemble_model, importance = main() 