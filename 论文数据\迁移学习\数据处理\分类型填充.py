import pandas as pd
from sklearn.ensemble import RandomForestClassifier
from sklearn.model_selection import train_test_split
from sklearn.impute import SimpleImputer

file_path = r"C:\Users\<USER>\Desktop\Cobalt5.2.xlsx"
# file_path = r"C:\Users\<USER>\Desktop\Copper 5.2.xlsx"
data = pd.read_excel(file_path,sheet_name='Sheet1') # 导入原始数据
df = pd.DataFrame(data)



# 查看原始数据
print("原始数据:")
print(df)

# 将分类变量转换为数值编码
df_encoded = pd.get_dummies(df, columns=['Geologic Ore Body Type'], drop_first=True)

# 分离特征和目标变量
X = df_encoded.drop(columns=['Ore Minerals'])
y = df_encoded['Ore Minerals']

# 使用SimpleImputer处理缺失值，这里先用一个占位符填充以便后续分割训练集和测试集
imputer = SimpleImputer(strategy='constant', fill_value=-999)
X_imputed = imputer.fit_transform(X)

# 合并特征和目标变量
df_imputed = pd.concat([pd.DataFrame(X_imputed, columns=X.columns), y], axis=1)

# 分割数据集为训练集和测试集
train_df = df_imputed[df_imputed['Ore Minerals'].notnull()]
test_df = df_imputed[df_imputed['Ore Minerals'].isnull()]

# 准备训练数据
X_train = train_df.drop(columns=['Ore Minerals']).values
y_train = train_df['Ore Minerals'].values

# 训练随机森林分类器
rf_classifier = RandomForestClassifier(random_state=42)
rf_classifier.fit(X_train, y_train)

# 准备测试数据
X_test = test_df.drop(columns=['Ore Minerals']).values

# 预测缺失值
predicted_values = rf_classifier.predict(X_test)

# 填补缺失值
df.loc[test_df.index, 'Ore Minerals'] = predicted_values

# 查看处理后的数据
print("\n处理后的数据:")
print(df)

df.to_excel(r"C:\Users\<USER>\Desktop\Copper11.xlsx")

