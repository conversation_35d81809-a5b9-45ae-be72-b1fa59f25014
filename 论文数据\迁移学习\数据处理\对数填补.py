import pandas as pd
import numpy as np
from scipy.stats import lognorm
import warnings

# 忽略所有警告（可选，用于忽略拟合过程中可能出现的警告）
warnings.filterwarnings('ignore')

# 读取数据
file_path = r"C:\Users\<USER>\Desktop\Copper1.0.xlsx"
data = pd.read_excel(file_path, sheet_name='Sheet1')

# 检查是否有缺失值
print("Initial missing values in Initial Capital Cost:", data['Initial Capital Cost'].isnull().sum())

# 确保所有的数值都是正数，否则对数转换会失败
data['Initial Capital Cost'] = data['Initial Capital Cost'].replace(0, np.nan)
data['Initial Capital Cost1'] = data['Initial Capital Cost'].replace(0, np.nan)
# 定义一个函数来进行多次抽样并取平均值
def sample_and_average(shape, loc, scale, num_samples=1000):
    samples = lognorm.rvs(shape, loc=loc, scale=scale, size=num_samples)
    return np.mean(samples)

# 对每个地质类型分别处理
for geologic_type, group in data.groupby('Geologic Ore Body Type Cluster'):
    # 提取非空的储量资源量
    non_null_values = group['Initial Capital Cost'].dropna()
    
    if not non_null_values.empty:
        # 拟合对数正态分布
        shape, loc, scale = lognorm.fit(non_null_values, floc=0)
        
        # 计算需要填补的数量
        num_missing = group['Initial Capital Cost'].isnull().sum()
        
        # 如果有缺失值，则从拟合的分布中抽样填补
        if num_missing > 0:
            # 方法1：从拟合的对数正态分布中抽样并取平均值
            sampled_values = [sample_and_average(shape, loc, scale) for _ in range(num_missing)]
            
            # 方法2：直接从原始数据的非空值中进行重采样（Bootstrap方法）
            # sampled_values = np.random.choice(non_null_values, size=num_missing, replace=True)
            
            # 更新原始DataFrame中的缺失值
            missing_indices = group[group['Initial Capital Cost'].isnull()].index
            data.loc[missing_indices, 'Initial Capital Cost'] = sampled_values

# 再次检查是否有缺失值
print("\nRemaining missing values in Initial Capital Cost:", data['Initial Capital Cost'].isnull().sum())

# 检查填补后的数据范围
print("\nFilled Initial Capital Cost summary statistics:")
print(data['Initial Capital Cost'].describe())

# 保存更新后的数据到新的Excel文件
output_file_path = r"C:\Users\<USER>\Desktop\Copper1_filled.xlsx"
data.to_excel(output_file_path, index=False)

print(f"\nFilled data saved to {output_file_path}")

# 可视化填补前后分布对比（可选）
import seaborn as sns
import matplotlib.pyplot as plt

plt.figure(figsize=(12, 6))

# 填补前的分布
sns.kdeplot(data[data['Initial Capital Cost1'].notnull()]['Initial Capital Cost'], label='Before Imputation')

# 填补后的分布
sns.kdeplot(data['Initial Capital Cost'], label='After Imputation')

plt.title('Distribution Comparison Before and After Imputation')
plt.xlabel('Initial Capital Cost')
plt.legend()
plt.show()

