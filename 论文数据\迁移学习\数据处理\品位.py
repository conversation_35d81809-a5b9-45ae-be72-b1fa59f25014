import pandas as pd
from sklearn.model_selection import train_test_split, GridSearchCV
from sklearn.ensemble import RandomForestRegressor
from sklearn.metrics import mean_squared_error

# 读取数据
file_path = r"C:\Users\<USER>\Desktop\Cobalt1.0.xlsx"
data = pd.read_excel(file_path, sheet_name='Sheet1')

# 创建一个空的DataFrame来存储最终结果
final_data = pd.DataFrame()

# 根据Mine Type分组
for mine_type, group in data.groupby('Mine Type'):
    # 分离出无缺失值的数据
    known_group = group[group['Estimated Mine Life'].notnull()]
    unknown_group = group[group['Estimated Mine Life'].isnull()]

    # 如果有足够的样本，则进行分割和建模
    if not known_group.empty and len(known_group) > 2:  # 确保有足够的数据进行划分
        X = known_group[['Initial Capital Cost']]
        y = known_group['Estimated Mine Life']

        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.1, random_state=42)

        # 定义随机森林回归模型
        rf_model = RandomForestRegressor(random_state=42)

        # 设置参数网格
        param_grid = {
            'n_estimators': [5, 25, 50],
            'max_depth': [None, 5, 10, 15],
            'min_samples_split': [2, 10, 20],  # 调整了最小样本分裂数
            'min_samples_leaf': [1, 5, 10]     # 调整了最小叶节点样本数
        }

        # 使用网格搜索寻找最佳参数
        grid_search = GridSearchCV(estimator=rf_model, param_grid=param_grid, cv=5, n_jobs=-1, verbose=2)
        grid_search.fit(X_train, y_train)

        # 输出最佳参数
        print(f'Mine Type {mine_type}: Best parameters found: {grid_search.best_params_}')

        # 使用最佳参数重新训练模型
        best_rf_model = grid_search.best_estimator_
        best_rf_model.fit(X_train, y_train)

        # 测试模型性能
        predictions = best_rf_model.predict(X_test)
        mse = mean_squared_error(y_test, predictions)
        print(f'Mine Type {mine_type}: MSE with best parameters: {mse}')

        # 使用模型预测未知的Estimated Mine Life
        if not unknown_group.empty:
            unknown_predictions = best_rf_model.predict(unknown_group[['Initial Capital Cost']])
            unknown_group['Predicted Estimated Mine Life'] = unknown_predictions

        # 合并已知和未知数据
        combined_group = pd.concat([known_group, unknown_group], ignore_index=True)
    else:
        combined_group = group  # 如果没有足够的样本，则不进行分割和建模

    final_data = pd.concat([final_data, combined_group], ignore_index=True)

# 保存最终数据到新的Excel文件
output_file_path = r"C:\Users\<USER>\Desktop\Copper11_optimized_by_mine_type.xlsx"
final_data.to_excel(output_file_path, index=False)

print("Processing completed and results saved.")