import pandas as pd
from sklearn.model_selection import train_test_split, GridSearchCV
from sklearn.ensemble import RandomForestClassifier
from sklearn.preprocessing import LabelEncoder
from sklearn.metrics import accuracy_score

# 读取数据
file_path = r"C:\Users\<USER>\Desktop\Copper1.0.xlsx"
data = pd.read_excel(file_path, sheet_name='Sheet1')

# 编码分类变量
le_commodities = LabelEncoder()
le_geology = LabelEncoder()

# 创建一个空的DataFrame来存储最终结果
final_data = pd.DataFrame()

# 根据Mine Type分组
for mine_type, group in data.groupby('Mine Type'):
    # 对每组内的Production Forms1进行编码
    if 'Production Forms1' in group.columns:
        group['Production Forms1_encoded'] = le_commodities.fit_transform(group['Production Forms1'])
    
    # 分离出无缺失值的数据
    known_group = group[group['Processing Method'].notnull()]
    unknown_group = group[group['Processing Method'].isnull()]

    # 对目标变量进行编码
    if not known_group.empty:
        known_group = known_group.copy()  # 显式创建一份副本
        known_group.loc[:, 'Processing Method_encoded'] = le_geology.fit_transform(known_group['Processing Method'])

        # 准备训练数据
        X = known_group[['Production Forms1_encoded']]
        y = known_group['Processing Method_encoded']

        # 如果有足够的样本，则进行分割和建模
        if len(X) > 2:  # 确保有足够的数据进行划分
            X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.1, random_state=42)

            # 定义随机森林模型
            rf_model = RandomForestClassifier(random_state=42)

            # 设置参数网格
            param_grid = {
                'n_estimators': [5, 25, 50],
                'max_depth': [None, 5, 10, 15],
                'min_samples_split': [2, 10, 20],  # 调整了最小样本分裂数
                'min_samples_leaf': [1, 5, 10]     # 调整了最小叶节点样本数
            }

            # 使用网格搜索寻找最佳参数
            grid_search = GridSearchCV(estimator=rf_model, param_grid=param_grid, cv=5, n_jobs=-1, verbose=2)
            grid_search.fit(X_train, y_train)

            # 输出最佳参数
            print(f'Mine Type {mine_type}: Best parameters found: {grid_search.best_params_}')

            # 使用最佳参数重新训练模型
            best_rf_model = grid_search.best_estimator_
            best_rf_model.fit(X_train, y_train)

            # 测试模型准确率
            predictions = best_rf_model.predict(X_test)
            print(f'Mine Type {mine_type}: Accuracy with best parameters: {accuracy_score(y_test, predictions)}')

            # 使用模型预测未知的地质类型
            if not unknown_group.empty:
                unknown_predictions = best_rf_model.predict(unknown_group[['Production Forms1_encoded']])
                unknown_group['Processing Method_encoded'] = unknown_predictions
                unknown_group['Predicted Processing Method'] = le_geology.inverse_transform(unknown_predictions.astype(int))

            # 合并已知和未知数据
            combined_group = pd.concat([known_group, unknown_group], ignore_index=True)
        else:
            combined_group = group  # 如果没有足够的样本，则不进行分割和建模

        final_data = pd.concat([final_data, combined_group], ignore_index=True)

# 保存最终数据到新的Excel文件
final_data.to_excel(r"C:\Users\<USER>\Desktop\Copper11_optimized_by_mine_type.xlsx", index=False)