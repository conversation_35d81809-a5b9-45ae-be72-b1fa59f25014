# import pandas as pd
# from sklearn.feature_extraction.text import TfidfVectorizer
# from sklearn.cluster import KMeans
# from sklearn.decomposition import PCA
# from sklearn.metrics import silhouette_score
# import matplotlib.pyplot as plt

# # 设置 Matplotlib 的全局字体为支持中文的字体
# plt.rcParams['font.sans-serif'] = ['SimHei']  # 使用黑体
# plt.rcParams['axes.unicode_minus'] = False  # 正常显示负号

# # 假设df是你的DataFrame，并且'Ore Minerals_truncated'是你想要聚类的列
# file_path = r"C:\Users\<USER>\Desktop\Copper2.0.xlsx"
# # file_path = r"C:\Users\<USER>\Desktop\Cobalt2.0.xlsx"
# data = pd.read_excel(file_path, sheet_name='Sheet3')  # 导入原始数据
# df = pd.DataFrame(data)

# # 文本预处理函数
# def preprocess_text(text):
#     if isinstance(text, str):
#         text = text.lower().replace(',', ' ').strip()
#         return text
#     else:
#         return ''

# # 应用预处理
# df['Commodity(s)_cleaned'] = df['选矿方法'].apply(preprocess_text)

# # 向量化
# vectorizer = TfidfVectorizer(stop_words='english')
# X = vectorizer.fit_transform(df['Commodity(s)_cleaned'])

# # 确定最佳聚类数
# best_k = 0
# best_score = -1
# silhouette_scores = []

# # 测试不同的k值，从2开始，因为至少需要两个簇来计算轮廓系数
# for k in range(2, 40):  # 尝试2到10个簇
#     kmeans = KMeans(n_clusters=k, random_state=42)
#     cluster_labels = kmeans.fit_predict(X)
#     score = silhouette_score(X, cluster_labels)
#     silhouette_scores.append(score)
    
#     print(f"Number of Clusters: {k}, Silhouette Score: {score}")
    
#     if score > best_score:
#         best_score = score
#         best_k = k

# print(f"Optimal Number of Clusters based on Silhouette Score: {best_k}")

# # 使用最佳k值进行最终聚类
# optimal_kmeans = KMeans(n_clusters=best_k, random_state=42)
# cluster_labels = optimal_kmeans.fit_predict(X)

# # 将聚类结果添加到原始DataFrame
# df['选矿方法 Cluster'] = cluster_labels

# # 可选：降维以帮助可视化
# pca = PCA(n_components=2)
# reduced_features = pca.fit_transform(X.toarray())

# # 添加降维后的坐标到DataFrame
# df[['PCA1', 'PCA2']] = reduced_features

# # 评估聚类效果
# silhouette_avg = silhouette_score(X, cluster_labels)
# print(f'Silhouette Score for Optimal k: {silhouette_avg}')

# # 查看每个簇的代表性元素
# for i in range(best_k):
#     print(f'\nCluster {i}:')
#     print(df[df['选矿方法 Cluster'] == i]['Commodity(s)_cleaned'].value_counts().head())

# # 保存聚类结果到新的Excel文件
# # df.to_excel(r"C:\Users\<USER>\Desktop\选矿方法_Clustered.xlsx", index=False)

# # 绘制轮廓系数图
# plt.plot(range(2, 40), silhouette_scores, 'bo-')
# plt.xlabel('类数量')
# plt.ylabel('轮廓系数')
# plt.title('铜矿：选矿方法')
# plt.show()


import pandas as pd
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.cluster import KMeans
from sklearn.decomposition import PCA
from sklearn.metrics import silhouette_score
import matplotlib.pyplot as plt

# 设置 Matplotlib 的全局字体为支持中文的字体
plt.rcParams['font.sans-serif'] = ['SimHei']  # 使用黑体
plt.rcParams['axes.unicode_minus'] = False  # 正常显示负号

# 假设df是你的DataFrame，并且'Ore Minerals_truncated'是你想要聚类的列
file_path = r"C:\Users\<USER>\Desktop\Copper2.0.xlsx"
# file_path = r"C:\Users\<USER>\Desktop\Cobalt2.0.xlsx"
data = pd.read_excel(file_path, sheet_name='Sheet3')  # 导入原始数据
df = pd.DataFrame(data)

# 文本预处理函数：清洗并分割字符串，仅保留前四个元素
def preprocess_text(text):
    if isinstance(text, str):
        # 清洗字符串：转换为小写，去除逗号并分割成列表
        commodities_list = [commodity.strip() for commodity in text.lower().replace(',', ' ').split()]
        # 仅保留前四个元素
        commodities_list = commodities_list[:4]
        # 将列表重新组合成字符串
        return ' '.join(commodities_list)
    else:
        return ''

# 应用预处理
df['Commodity(s)_cleaned'] = df['Commodities'].apply(preprocess_text)

# 向量化
vectorizer = TfidfVectorizer(stop_words='english')
X = vectorizer.fit_transform(df['Commodity(s)_cleaned'])

# 确定最佳聚类数
best_k = 0
best_score = -1
silhouette_scores = []

# 测试不同的k值，从2开始，因为至少需要两个簇来计算轮廓系数
for k in range(2, 41):  # 尝试2到10个簇
    kmeans = KMeans(n_clusters=k, random_state=42)
    cluster_labels = kmeans.fit_predict(X)
    score = silhouette_score(X, cluster_labels)
    silhouette_scores.append(score)
    
    print(f"Number of Clusters: {k}, Silhouette Score: {score}")
    
    if score > best_score:
        best_score = score
        best_k = k

print(f"Optimal Number of Clusters based on Silhouette Score: {best_k}")

# 使用最佳k值进行最终聚类
optimal_kmeans = KMeans(n_clusters=best_k, random_state=42)
cluster_labels = optimal_kmeans.fit_predict(X)

# 将聚类结果添加到原始DataFrame
df['Commodities Cluster'] = cluster_labels

# 可选：降维以帮助可视化
pca = PCA(n_components=2)
reduced_features = pca.fit_transform(X.toarray())

# 添加降维后的坐标到DataFrame
df[['PCA1', 'PCA2']] = reduced_features

# 评估聚类效果
silhouette_avg = silhouette_score(X, cluster_labels)
print(f'Silhouette Score for Optimal k: {silhouette_avg}')

# 查看每个簇的代表性元素
for i in range(best_k):
    print(f'\nCluster {i}:')
    print(df[df['Commodities Cluster'] == i]['Commodity(s)_cleaned'].value_counts().head())

# # 保存聚类结果到新的Excel文件
# df.to_excel(r"C:\Users\<USER>\Desktop\Commodities_Clustered.xlsx", index=False)

# 绘制轮廓系数图
plt.plot(range(2, 41), silhouette_scores, 'bo-')
plt.xlabel('类数量')
plt.ylabel('轮廓系数')
plt.title('铜矿：矿产资源')
plt.show()