import pandas as pd

file_path = r"C:\Users\<USER>\Desktop\Cobalt.xlsx"
data = pd.read_excel(file_path,sheet_name='Sheet1')
df = pd.DataFrame(data)

#print(df.columns)

df['Global Region'] = df['Global Region'].replace({'United States and Canada': 0,
                                                   'Africa': 1,
                                                   'Asia-Pacific': 2,
                                                   'Europe': 3,
                                                   'Latin America and Caribbean': 4,
                                                   'Middle East': 5})

df['Overall Risk Assessment'] = df['Overall Risk Assessment'].replace({'Low': 0,
                                                                       'Medium': 1,
                                                                       'High': 2})

df['Security Risk Assessment'] = df['Security Risk Assessment'].replace({'Low': 0,
                                                                         'Medium': 1,
                                                                         'High': 2})

df['Political Risk Assessment'] = df['Political Risk Assessment'].replace({'Low': 0,
                                                                           'Medium': 1,
                                                                           'High': 2})

df['Economic Risk Assessment'] = df['Economic Risk Assessment'].replace({'Low': 0,
                                                                         'Medium': 1,
                                                                         'High': 2})

df['Legal Risk Assessment'] = df['Legal Risk Assessment'].replace({'Low': 0,
                                                                   'Medium': 1,
                                                                   'High': 2})

df['Tax Risk Assessment'] = df['Tax Risk Assessment'].replace({'Low': 0,
                                                               'Medium': 1,
                                                               'High': 2})

df['Operational Risk Assessment'] = df['Operational Risk Assessment'].replace({'Low': 0,
                                                                               'Medium': 1,
                                                                               'High': 2})

df['Development Stage'] = df['Development Stage'].replace({'Closed': 0,
                                                           'Resource Pool': 1, 
                                                           'Undergoing Feasibility': 2,
                                                           'Incentive':3, 
                                                           'Per Production':4, 
                                                           'Production':5})

df['Activity Status'] = df['Activity Status'].replace({'Inactive': 0,
                                                       'Active': 1, 
                                                       'Temporarily On Hold': 2})
# copper
# Other = ["Silver", "Molybdenum", "Cobalt","Lead","Palladium","Tin","Iron Ore","Chromite"
#          ,"U3O8","Lithium","Vadium","Lanthanides","Vadium","Diamonds","Tungsten","Platinum"]
# df['Primary Commodity'] = df['Primary Commodity'].replace(Other, 'Other')
# df['Primary Commodity'] = df['Primary Commodity'].replace({'Copper': 0,
#                                                            'Nickel': 1, 
#                                                            'Gold': 2,
#                                                            'Zinc': 3,
#                                                            'Other': 4})

Other = ["Silver", "Molybdenum","Lead","Palladium","Tin","Iron Ore","Chromite"
         ,"U3O8","Lithium","Vadium","Lanthanides","Vadium","Diamonds","Tungsten"
         ,"Platinum","Zinc","Gold","Scandium"]
df['Primary Commodity'] = df['Primary Commodity'].replace(Other, 'Other')
df['Primary Commodity'] = df['Primary Commodity'].replace({'Copper': 0,
                                                           'Nickel': 1, 
                                                           'Cobalt': 2,
                                                           'Other': 3})

df['Mine Type'] = df['Mine Type'].replace({'Underground': 0,'Open Pit': 1})

output_path = r"C:\Users\<USER>\Desktop\Cobalt3.xlsx"
df.to_excel(output_path, index=False)