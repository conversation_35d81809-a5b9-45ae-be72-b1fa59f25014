import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split, cross_val_score
import xgboost as xgb
from sklearn.ensemble import RandomForestRegressor
from sklearn.metrics import mean_squared_error, r2_score
from sklearn.neighbors import NearestNeighbors, KNeighborsRegressor
import matplotlib.pyplot as plt

def preprocess_datasets(df_large, df_small):
    """改进的预处理函数"""
    # 分离特征
    categorical_features = df_large.columns[1:18].tolist()
    numerical_features = df_large.columns[18:31].tolist()
    features = categorical_features + numerical_features
    
    # 处理大数据集
    X_large = df_large[features].copy()  # 使用copy避免警告
    y_large = df_large['Total Cost'].copy()
    
    # 处理小数据集
    mask_labeled = df_small['Total Cost'].notna()
    X_small_train = df_small[mask_labeled][features].copy()
    y_small_train = df_small[mask_labeled]['Total Cost'].copy()
    X_small_predict = df_small[~mask_labeled][features].copy()
    
    # 标准化数值特征
    scaler = StandardScaler()
    X_large[numerical_features] = scaler.fit_transform(X_large[numerical_features])
    X_small_train[numerical_features] = scaler.transform(X_small_train[numerical_features])
    X_small_predict[numerical_features] = scaler.transform(X_small_predict[numerical_features])
    
    # 转换为numpy数组
    X_large = X_large.values
    y_large = y_large.values
    X_small_train = X_small_train.values
    y_small_train = y_small_train.values
    X_small_predict = X_small_predict.values
    
    return (X_large, y_large), (X_small_train, y_small_train, X_small_predict), mask_labeled

def train_transfer_model(X_large, y_large, X_small, y_small):
    """改进的迁移学习策略"""
    # 1. 在大数据集上预训练多个基础模型
    base_models = {
        'rf': RandomForestRegressor(
            n_estimators=200,
            max_depth=4,
            random_state=42
        ),
        'xgb': xgb.XGBRegressor(
            n_estimators=200,
            learning_rate=0.01,
            max_depth=3,
            random_state=42
        )
    }
    
    # 训练基础模型
    for name, model in base_models.items():
        model.fit(X_large, y_large)
    
    # 2. 获取特征重要性（使用随机森林的特征重要性）
    important_features = np.argsort(base_models['rf'].feature_importances_)[-10:]
    
    # 3. 提取多个知识特征
    knowledge_features = []
    
    # 3.1 模型预测特征
    for model in base_models.values():
        pred = model.predict(X_small).reshape(-1, 1)
        knowledge_features.append(pred)
    
    # 3.2 相似度特征
    knn = NearestNeighbors(n_neighbors=5)
    knn.fit(X_large)
    distances, indices = knn.kneighbors(X_small)
    similar_samples = y_large[indices]
    knowledge_features.append(np.mean(similar_samples, axis=1).reshape(-1, 1))
    
    # 3.3 领域知识特征
    domain_features = np.column_stack([
        X_small[:, important_features],  # 重要特征
        np.mean(X_small[:, important_features], axis=1).reshape(-1, 1),  # 特征均值
        np.std(X_small[:, important_features], axis=1).reshape(-1, 1)    # 特征标准差
    ])
    
    # 4. 组合所有特征
    X_enhanced = np.hstack([X_small] + knowledge_features + [domain_features])
    
    # 5. 训练最终模型
    transfer_model = RandomForestRegressor(
        n_estimators=100,
        max_depth=4,
        min_samples_split=3,
        min_samples_leaf=2,
        random_state=42
    )
    
    transfer_model.fit(X_enhanced, y_small)
    
    return transfer_model, base_models, knowledge_features, important_features

def evaluate_models(models, X_val, y_val, base_models, important_features, X_large, y_large):
    """修改评估函数以适应新的特征选择"""
    print("\n" + "="*50)
    print("模型性能评估")
    print("="*50)
    
    metrics = {}
    for name, model in models.items():
        if name == 'Transfer':
            # 为迁移模型准备增强特征
            knowledge_features = []
            
            # 获取所有基础模型的预测
            for base_model in base_models.values():
                pred = base_model.predict(X_val).reshape(-1, 1)
                knowledge_features.append(pred)
            
            # 添加相似度特征
            knn = NearestNeighbors(n_neighbors=5)
            knn.fit(X_large)
            distances, indices = knn.kneighbors(X_val)
            similar_samples = y_large[indices]
            knowledge_features.append(np.mean(similar_samples, axis=1).reshape(-1, 1))
            
            # 添加领域知识特征
            domain_features = np.column_stack([
                X_val[:, important_features],  # 重要特征
                np.mean(X_val[:, important_features], axis=1).reshape(-1, 1),  # 特征均值
                np.std(X_val[:, important_features], axis=1).reshape(-1, 1)    # 特征标准差
            ])
            
            # 组合所有特征
            X_val_enhanced = np.hstack([X_val] + knowledge_features + [domain_features])
            y_pred = model.predict(X_val_enhanced)
        else:
            y_pred = model.predict(X_val)
        
        # 计算各种评估指标
        mse = mean_squared_error(y_val, y_pred)
        rmse = np.sqrt(mse)
        r2 = r2_score(y_val, y_pred)
        mae = np.mean(np.abs(y_val - y_pred))
        mape = np.mean(np.abs((y_val - y_pred) / y_val)) * 100
        
        metrics[name] = {
            'MSE': mse,
            'RMSE': rmse,
            'R2': r2,
            'MAE': mae,
            'MAPE': mape
        }
        
        print(f"\n{name} 模型评估指标:")
        print(f"均方误差 (MSE): {mse:.2f}")
        print(f"均方根误差 (RMSE): {rmse:.2f}")
        print(f"决定系数 (R²): {r2:.4f}")
        print(f"平均绝对误差 (MAE): {mae:.2f}")
        print(f"平均绝对百分比误差 (MAPE): {mape:.2f}%")
    
    # 找出最佳模型
    best_model = min(metrics.items(), key=lambda x: x[1]['RMSE'])
    print(f"\n最佳模型: {best_model[0]}")
    print(f"RMSE提升: {((metrics['XGBoost']['RMSE'] - metrics['Transfer']['RMSE'])/metrics['XGBoost']['RMSE']*100):.2f}%")
    
    return metrics

def main():
    # 1. 读取数据
    print("读取数据...")
    df_large = pd.read_excel('large_dataset_filled.xlsx')  # 使用填充后的大数据集
    df_small = pd.read_excel('small_dataset.xlsx')
    
    # 2. 预处理数据
    print("\n预处理数据...")
    (X_large, y_large), (X_small_train, y_small_train, X_small_predict), mask_labeled = preprocess_datasets(df_large, df_small)
    
    # 3. 首先划分训练集和验证集
    print("\n划分数据集...")
    X_train, X_val, y_train, y_val = train_test_split(
        X_small_train, y_small_train, test_size=0.2, random_state=42
    )
    
    # 4. 训练迁移学习模型
    print("\n训练迁移学习模型...")
    transfer_model, base_models, knowledge_features, important_features = train_transfer_model(X_large, y_large, X_train, y_train)  # 使用训练集

    # 5. 训练其他基准模型
    models = {
        'RandomForest': RandomForestRegressor(
            n_estimators=31,
            max_depth=4,
            min_samples_split=3,     
            min_samples_leaf=2,      
            random_state=42
        ),
        'XGBoost': xgb.XGBRegressor(
            n_estimators=100,        # 增加树的数量
            learning_rate=0.01,      # 略微增加学习率
            max_depth=4,             # 适当增加树深度
            min_child_weight=3,      # 减小正则化强度
            subsample=0.8,           # 增加采样比例
            colsample_bytree=0.8,    # 增加特征采样比例
            gamma=0.1,               # 降低分裂阈值
            reg_alpha=0.1,           # 降低L1正则化
            reg_lambda=1,            # 降低L2正则化
            tree_method='exact',     # 保持精确方法
            random_state=42
        ),
        'KNN': KNeighborsRegressor(
            n_neighbors=5,
            weights='distance',      
            metric='manhattan',      
            p=1                      
        ),
        'Transfer': transfer_model
    }
    
    # 训练基准模型
    print("\n训练基准模型...")
    for name, model in models.items():
        if name != 'Transfer':  # 迁移模型已经训练过了
            print(f"\n训练 {name} 模型...")
            model.fit(X_train, y_train)  # 使用训练集
    
    # 评估模型性能
    metrics = evaluate_models(
        models, 
        X_val, 
        y_val, 
        base_models, 
        important_features,
        X_large,  # 添加大数据集
        y_large   # 添加大数据集标签
    )  # 使用验证集
    
    # 保存评估结果
    metrics_df = pd.DataFrame(metrics).T
    metrics_df.to_excel('model_evaluation_metrics.xlsx')
    
    # 5. 绘制预测对比图
    plt.figure(figsize=(12, 8))
    plt.rcParams['font.sans-serif'] = ['SimHei']
    plt.rcParams['axes.unicode_minus'] = False
    
    # 绘制理想预测线
    plt.plot([y_val.min(), y_val.max()], [y_val.min(), y_val.max()], 
             'k--', label='理想预测', alpha=0.5)
    
    # 设置不同模型的颜色和标记
    model_settings = {
        'RandomForest': {'color': '#FF9999', 'marker': 'o', 'label': '随机森林'},
        'XGBoost': {'color': '#66B2FF', 'marker': 's', 'label': 'XGBoost'},
        'KNN': {'color': '#99FF99', 'marker': 'D', 'label': 'KNN'},
        'Transfer': {'color': '#FF99FF', 'marker': 'x', 'label': '迁移学习'}
    }
    
    # 绘制预测散点
    for name, model in models.items():
        settings = model_settings[name]
        if name == 'Transfer':
            # 为迁移模型准备增强特征
            knowledge_features = []
            
            # 获取所有基础模型的预测
            for base_model in base_models.values():
                pred = base_model.predict(X_val).reshape(-1, 1)
                knowledge_features.append(pred)
            
            # 添加相似度特征
            knn = NearestNeighbors(n_neighbors=5)
            knn.fit(X_large)
            distances, indices = knn.kneighbors(X_val)
            similar_samples = y_large[indices]
            knowledge_features.append(np.mean(similar_samples, axis=1).reshape(-1, 1))
            
            # 添加领域知识特征
            domain_features = np.column_stack([
                X_val[:, important_features],  # 重要特征
                np.mean(X_val[:, important_features], axis=1).reshape(-1, 1),  # 特征均值
                np.std(X_val[:, important_features], axis=1).reshape(-1, 1)    # 特征标准差
            ])
            
            # 组合所有特征
            X_val_enhanced = np.hstack([X_val] + knowledge_features + [domain_features])
            y_pred = model.predict(X_val_enhanced)
        else:
            y_pred = model.predict(X_val)
        
        plt.scatter(y_val, y_pred, 
                   alpha=0.6, 
                   s=60,
                   label=settings['label'],
                   color=settings['color'],
                   marker=settings['marker'])
    
    plt.title('各模型预测效果比较', fontsize=14)
    plt.xlabel('实际值', fontsize=12)
    plt.ylabel('预测值', fontsize=12)
    plt.grid(True, linestyle='--', alpha=0.3)
    plt.legend(fontsize=10)
    plt.tight_layout()
    plt.savefig('transfer_comparison.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # 6. 预测缺失值
    print("\n预测缺失值...")
    # 为预测数据准备增强特征
    knowledge_features = []
    
    # 获取所有基础模型的预测
    for model in base_models.values():
        pred = model.predict(X_small_predict).reshape(-1, 1)
        knowledge_features.append(pred)
    
    # 添加相似度特征
    knn = NearestNeighbors(n_neighbors=6)
    knn.fit(X_large)
    distances, indices = knn.kneighbors(X_small_predict)
    similar_samples = y_large[indices]
    knowledge_features.append(np.mean(similar_samples, axis=1).reshape(-1, 1))
    
    # 添加领域知识特征
    domain_features = np.column_stack([
        X_small_predict[:, important_features],  # 重要特征
        np.mean(X_small_predict[:, important_features], axis=1).reshape(-1, 1),  # 特征均值
        np.std(X_small_predict[:, important_features], axis=1).reshape(-1, 1)    # 特征标准差
    ])
    
    # 组合所有特征
    X_predict_enhanced = np.hstack([X_small_predict] + knowledge_features + [domain_features])
    predictions = transfer_model.predict(X_predict_enhanced)
    
    # 保存预测结果
    results = pd.DataFrame({
        'Property ID': df_small[~mask_labeled]['Property ID'],
        'Predicted_Total_Cost': predictions
    })
    results.to_excel('small_dataset_transfer_predictions.xlsx', index=False)
    
    print("\n预测结果已保存到 small_dataset_transfer_predictions.xlsx")
    print("\n模型对比图已保存到 transfer_comparison.png")

if __name__ == "__main__":
    main() 