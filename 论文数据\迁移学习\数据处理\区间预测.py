import matplotlib.pyplot as plt
import pandas as pd
from statsmodels.tsa.arima.model import ARIMA
import numpy as np
from sklearn.preprocessing import MinMaxScaler
from keras.models import Sequential
from keras.layers import LSTM, Dense
from sklearn.metrics import mean_absolute_error, mean_squared_error, mean_absolute_percentage_error, r2_score


plt.rcParams['font.sans-serif'] = ['SimHei']  # 使用黑体
plt.rcParams['axes.unicode_minus'] = False  # 正常显示负号

# # 假设你的数据存储在一个 CSV 文件中，包含两列：Date 和 LME-Cobalt Cash ($/tonne)
# # 读取数据
# file_path = r"C:\Users\<USER>\Desktop\Price-Cobalt.xlsx"
# data = pd.read_excel(file_path, sheet_name='Sheet1')
# # data = pd.read_excel(file_path, sheet_name='Data')
# df = pd.DataFrame(data)


# # 确保日期列是日期格式
# df['Date'] = pd.to_datetime(df['Date'])

# # # 按日期排序（倒置数据）
# # df = df.sort_values(by='Date', ascending=False).reset_index(drop=True)

# # 再次倒置数据，使其按日期从旧到新排列
# df = df.sort_values(by='Date', ascending=True).reset_index(drop=True)

# # 检查倒置后的数据
# print("\n倒置后的数据：")
# print(df.head())

# # 按月份分组，计算每个月的最大值和最小值
# df_monthly = df.resample('M', on='Date').agg(
#     {
#         'LME-Cobalt Cash ($/tonne)': ['max', 'min'],
#         'Date': 'max'
#     }
# ).reset_index()

# # 检查聚合后的列名
# print("\n聚合后的列名：")
# print(df_monthly.columns)

# # 重命名列
# df_monthly.columns = ['Date', 'Max_Price', 'Min_Price', 'Date']  # 确保列名数量匹配

# # 删除多余的列
# df_monthly = df_monthly[['Date', 'Max_Price', 'Min_Price']]

# # 检查结果
# print("\n按月份分组后的结果：")
# print(df_monthly)

# # 保存结果到 CSV 文件
# df_monthly.to_csv('result_monthly.csv', index=False)


# # 确保日期列是日期格式
# df['Date'] = pd.to_datetime(df['Date'])

# # 创建走势图
# plt.figure(figsize=(10, 8))  # 设置图表大小
# plt.plot(df['Date'], df['LME-Cobalt Cash ($/tonne)'], linestyle='-')  # 绘制价格走势，'o'表示数据点

# # 添加标题和标签
# plt.xlabel('时间',fontsize=14, fontweight='bold')
# plt.ylabel('钴矿现货价格(美元/吨)',fontsize=14, fontweight='bold')

# # 优化日期显示
# plt.gcf().autofmt_xdate()  # 自动旋转日期标记以避免重叠
# plt.xticks(rotation=45)  # 旋转x轴上的日期标记

# # 添加网格线
# plt.grid(True, linestyle='--', alpha=0.7)

# # 显示图表
# plt.show()



# # 创建走势图
# plt.figure(figsize=(10, 8))  # 设置图表大小

# # 绘制 Max_Price 走势，使用红色线条表示
# plt.plot(df['Date'], df['Max_Price'], color='red', label='最高价格')

# # 绘制 Min_Price 走势，使用绿色线条表示
# plt.plot(df['Date'], df['Min_Price'], color='green', label='最低价格')

# # 添加标题和标签

# plt.xlabel('时间',fontsize=14, fontweight='bold')
# plt.ylabel('钴矿现货价格(美元/吨)',fontsize=14, fontweight='bold')

# # 优化日期显示
# plt.gcf().autofmt_xdate()  # 自动旋转日期标记以避免重叠
# plt.xticks(rotation=45)  # 旋转x轴上的日期标记

# # 显示图例
# plt.legend()

# # 显示网格
# plt.grid(True, linestyle='--', alpha=0.7)

# # 显示图表
# plt.show()




# 读取数据
file_path = r"C:\Users\<USER>\Desktop\Price-Cobalt.xlsx"
data = pd.read_excel(file_path, sheet_name='Sheet1')
df = pd.DataFrame(data)

# 数据预处理
df['Date'] = pd.to_datetime(df['Date'])
df.set_index('Date', inplace=True)

# 提取 Max_Price 和 Min_Price
max_price = df['Max_Price'].values.reshape(-1, 1)
min_price = df['Min_Price'].values.reshape(-1, 1)

# 归一化数据
scaler_max = MinMaxScaler(feature_range=(0, 1))
scaler_min = MinMaxScaler(feature_range=(0, 1))

max_price_scaled = scaler_max.fit_transform(max_price)
min_price_scaled = scaler_min.fit_transform(min_price)

# 创建时间序列数据
def create_dataset(data, time_step=1):
    X, y = [], []
    for i in range(len(data) - time_step - 1):
        X.append(data[i:(i + time_step), 0])
        y.append(data[i + time_step, 0])
    return np.array(X), np.array(y)

time_step = 10  # 使用前10个时间步预测下一个值

# 创建 Max_Price 数据集
X_max, y_max = create_dataset(max_price_scaled, time_step)
# 创建 Min_Price 数据集
X_min, y_min = create_dataset(min_price_scaled, time_step)

# 调整输入形状以适应 LSTM 模型
X_max = X_max.reshape(X_max.shape[0], X_max.shape[1], 1)
X_min = X_min.reshape(X_min.shape[0], X_min.shape[1], 1)

# 构建 LSTM 模型
def build_model():
    model = Sequential()
    model.add(LSTM(50, return_sequences=True, input_shape=(time_step, 1)))
    model.add(LSTM(50, return_sequences=False))
    model.add(Dense(25))
    model.add(Dense(1))
    model.compile(optimizer='adam', loss='mean_squared_error', metrics=['mae'])
    return model

# 训练模型
model_max = build_model()
history_max = model_max.fit(X_max, y_max, epochs=50, batch_size=32, verbose=1)

model_min = build_model()
history_min = model_min.fit(X_min, y_min, epochs=50, batch_size=32, verbose=1)

# 绘制损失函数变化图
plt.figure(figsize=(12, 6))
plt.subplot(1, 2, 1)
plt.plot(history_max.history['loss'], label='最大值训练误差')
plt.title('最大值训练误差')
plt.xlabel('迭代次数')
plt.ylabel('损失')
plt.legend()

plt.subplot(1, 2, 2)
plt.plot(history_min.history['loss'], label='最小值训练误差')
plt.title('最小值训练误差')
plt.xlabel('迭代次数')
plt.ylabel('损失')
plt.legend()
plt.show()

# 定义模型评价函数
def evaluate_model(model, X, y, scaler, column_name):
    # 预测
    predictions = model.predict(X)
    # 逆归一化预测值和真实值
    y_pred = scaler.inverse_transform(predictions)
    y_true = scaler.inverse_transform(y.reshape(-1, 1))
    # 计算指标
    mse = mean_squared_error(y_true, y_pred)
    mae = mean_absolute_error(y_true, y_pred)
    mape = mean_absolute_percentage_error(y_true, y_pred)
    r2 = r2_score(y_true, y_pred)
    # 打印指标
    print(f"--- {column_name} Model Evaluation ---")
    print(f"Mean Squared Error (MSE): {mse:.4f}")
    print(f"Mean Absolute Error (MAE): {mae:.4f}")
    print(f"Mean Absolute Percentage Error (MAPE): {mape:.4f}%")
    print(f"R² Score: {r2:.4f}")
    return {
        'MSE': mse,
        'MAE': mae,
        'MAPE': mape,
        'R²': r2
    }

# 评估 Max_Price 模型
evaluation_max = evaluate_model(model_max, X_max, y_max, scaler_max, "Max_Price")

# 评估 Min_Price 模型
evaluation_min = evaluate_model(model_min, X_min, y_min, scaler_min, "Min_Price")

def predict_future(model, data, scaler, time_step, forecast_steps):
    predictions = []
    current_batch = data[-time_step:]
    current_batch = current_batch.reshape(1, time_step, 1)
    for i in range(forecast_steps):
        pred = model.predict(current_batch)[0]
        predictions.append(pred)
        current_batch = np.roll(current_batch, -1)
        current_batch[0, -1, 0] = pred
    return scaler.inverse_transform(np.array(predictions).reshape(-1, 1))

forecast_steps = 60  # 预测未来12个时间步

# 预测 Max_Price 和 Min_Price
future_max = predict_future(model_max, max_price_scaled, scaler_max, time_step, forecast_steps)
future_min = predict_future(model_min, min_price_scaled, scaler_min, time_step, forecast_steps)

# 生成预测的时间轴，确保与原数据连续
original_dates = df.index.tolist()
start_date = original_dates[-1] + pd.Timedelta(days=31)  # 假设按月推移，这里可以根据实际数据频率调整
forecast_dates_max = pd.date_range(start=start_date, periods=forecast_steps, freq='M')
forecast_dates_min = pd.date_range(start=start_date, periods=forecast_steps, freq='M')

# 将预测结果保存到DataFrame并导出到Excel
forecast_data = pd.DataFrame({
    'Date': forecast_dates_max,
    'Predicted_Max_Price': future_max[:, 0],
    'Predicted_Min_Price': future_min[:, 0]
})

# 将预测结果保存为Excel文件
forecast_data.to_excel('cobalt_price_forecast.xlsx', index=False)
print("预测结果已保存到 cobalt_price_forecast.xlsx 文件中。")

# 可视化结果
plt.figure(figsize=(10, 6))
plt.plot(df.index, df['Max_Price'], label='最高价格', marker='o')
plt.plot(df.index, df['Min_Price'], label='最低价格', marker='o')
plt.plot(forecast_dates_max, future_max[:, 0], label='预测最高价格', linestyle='--', color='orange', marker='^')
plt.plot(forecast_dates_min, future_min[:, 0], label='预测最低价格', linestyle='--', color='green', marker='^')
plt.axvline(x=df.index[-1], color='black', linestyle='--')  # 分隔原数据和预测数据
plt.title('钴矿价格区间预测')
plt.xlabel('时间')
plt.ylabel('钴矿价格 (美元/吨)')
plt.legend(loc='upper left')
plt.grid(True)
plt.show()