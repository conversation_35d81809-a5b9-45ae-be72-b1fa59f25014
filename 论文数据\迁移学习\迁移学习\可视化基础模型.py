import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestRegressor
from sklearn.neighbors import K<PERSON>eighborsRegressor
from sklearn.svm import SVR
from xgboost import XGBRegressor
from sklearn.metrics import mean_squared_error, r2_score
import matplotlib.pyplot as plt

# 数据预处理函数
def preprocess_datasets(df_small):
    """数据预处理函数"""
    # 分离特征
    categorical_features = df_small.columns[1:18].tolist()
    numerical_features = df_small.columns[18:31].tolist()
    features = categorical_features + numerical_features

    # 处理小数据集
    mask_labeled = df_small['总成本'].notna()
    X_small_train = df_small[mask_labeled][features].copy()
    y_small_train = df_small[mask_labeled]['总成本'].copy()
    X_small_predict = df_small[~mask_labeled][features].copy()

    # 标准化数值特征
    scaler = StandardScaler()
    X_small_train[numerical_features] = scaler.fit_transform(X_small_train[numerical_features])
    X_small_predict[numerical_features] = scaler.transform(X_small_predict[numerical_features])

    # 转换为numpy数组
    X_small_train = X_small_train.values
    y_small_train = y_small_train.values
    X_small_predict = X_small_predict.values

    return X_small_train, y_small_train, X_small_predict, mask_labeled

def plot_predictions(y_true, y_preds, title, save_path=None):
    plt.figure(figsize=(12, 8))
    plt.rcParams['font.sans-serif'] = ['Arial', 'DejaVu Sans', 'Liberation Sans']
    plt.rcParams['axes.unicode_minus'] = False

    model_settings = {
        'RandomForest': {'color': '#FF9999', 'label': 'Random Forest'},
        'XGBoost': {'color': '#66B2FF', 'label': 'XGBoost'},
        'KNN': {'color': '#99FF99', 'label': 'KNN'},
        'SVR': {'color': '#FFCC99', 'label': 'SVR'}
    }

    sorted_indices = np.argsort(y_true)
    plt.plot(
        np.arange(len(y_true)),
        y_true[sorted_indices],
        'k--',
        label='Actual Values',
        alpha=0.5
    )

    for name, y_pred in y_preds.items():
        settings = model_settings[name]
        plt.plot(
            np.arange(len(y_true)),
            y_pred[sorted_indices],
            label=settings['label'],
            color=settings['color']
        )

    plt.title(title, fontsize=14)
    plt.xlabel('Sample Index', fontsize=12)
    plt.ylabel('Cost Value', fontsize=12)
    plt.grid(True, linestyle='--', alpha=0.3)
    plt.legend(fontsize=10)
    plt.tight_layout()
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.show()

def plot_missing_predictions(predictions, title, save_path=None):
    plt.figure(figsize=(12, 8))
    plt.rcParams['font.sans-serif'] = ['Arial', 'DejaVu Sans', 'Liberation Sans']
    plt.rcParams['axes.unicode_minus'] = False

    model_settings = {
        'RandomForest': {'color': '#FF9999', 'label': 'Random Forest'},
        'XGBoost': {'color': '#66B2FF', 'label': 'XGBoost'},
        'KNN': {'color': '#99FF99', 'label': 'KNN'},
        'SVR': {'color': '#FFCC99', 'label': 'SVR'}
    }

    for name, y_pred in predictions.items():
        settings = model_settings[name]
        plt.plot(
            np.arange(len(y_pred)),
            y_pred,
            label=settings['label'],
            color=settings['color']
        )

    plt.title(title, fontsize=14)
    plt.xlabel('Sample Index', fontsize=12)
    plt.ylabel('Cost Value', fontsize=12)
    plt.grid(True, linestyle='--', alpha=0.3)
    plt.legend(fontsize=10)
    plt.tight_layout()
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.show()

# 评估模型性能并绘制预测结果图
def evaluate_models(models, X_train, y_train, X_val, y_val):
    """评估模型性能并绘制预测结果图"""
    print("\n" + "=" * 50)
    print("模型性能评估")
    print("=" * 50)

    metrics = {}
    train_predictions = {}  # 用于存储训练集预测结果
    val_predictions = {}    # 用于存储验证集预测结果

    for name, model in models.items():
        # 训练模型
        model.fit(X_train, y_train)

        # 训练集预测
        y_train_pred = model.predict(X_train)
        train_predictions[name] = y_train_pred

        # 验证集预测
        y_val_pred = model.predict(X_val)
        val_predictions[name] = y_val_pred

        # 计算各种评估指标
        mse_train = mean_squared_error(y_train, y_train_pred)
        rmse_train = np.sqrt(mse_train)
        r2_train = r2_score(y_train, y_train_pred)

        mse_val = mean_squared_error(y_val, y_val_pred)
        rmse_val = np.sqrt(mse_val)
        r2_val = r2_score(y_val, y_val_pred)

        metrics[name] = {
            'Train MSE': mse_train,
            'Train RMSE': rmse_train,
            'Train R2': r2_train,
            'Val MSE': mse_val,
            'Val RMSE': rmse_val,
            'Val R2': r2_val
        }

        print(f"\n{name} 模型评估指标:")
        print(f"训练集均方误差 (MSE): {mse_train:.2f}")
        print(f"训练集均方根误差 (RMSE): {rmse_train:.2f}")
        print(f"训练集决定系数 (R²): {r2_train:.4f}")
        print(f"验证集均方误差 (MSE): {mse_val:.2f}")
        print(f"验证集均方根误差 (RMSE): {rmse_val:.2f}")
        print(f"验证集决定系数 (R²): {r2_val:.4f}")

    plot_predictions(
        y_true=y_train,
        y_preds=train_predictions,
        title='Training Set Prediction Results Comparison',
        save_path='train_predictions.png'
    )

    plot_predictions(
        y_true=y_val,
        y_preds=val_predictions,
        title='Validation Set Prediction Results Comparison',
        save_path='val_predictions.png'
    )

    return metrics, train_predictions, val_predictions

# 主函数
def main():
    # 1. 读取小数据集
    print("读取小数据集...")
    try:
        df_small = pd.read_excel('small_dataset.xlsx')
    except Exception as e:
        print(f"读取小数据集时发生错误: {e}")
        return

    # 2. 预处理数据
    print("\n预处理数据...")
    X_small_train, y_small_train, X_small_predict, mask_labeled = preprocess_datasets(df_small)

    # 3. 首先划分训练集和验证集
    print("\n划分数据集...")
    X_train, X_val, y_train, y_val = train_test_split(
        X_small_train, y_small_train, test_size=0.2, random_state=42
    )

    # 4. 定义模型
    models = {
        'RandomForest': RandomForestRegressor(n_estimators=25, max_depth=5,
                                              min_samples_split=3, min_samples_leaf=2, random_state=42),
        'XGBoost': XGBRegressor(n_estimators=131, learning_rate=0.02, max_depth=5, subsample=0.8, colsample_bytree=0.8,
                                objective='reg:squarederror', random_state=42),
        'KNN': KNeighborsRegressor(n_neighbors=5, weights='distance', metric='manhattan'),
        'SVR': SVR(C=138, epsilon=0.2, kernel='linear')
    }

    # 评估模型性能并绘制预测结果图
    metrics, train_predictions, val_predictions = evaluate_models(models, X_train, y_train, X_val, y_val)

    # 保存评估结果
    try:
        metrics_df = pd.DataFrame(metrics).T
        metrics_df.to_excel('model_evaluation_metrics.xlsx')
        print("\n评估结果已保存到 model_evaluation_metrics.xlsx")
    except Exception as e:
        print(f"保存评估结果时发生错误: {e}")

    # 5. 预测缺失值
    print("\n预测缺失值...")
    predictions = {}
    for name, model in models.items():
        y_pred = model.predict(X_small_predict)
        predictions[name] = y_pred

    plot_missing_predictions(
        predictions,
        title='Missing Value Prediction Results',
        save_path='missing_predictions.png'
    )

    # 保存预测结果
    try:
        results = pd.DataFrame({
            'Property ID': df_small[~mask_labeled]['Property ID'],
            'RandomForest_Pred': predictions['RandomForest'],
            'XGBoost_Pred': predictions['XGBoost'],
            'KNN_Pred': predictions['KNN'],
            'SVR_Pred': predictions['SVR']
        })
        results.to_excel('small_dataset_predictions.xlsx', index=False)
        print("\n预测结果已保存到 small_dataset_predictions.xlsx")
    except Exception as e:
        print(f"保存预测结果时发生错误: {e}")

if __name__ == "__main__":
    main()