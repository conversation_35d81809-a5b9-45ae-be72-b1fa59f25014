import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split, GridSearchCV
import xgboost as xgb
from sklearn.ensemble import RandomForestRegressor
from sklearn.svm import SVR
from sklearn.metrics import mean_squared_error, r2_score
from sklearn.neighbors import NearestNeighbors, KNeighborsRegressor
import matplotlib.pyplot as plt
import seaborn as sns


def preprocess_datasets(df_large, df_small):
    """改进的预处理函数"""
    # 分离特征
    categorical_features = df_large.columns[1:18].tolist()
    numerical_features = df_large.columns[18:31].tolist()
    features = categorical_features + numerical_features

    # 处理大数据集
    X_large = df_large[features].copy()  # 使用copy避免警告
    y_large = df_large['总成本'].copy()

    # 处理小数据集
    mask_labeled = df_small['总成本'].notna()
    X_small_train = df_small[mask_labeled][features].copy()
    y_small_train = df_small[mask_labeled]['总成本'].copy()
    X_small_predict = df_small[~mask_labeled][features].copy()

    # 标准化数值特征
    scaler = StandardScaler()
    X_large[numerical_features] = scaler.fit_transform(X_large[numerical_features])
    X_small_train[numerical_features] = scaler.transform(X_small_train[numerical_features])
    X_small_predict[numerical_features] = scaler.transform(X_small_predict[numerical_features])

    # 转换为numpy数组
    X_large = X_large.values
    y_large = y_large.values
    X_small_train = X_small_train.values
    y_small_train = y_small_train.values
    X_small_predict = X_small_predict.values

    return (X_large, y_large), (X_small_train, y_small_train, X_small_predict), mask_labeled


def train_transfer_model(X_large, y_large, X_small, y_small):
    """改进的迁移学习策略"""
    # 1. 在大数据集上预训练多个基础模型
    base_models = {
        'rf': RandomForestRegressor(
            n_estimators=154,
            max_depth=4,
            random_state=42
        ),
        'xgb': xgb.XGBRegressor(
            n_estimators=151,
            learning_rate=0.01,
            max_depth=4,
            random_state=42
        )
    }
    # 训练基础模型
    for name, model in base_models.items():
        model.fit(X_large, y_large)

    # 2. 获取特征重要性（使用随机森林的特征重要性）
    feature_importances = base_models['xgb'].feature_importances_
    important_features_indices = np.argsort(feature_importances)[-10:]

    # 确保 important_features_indices 是整数数组
    important_features_indices = important_features_indices.astype(int)

    # 按照重要性分数降序排序
    sorted_indices = important_features_indices[np.argsort(feature_importances[important_features_indices])[::-1]]

    # 3. 提取多个知识特征
    knowledge_features = []

    # 3.1 模型预测特征
    for model in base_models.values():
        pred = model.predict(X_small).reshape(-1, 1)
        knowledge_features.append(pred)

    # 3.2 相似度特征
    knn = NearestNeighbors(n_neighbors=6)
    knn.fit(X_large)
    distances, indices = knn.kneighbors(X_small)
    similar_samples = y_large[indices]
    knowledge_features.append(np.mean(similar_samples, axis=1).reshape(-1, 1))

    # 3.3 领域知识特征
    domain_features = np.column_stack([
        X_small[:, sorted_indices],  # 重要特征
        np.mean(X_small[:, sorted_indices], axis=1).reshape(-1, 1),  # 特征均值
        np.std(X_small[:, sorted_indices], axis=1).reshape(-1, 1)  # 特征标准差
    ])

    # 4. 组合所有特征
    X_enhanced = np.hstack([X_small] + knowledge_features + [domain_features])

    # 5. 训练最终模型
    transfer_model = RandomForestRegressor(
        n_estimators=84,
        max_depth=4,
        min_samples_split=3,
        min_samples_leaf=2,
        random_state=42
    )
    # transfer_model = xgb.XGBRegressor(
    #     n_estimators=111,
    #     max_depth=4,
    #     learning_rate=0.02,
    #     subsample=1,
    #     random_state=42
    # )

    transfer_model.fit(X_enhanced, y_small)

    return transfer_model, base_models, knowledge_features, sorted_indices


def optimize_hyperparameters(models, X_train, y_train):
    """使用网格搜索优化每个模型的超参数"""
    param_grids = {
        'RandomForest': {
            'n_estimators': [25],
            'max_depth': [5],
            'min_samples_split': [3],
            'min_samples_leaf': [2]
        },
        'XGBoost': {
            'n_estimators': [131],
            'learning_rate': [0.02],
            'max_depth': [5],
            'subsample': [0.8],
            'colsample_bytree': [0.8]

    },
        'KNN': {
            'n_neighbors': [6],
            'weights': ['distance'],
            'metric': ['manhattan']
        },
        'SVR': {
            'C': [138],
            'epsilon': [0.1],
            'kernel': ['linear']
        }
    }

    optimized_models = {}
    for name, model in models.items():
        if name == 'Transfer':
            continue  # 迁移模型已经在train_transfer_model中训练过

        grid_search = GridSearchCV(estimator=model, param_grid=param_grids[name], cv=3,
                                   scoring='neg_mean_squared_error', n_jobs=-1)
        grid_search.fit(X_train, y_train)

        print(f"\n最佳参数 ({name}): {grid_search.best_params_}")
        optimized_models[name] = grid_search.best_estimator_

    return optimized_models


def evaluate_models(models, X_val, y_val, base_models, important_features_indices, X_large, y_large):
    """修改评估函数以适应新的特征选择"""
    print("\n" + "=" * 50)
    print("模型性能评估")
    print("=" * 50)

    metrics = {}
    for name, model in models.items():
        if name == 'Transfer':
            # 为迁移模型准备增强特征
            knowledge_features = []

            # 获取所有基础模型的预测
            for base_model in base_models.values():
                pred = base_model.predict(X_val).reshape(-1, 1)
                knowledge_features.append(pred)

            # 添加相似度特征
            knn = NearestNeighbors(n_neighbors=6)
            knn.fit(X_large)
            distances, indices = knn.kneighbors(X_val)
            similar_samples = y_large[indices]
            knowledge_features.append(np.mean(similar_samples, axis=1).reshape(-1, 1))

            # 添加领域知识特征
            domain_features = np.column_stack([
                X_val[:, important_features_indices],  # 重要特征
                np.mean(X_val[:, important_features_indices], axis=1).reshape(-1, 1),  # 特征均值
                np.std(X_val[:, important_features_indices], axis=1).reshape(-1, 1)  # 特征标准差
            ])

            # 组合所有特征
            X_val_enhanced = np.hstack([X_val] + knowledge_features + [domain_features])
            y_pred = model.predict(X_val_enhanced)
        else:
            y_pred = model.predict(X_val)

        # 计算各种评估指标
        mse = mean_squared_error(y_val, y_pred)
        rmse = np.sqrt(mse)
        r2 = r2_score(y_val, y_pred)
        mae = np.mean(np.abs(y_val - y_pred))
        mape = np.mean(np.abs((y_val - y_pred) / y_val)) * 100

        metrics[name] = {
            'MSE': mse,
            'RMSE': rmse,
            'R2': r2,
            'MAE': mae,
            'MAPE': mape
        }

        print(f"\n{name} 模型评估指标:")
        print(f"均方误差 (MSE): {mse:.2f}")
        print(f"均方根误差 (RMSE): {rmse:.2f}")
        print(f"决定系数 (R²): {r2:.4f}")
        print(f"平均绝对误差 (MAE): {mae:.2f}")
        print(f"平均绝对百分比误差 (MAPE): {mape:.2f}%")

    # 找出最佳模型
    best_model = min(metrics.items(), key=lambda x: x[1]['RMSE'])
    print(f"\n最佳模型: {best_model[0]}")
    print(
        f"RMSE提升: {((metrics['XGBoost']['RMSE'] - metrics['Transfer']['RMSE']) / metrics['XGBoost']['RMSE'] * 100):.2f}%")

    return metrics


def plot_feature_importance(important_features_indices, feature_names, feature_importances, title='Feature Importance'):
    """绘制特征重要度图"""
    # 按照重要性分数降序排序
    sorted_indices = np.argsort(feature_importances[important_features_indices])
    sorted_important_features_indices = important_features_indices[sorted_indices]
    sorted_feature_importances = feature_importances[sorted_important_features_indices]
    sorted_feature_names = [feature_names[i] for i in sorted_important_features_indices]

    plt.figure(figsize=(12, 6))
    plt.barh(sorted_feature_names, sorted_feature_importances, align='center')
    plt.xlabel('重要性得分')
    plt.title('特征重要度')
    plt.grid(axis='x', linestyle='--', alpha=0.7)
    plt.tight_layout()
    plt.show()

def visualize_knn_predictions(X_large, y_large, X_val, indices, similar_samples_mean):
    """可视化KNN预测过程"""
    plt.figure(figsize=(12, 8))
    plt.rcParams['font.sans-serif'] = ['SimHei']
    plt.rcParams['axes.unicode_minus'] = False

    # 绘制大数据集中的样本
    plt.scatter(X_large[:, 0], X_large[:, 13], c=y_large, cmap='viridis', alpha=0.3, label='大数据集样本')

    # 绘制验证集中的样本及其最近邻
    for i in range(X_val.shape[0]):
        # 绘制验证集样本
        plt.scatter(X_val[i, 0], X_val[i, 13], c='red', marker='x', s=100, label='验证集样本' if i == 0 else "")

        # 绘制最近邻样本
        for j in indices[i]:
            plt.plot([X_val[i, 0], X_large[j, 0]], [X_val[i, 13], X_large[j, 13]], 'k--', alpha=0.5)

    # 添加标题和标签
    plt.title('相似度测算', fontsize=14)
    plt.xlabel('全球区域', fontsize=12)
    plt.ylabel('地质矿床类型', fontsize=12)
    plt.grid(True, linestyle='--', alpha=0.3)
    plt.legend(fontsize=10)
    plt.tight_layout()
    plt.savefig('knn_visualization.png', dpi=300, bbox_inches='tight')
    plt.show()

def main():
    # 1. 读取数据
    print("读取数据...")
    try:
        df_large = pd.read_excel('large_dataset_filled.xlsx')  # 使用填充后的大数据集
        df_small = pd.read_excel('small_dataset.xlsx')
    except Exception as e:
        print(f"读取数据时发生错误: {e}")
        return

    # 2. 预处理数据
    print("\n预处理数据...")
    (X_large, y_large), (X_small_train, y_small_train, X_small_predict), mask_labeled = preprocess_datasets(df_large,
                                                                                                            df_small)

    # 3. 首先划分训练集和验证集
    print("\n划分数据集...")
    X_train, X_val, y_train, y_val = train_test_split(
        X_small_train, y_small_train, test_size=0.2, random_state=42
    )

    # 4. 训练迁移学习模型
    print("\n训练迁移学习模型...")
    transfer_model, base_models, knowledge_features, important_features_indices = train_transfer_model(X_large, y_large,
                                                                                                       X_train,
                                                                                                       y_train)  # 使用训练集

    # 5. 训练其他基准模型并优化超参数
    models = {
        'RandomForest': RandomForestRegressor(random_state=42),
        'XGBoost': xgb.XGBRegressor(objective='reg:squarederror', random_state=42),
        'KNN': KNeighborsRegressor(),
        'SVR': SVR(),
        'Transfer': transfer_model
    }

    print("\n优化超参数...")
    optimized_models = optimize_hyperparameters(models, X_train, y_train)
    optimized_models['Transfer'] = transfer_model  # 将迁移模型添加回字典

    # 评估模型性能
    metrics = evaluate_models(
        optimized_models,
        X_val,
        y_val,
        base_models,
        important_features_indices,
        X_large,  # 添加大数据集
        y_large  # 添加大数据集标签
    )  # 使用验证集

    # 保存评估结果
    try:
        metrics_df = pd.DataFrame(metrics).T
        metrics_df.to_excel('model_evaluation_metrics.xlsx')
        print("\n评估结果已保存到 model_evaluation_metrics.xlsx")
    except Exception as e:
        print(f"保存评估结果时发生错误: {e}")

    # 5. 绘制预测对比图
    plt.figure(figsize=(12, 8))
    plt.rcParams['font.sans-serif'] = ['SimHei']
    plt.rcParams['axes.unicode_minus'] = False

    # 绘制理想预测线
    plt.plot([y_val.min(), y_val.max()], [y_val.min(), y_val.max()],
             'k--', label='理想预测', alpha=0.5)

    # 设置不同模型的颜色和标记
    model_settings = {
        'RandomForest': {'color': '#FF9999', 'marker': 'o', 'label': '随机森林'},
        'XGBoost': {'color': '#66B2FF', 'marker': 's', 'label': 'XGBoost'},
        'KNN': {'color': '#99FF99', 'marker': 'D', 'label': 'KNN'},
        'SVR': {'color': '#FFCC99', 'marker': '^', 'label': 'SVR'},
        'Transfer': {'color': '#FF99FF', 'marker': 'x', 'label': '迁移学习3'}
    }

    # 绘制预测散点
    for name, model in optimized_models.items():
        settings = model_settings[name]
        if name == 'Transfer':
            # 为迁移模型准备增强特征
            knowledge_features = []

            # 获取所有基础模型的预测
            for base_model in base_models.values():
                pred = base_model.predict(X_val).reshape(-1, 1)
                knowledge_features.append(pred)

            # 添加相似度特征
            knn = NearestNeighbors(n_neighbors=6)
            knn.fit(X_large)
            distances, indices = knn.kneighbors(X_val)
            similar_samples = y_large[indices]
            knowledge_features.append(np.mean(similar_samples, axis=1).reshape(-1, 1))
            similar_samples_mean = np.mean(similar_samples, axis=1).reshape(-1, 1)
            # 添加领域知识特征
            domain_features = np.column_stack([
                X_val[:, important_features_indices],  # 重要特征
                np.mean(X_val[:, important_features_indices], axis=1).reshape(-1, 1),  # 特征均值
                np.std(X_val[:, important_features_indices], axis=1).reshape(-1, 1)  # 特征标准差
            ])

            # 组合所有特征
            X_val_enhanced = np.hstack([X_val] + knowledge_features + [domain_features])
            y_pred = model.predict(X_val_enhanced)
        else:
            y_pred = model.predict(X_val)

        plt.scatter(y_val, y_pred,
                    alpha=0.6,
                    s=60,
                    label=settings['label'],
                    color=settings['color'],
                    marker=settings['marker'])

    plt.title('各模型预测效果比较', fontsize=14)
    plt.xlabel('实际值', fontsize=12)
    plt.ylabel('预测值', fontsize=12)
    plt.grid(True, linestyle='--', alpha=0.3)
    plt.legend(fontsize=10)
    plt.tight_layout()
    plt.savefig('transfer_comparison.png', dpi=300, bbox_inches='tight')
    plt.show()  # 显示图表

    # 在主函数中调用可视化函数
    visualize_knn_predictions(X_large, y_large, X_val, indices, similar_samples_mean)

    # 6. 预测缺失值
    print("\n预测缺失值...")
    # 为预测数据准备增强特征
    knowledge_features = []

    # 获取所有基础模型的预测
    for model in base_models.values():
        pred = model.predict(X_small_predict).reshape(-1, 1)
        knowledge_features.append(pred)

    # 添加相似度特征
    knn = NearestNeighbors(n_neighbors=6)
    knn.fit(X_large)
    distances, indices = knn.kneighbors(X_small_predict)
    similar_samples = y_large[indices]
    knowledge_features.append(np.mean(similar_samples, axis=1).reshape(-1, 1))

    # 添加领域知识特征
    domain_features = np.column_stack([
        X_small_predict[:, important_features_indices],  # 重要特征
        np.mean(X_small_predict[:, important_features_indices], axis=1).reshape(-1, 1),  # 特征均值
        np.std(X_small_predict[:, important_features_indices], axis=1).reshape(-1, 1)  # 特征标准差
    ])

    # 组合所有特征
    X_predict_enhanced = np.hstack([X_small_predict] + knowledge_features + [domain_features])
    predictions = transfer_model.predict(X_predict_enhanced)
    # 将基础模型的预测值、相似度特征和领域知识特征保存到Excel
    base_model_predictions = {}
    for i, model_name in enumerate(base_models.keys()):
        base_model_predictions[model_name] = knowledge_features[i].flatten()

    # 创建一个DataFrame来保存迁移知识
    results = pd.DataFrame({
        'Property ID': df_small[~mask_labeled]['Property ID'],
        'Predicted_Total_Cost': predictions,
        **base_model_predictions,
        'Similarity_Feature': knowledge_features[-1].flatten(),
        'Domain_Important_Features': domain_features[:, :len(important_features_indices)].mean(axis=1),
        'Domain_Mean_Feature': domain_features[:, len(important_features_indices)],
        'Domain_Std_Feature': domain_features[:, len(important_features_indices) + 1]
    })

    # 保存到Excel
    try:
        results.to_excel('small_dataset_transfer_predictions1.xlsx', index=False)
        print("\n预测结果已保存到 small_dataset_transfer_predictions.xlsx")
    except Exception as e:
        print(f"保存预测结果时发生错误: {e}")

    # 保存预测结果
    try:
        results = pd.DataFrame({
            'Property ID': df_small[~mask_labeled]['Property ID'],
            'Predicted_Total_Cost': predictions
        })
        results.to_excel('small_dataset_transfer_predictions.xlsx', index=False)
        print("\n预测结果已保存到 small_dataset_transfer_predictions.xlsx")
    except Exception as e:
        print(f"保存预测结果时发生错误: {e}")

    print("\n模型对比图已保存到 transfer_comparison.png")

    # 7. 绘制特征重要度图
    print("\n绘制特征重要度图...")
    feature_names = df_large.columns[1:].tolist()
    feature_importances = base_models['xgb'].feature_importances_
    plot_feature_importance(important_features_indices, feature_names, feature_importances)


if __name__ == "__main__":
    main()



