import pandas as pd
import numpy as np
from sklearn.model_selection import cross_val_score
from sklearn.ensemble import RandomForestRegressor
from sklearn.preprocessing import StandardScaler
import matplotlib.pyplot as plt

def engineer_features(df):
    """增强特征工程"""
    df = df.copy()

    # 成本相关特征
    if all(col in df.columns for col in ['Initial Capital Cost', 'Mine Sustaining Cost']):
        df['Cost_Ratio'] = df['Initial Capital Cost'] / df['Mine Sustaining Cost']
        df['Total_Initial_Cost'] = df['Initial Capital Cost'] * df['Mill Capacity']

    # 生产效率特征
    if all(col in df.columns for col in ['Mill Capacity', 'Production Capacity']):
        df['Capacity_Efficiency'] = df['Production Capacity'] / df['Mill Capacity']

    # 矿石品位和回收率
    if all(col in df.columns for col in ['MillHead Grade', 'Recovery Rate']):
        df['Grade_Recovery'] = df['MillHead Grade'] * df['Recovery Rate']

    # 经济效益指标
    if 'In-Situ Value' in df.columns:
        df['Value_Per_Ton'] = df['In-Situ Value'] / df['Mill Capacity']

    return df


def train_global_model(df, features):
    """训练全局模型以获取特征重要性"""
    mask = df['Total Cost'].notna()
    X_train = df[mask][features]
    y_train = df[mask]['Total Cost']

    # 使用随机森林作为基础模型
    model = RandomForestRegressor(
        n_estimators=100,
        max_depth=4,
        min_samples_split=5,
        min_samples_leaf=3,
        random_state=42
    )

    if len(X_train) >= 5:
        cv_scores = cross_val_score(model, X_train, y_train, cv=min(5, len(X_train)))
        print(f"全局交叉验证分数: {cv_scores.mean():.4f} (+/- {cv_scores.std() * 2:.4f})")

    model.fit(X_train, y_train)
    return model


def plot_feature_importances(features, importances):
    """绘制特征重要性"""
    indices = np.argsort(importances)[::-1]
    plt.figure(figsize=(12, 8))
    plt.title("Feature Importances")
    plt.bar(range(len(indices)), importances[indices], align="center")
    plt.xticks(range(len(indices)), [features[i] for i in indices], rotation=90)
    plt.xlim([-1, len(indices)])
    plt.show()


def fill_large_dataset():
    """改进的大数据集填充策略"""
    print("读取数据...")
    df_large = pd.read_excel('large_dataset.xlsx')

    # 1. 特征工程
    print("\n进行特征工程...")
    df_enhanced = engineer_features(df_large)

    # 2. 准备特征
    numerical_features = [
        'Initial Capital Cost', 'Mining & Processing Costs', 'Mine Sustaining Cost',
        'Mill Capacity', 'Stripping Ratio', 'MillHead Grade', 'Recovery Rate',
        'Production Capacity', 'Estimated Mine Life', 'In-Situ Value'
    ]
    categorical_features = df_large.columns[1:18].tolist()
    # 添加工程特征
    # engineered_features = [
    #     'Cost_Ratio', 'Total_Initial_Cost', 'Capacity_Efficiency',
    #     'Grade_Recovery', 'Value_Per_Ton'
    # ]

    features = [col for col in numerical_features + categorical_features
                if col in df_enhanced.columns]

    # 3. 标准化特征
    scaler = StandardScaler()
    df_enhanced[features] = scaler.fit_transform(df_enhanced[features])

    # 4. 训练全局模型以获取特征重要性
    print("\n训练全局模型以获取特征重要性...")
    global_model = train_global_model(df_enhanced, features)
    importances = global_model.feature_importances_

    # 5. 绘制特征重要性
    plot_feature_importances(features, importances)

    # 6. 数据分组（基于多个特征）
    print("\n数据分组...")
    df_enhanced['Size_Group'] = pd.qcut(df_enhanced['Mill Capacity'].rank(method='first'), q=3)
    df_enhanced['Cost_Group'] = pd.qcut(df_enhanced['Initial Capital Cost'].rank(method='first'), q=3)

    # 7. 对每个组合训练模型
    group_models = {}
    group_stats = {}

    for size_group in df_enhanced['Size_Group'].unique():
        for cost_group in df_enhanced['Cost_Group'].unique():
            mask = (df_enhanced['Size_Group'] == size_group) & \
                   (df_enhanced['Cost_Group'] == cost_group)
            group_data = df_enhanced[mask]

            if len(group_data) > 0:
                print(f"\n处理组 (Size: {size_group}, Cost: {cost_group})")
                group_key = (size_group, cost_group)

                # 计算组统计信息
                group_stats[group_key] = {
                    'mean': group_data['Total Cost'].mean(),
                    'std': group_data['Total Cost'].std()
                }

                # 训练模型
                group_models[group_key] = train_global_model(group_data, features)

    # 8. 填充预测
    missing_mask = df_large['Total Cost'].isna()
    for idx in df_large[missing_mask].index:
        size_group = df_enhanced.loc[idx, 'Size_Group']
        cost_group = df_enhanced.loc[idx, 'Cost_Group']
        group_key = (size_group, cost_group)

        if group_key in group_models:
            X_predict = df_enhanced.loc[[idx], features]
            prediction = group_models[group_key].predict(X_predict)[0]

            # 验证预测值
            stats = group_stats[group_key]
            prediction = np.clip(
                prediction,
                stats['mean'] - 2 * stats['std'],
                stats['mean'] + 2 * stats['std']
            )

            df_large.loc[idx, 'Total Cost'] = prediction

    # 9. 保存结果
    df_large.to_excel('large_dataset_filled.xlsx', index=False)
    print("\n填充完成，结果已保存到 large_dataset_filled.xlsx")


if __name__ == "__main__":
    fill_large_dataset()



