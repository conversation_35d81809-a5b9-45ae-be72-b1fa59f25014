import pandas as pd

file_path = r"C:\Users\<USER>\Desktop\Cobalt351.xlsx"
data = pd.read_excel(file_path,sheet_name='Sheet1')
df = pd.DataFrame(data)

#print(df.columns)

Low = ["Low", "Moderate"]
Medium = ["High", "Elevated"]
High = ["Very High", "Severe", "Extreme"]
df['Overall Risk Assessment'] = df['Overall Risk Assessment'].replace(Low, 'Low')
df['Overall Risk Assessment'] = df['Overall Risk Assessment'].replace(Medium, 'Medium')
df['Overall Risk Assessment'] = df['Overall Risk Assessment'].replace(High, 'High')
### Political Risk Assessment
df['Political Risk Assessment'] = df['Political Risk Assessment'].replace(Low, 'Low')
df['Political Risk Assessment'] = df['Political Risk Assessment'].replace(Medium, 'Medium')
df['Political Risk Assessment'] = df['Political Risk Assessment'].replace(High, 'High')
# df['Political Risk Assessment'] = df['Political Risk Assessment'].replace({'Low': 0,'Medium': 1,
#                                                                            'High': 2})
###Operational Risk Assessment
df['Operational Risk Assessment'] = df['Operational Risk Assessment'].replace(Low, 'Low')
df['Operational Risk Assessment'] = df['Operational Risk Assessment'].replace(Medium, 'Medium')
df['Operational Risk Assessment'] = df['Operational Risk Assessment'].replace(High, 'High')
# df['Operational Risk Assessment'] = df['Operational Risk Assessment'].replace({'Low': 0,'Medium': 1,
#                                                                                'High': 2})
### Security Risk Assessment
df['Security Risk Assessment'] = df['Security Risk Assessment'].replace(Low, 'Low')
df['Security Risk Assessment'] = df['Security Risk Assessment'].replace(Medium, 'Medium')
df['Security Risk Assessment'] = df['Security Risk Assessment'].replace(High, 'High')
# df['Security Risk Assessment'] = df['Security Risk Assessment'].replace({'Low': 0,'Medium': 1,
#                                                                            'High': 2})
### Legal Risk Assessment
df['Legal Risk Assessment'] = df['Legal Risk Assessment'].replace(Low, 'Low')
df['Legal Risk Assessment'] = df['Legal Risk Assessment'].replace(Medium, 'Medium')
df['Legal Risk Assessment'] = df['Legal Risk Assessment'].replace(High, 'High')
# df['Legal Risk Assessment'] = df['Legal Risk Assessment'].replace({'Low': 0,'Medium': 1,
#                                                                            'High': 2})

### Tax Risk Assessment
df['Tax Risk Assessment'] = df['Tax Risk Assessment'].replace(Low, 'Low')
df['Tax Risk Assessment'] = df['Tax Risk Assessment'].replace(Medium, 'Medium')
df['Tax Risk Assessment'] = df['Tax Risk Assessment'].replace(High, 'High')

### Economic Risk Assessment
df['Economic Risk Assessment'] = df['Economic Risk Assessment'].replace(Low, 'Low')
df['Economic Risk Assessment'] = df['Economic Risk Assessment'].replace(Medium, 'Medium')
df['Economic Risk Assessment'] = df['Economic Risk Assessment'].replace(High, 'High')


### Development Stage
Production = ["Satellite", "Operating", "Expansion","Limited Production","Residual Production"]
Per_Production = ["Preproduction", "Commissioning", "Construction Planned","Construction Started"]
Incentive = ["Feasibility Complete", "Feasibility"]
Undergoing_Feasibility = ["Feasibility Started"]
Resource_Pool = ["Prefeas/Scoping", "Reserves Development", "Exploration","Grassroots","Target Outline","Advanced Exploration"]

df['Development Stage'] = df['Development Stage'].replace(Production, 'Production')
df['Development Stage'] = df['Development Stage'].replace(Per_Production, 'Per Production')
df['Development Stage'] = df['Development Stage'].replace(Incentive, 'Incentive')
df['Development Stage'] = df['Development Stage'].replace(Undergoing_Feasibility, 'Undergoing Feasibility')
df['Development Stage'] = df['Development Stage'].replace(Resource_Pool, 'Resource Pool')

# df['Development Stage'] = df['Development Stage'].replace({'Closed': 0,'Resource Pool': 1, 'Undergoing Feasibility': 2,
#                                                            'Incentive':3, 'Per Production':4, 'Production':5})

### Activity Status
Temporarily_On_Hold = ["Care And Maintenance", "On Hold Awaiting Financing", "On Hold Awaiting Higher Prices"
                       , "Under Litigation", "Rehabilitation", "Temporarily On Hold", "Care And Maintence"]

df['Activity Status'] = df['Activity Status'].replace(Temporarily_On_Hold, 'Temporarily On Hold')

# df['Activity Status'] = df['Activity Status'].replace({'Inactive': 0,'Active': 1, 
#                                                            'Temporarily On Hold': 2})

### Production Forms
# Primary_product = ["Ore", "Fines", "Direct Shipping Ore","Concentrate","Sulfates","Precipitates","Slimes"]
# Intermediate_product = ["Cathodes","Matte","Dore","Hydroxide","Bullion","Blister", "Yellow Cake"]
# Final_product = ["Cement","Flux","Solvent Extract", "Anodes","Alloy","Pellets"]

# df['Production Forms'] = df['Production Forms'].replace(Primary_product, 'Primary product')
# df['Production Forms'] = df['Production Forms'].replace(Intermediate_product, 'Intermediate product')
# df['Production Forms'] = df['Production Forms'].replace(Final_product, 'Final product')

# df['Production Forms'] = df['Production Forms'].replace({'Primary product': 0,'Intermediate product': 1, 
#                                                            'Final product': 2})


### Geologic Ore Body Type
# Iron_Oxide_Copper_Gold = ["Iron Oxide Copper Gold (IOCG)", "IOCG Breccia Complex","Banded Iron Formation (BIF)", "BIF - Algoma Type"
#                           ,"Magmatic Magnetite","Komatiitic Magmatic","Layered Mafic-Ultramafic Intrusion","Alkali Intrusion",
#                           "Granite Related","Flood Basalt (Dyke-Sill Complexes)","Breccia Pipes"]
# Sedimentary_Hosted_Deposits = ["Sediment Hosted (Reduced Facies)", "Shale Hosted", "Black Shale","Sedimentary Exhalative (SEDEX)",
#                                "Sediment Hosted Red Bed","Sedimentary Hosted Stratiform Pb-Zn","Carb-Hosted (Mississippi Valley Type)",
#                                "Unconformity Related","Supergene","Exotic Copper Deposits","Laterite (Generic)","Laterite (Ni-Co - Limonite)",
#                                "Laterite (Ni-Saprolite)"]
# Epithermal_and_Supergene_Deposits = ["Epithermal High Sulphidation", "Epithermal Low Sulphidation", "Mesothermal Lode Gold","Skarn (Metasomatic)",
#                         "Carbote Replacement (incl Manto)","Greisen Related","Vein Hosted","Epithermal", "Saddle Reefs"
#                         ,"Replacement", "Carlin Style Carbote Replacement"]
# Special_Types  = ["Accretiory Sea Floor Nodules", "Placer (Alluvial", "Paleoplacer (Buried","Graphite (Dissemited Flake)",
#                 "Graphite (Microcrystalline)","Diamond-Kimberlitic-Diatreme","Brine (Salar)","Astrobleme",
#                 "Sandstone Hosted Roll Front","Carbotite Hosted","Marine (Ocean)","Intrusive Related","Pegmatite Hosted"]

# df['Geologic Ore Body Type'] = df['Geologic Ore Body Type'].replace(Iron_Oxide_Copper_Gold, 'Iron Oxide Copper Gold')
# df['Geologic Ore Body Type'] = df['Geologic Ore Body Type'].replace(Sedimentary_Hosted_Deposits, 'Sedimentary Hosted Deposits')
# df['Geologic Ore Body Type'] = df['Geologic Ore Body Type'].replace(Epithermal_and_Supergene_Deposits, 'Epithermal and Supergene Deposits')
# df['Geologic Ore Body Type'] = df['Geologic Ore Body Type'].replace(Special_Types, 'Special Types')
# df['Geologic Ore Body Type'] = df['Geologic Ore Body Type'].replace({'Iron Oxide Copper Gold': 0,'Sedimentary Hosted Deposits': 1, 
#                                                                      'Epithermal and Supergene Deposits': 2,'Special Types':3,
#                                                                        'Porphyry Deposit':4, 'Volcanogenic Massive Sulfide (VMS)':5})

### Mine Type
Open_Pit = ["Open Pit", "Cut & Fill Stoping", "Blasthole Open Stoping","Vertical Crater Retreat",
            "Bulk Mining Methods","Quarry","Open Stope","Tailings", "Dump", "Stock Pile","Hydraulic Mining"]
Underground= ["Underground", "Block & Panel Caving", "Room & Pillar","Sublevel Caving",
              "Long Hole Stoping","Shrinkage Stoping","Sublevel Stoping","Ocean","Dredging",
              "Placer","Brine","In-Situ Leach"]

df['Mine Type'] = df['Mine Type'].replace(Open_Pit, 'Open Pit')
df['Mine Type'] = df['Mine Type'].replace(Underground, 'Underground')

# df['Mine Type'] = df['Mine Type'].replace({'Underground': 0,'Open Pit': 1})

# unique_Mining_Method = df['Mining Method'].unique()
# print(unique_Mining_Method)

### Processing Method
Physical = ["Crushing", "Grinding", "Autogenous Grinding","Semi-Autogenous Grinding","Gravity","Shaker Tables",
            "Knelson Concentrator","Dense Media Separation","Magnetic Separation","Ore Sorting","X-Ray Sorting",
            "Sluicing","Ball","Agglomeration", "Washing","Evaporation","Ion Exchange","Brine"]
Chemical = ["Flotation", "Agitation Cyanide Leach", "Cyanide Leach","Heap Leach","Dump Leach","In-Situ Leach",
            "Leach","Acid Leach","Acid Chloride Leach","Bio-Leaching","Bio-Oxidation","Pressure Acid Leaching",
            "Roasting","Oxidation","Solvent-Extraction Electrowinning","Solvent Extraction","Electrowinning",
            "Vat Leach","Cyanidation","Solvent-Extraction Electrowinning", "Hydrometallurgical","Carbon-In-Leach",
            "Carbon-In-Pulp","Carbon Absorption","Zinc Precipitation","Merrill-Crowe Zinc Precipitation",
            "Carbon Adsorption","Smelting", "Flash Smelting", "Autoclaving","Blast Furce","Caustic Fusion"]

df['Processing Method'] = df['Processing Method'].replace(Physical, 'Physical')
df['Processing Method'] = df['Processing Method'].replace(Chemical, 'Chemical')
# df['Processing Method'] = df['Processing Method'].replace({'Physical': 0,'Chemical': 1})


### Ore Minerals
# Copper = ["Chalcopyrite", "Bornite", "Chalcocite","Covellite","Azurite","Malachite"
#         ,"Tenntite","Tetrahedrite","Chrysocolla","Sulfide","Oxide",
#          "Carbote","Chromite","Goethite","Limonite","Barite (Barytes)"]
# Cobalt = ["Cobaltite", "Erythrite","Spodumene","Cassiterite","Gibbsite","Garnierite",
#           "Sperrylite","Pentlandite", "Pyrrhotite","Pyrite","Magnetite","Molybdenite"]
# Mix = ["Sphalerite","Arsenopyrite","Hematite","Siderite","Pyrargyrite"
#        ,"Smithsonite","Argentite","Willemite","Telluride","Hemimorphite"
#        ,"tive","Electrum","Graphite","Silicate","Olivine", "Scheelite","Uraninite (Pitchblende)",
#         "Allanite","Apatite","Mozite","Lepidolite","Florencite","Carllite",
#         "Ergite","Wolframite","Gale","Xenotime","Coffinite"]

# df['Ore Minerals'] = df['Ore Minerals'].replace(Copper, 'Copper')
# df['Ore Minerals'] = df['Ore Minerals'].replace(Cobalt, 'Cobalt')
# df['Ore Minerals'] = df['Ore Minerals'].replace(Mix, 'Mix')
# df['Ore Minerals'] = df['Ore Minerals'].replace({'Copper': 0,'Cobalt': 1,'Mix': 2})


# # 分类变量列表
# categorical_columns = [
#     'Geologic Ore Body Type', 'Ore Minerals', 'Production Forms', 
#     'Mining Method', 'Processing Method'
# ]

# # 遍历每个分类变量并打印每个类型名和数量
# for column in categorical_columns:
#     print(f"Column: {column}")
#     value_counts = df[column].value_counts()
#     print(value_counts)
#     print("\n")


output_path = r"C:\Users\<USER>\Desktop\1Cobalt351.xlsx"
df.to_excel(output_path, index=False)